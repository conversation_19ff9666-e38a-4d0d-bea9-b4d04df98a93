# THIS FILE IS AUTO-GENERATED. DO NOT MODIFY!!

# Copyright 2020-2023 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT

-keep class com.cnsprowms.wms.* {
  native <methods>;
}

-keep class com.cnsprowms.wms.WryActivity {
  public <init>(...);

  void setWebView(com.cnsprowms.wms.RustWebView);
  java.lang.Class getAppClass(...);
  java.lang.String getVersion();
}

-keep class com.cnsprowms.wms.Ipc {
  public <init>(...);

  @android.webkit.JavascriptInterface public <methods>;
}

-keep class com.cnsprowms.wms.RustWebView {
  public <init>(...);

  void loadUrlMainThread(...);
  void loadHTMLMainThread(...);
  void evalScript(...);
}

-keep class com.cnsprowms.wms.RustWebChromeClient,com.cnsprowms.wms.RustWebViewClient {
  public <init>(...);
}
