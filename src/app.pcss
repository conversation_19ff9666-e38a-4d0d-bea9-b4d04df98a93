/* Write your global styles here, in PostCSS syntax */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
    .pt-safe {
        padding-top: env(safe-area-inset-top);
    }

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        padding-top: env(safe-area-inset-top);
        padding-bottom: env(safe-area-inset-bottom);
        height: 100vh;
    }
}

/* Customization  */
@layer components {
    .main-section {
        @apply px-1.5 py-3;
    }

    /* 다크모드 지원 스타일 */
    .scan-container {
        @apply bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden;
    }

    .scan-header {
        @apply bg-gradient-to-r from-blue-500 to-blue-600 dark:from-blue-600 dark:to-blue-700 text-white px-6 py-4;
    }

    .scan-content {
        @apply p-4 space-y-1;
    }

    .scan-input-group {
        @apply flex gap-3 items-center;
    }

    .scan-input {
        @apply flex-1 px-4 py-3 text-lg font-mono bg-gray-50 dark:bg-gray-700 border-2 border-gray-300 dark:border-gray-600 rounded-lg focus:border-blue-500 dark:focus:border-blue-400 focus:outline-none transition-colors duration-200 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400;
    }

    .scan-button {
        @apply px-6 py-3 bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800;
    }

    .product-info {
        @apply bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 overflow-hidden;
    }

    .product-info-row {
        @apply flex border-b border-gray-200 dark:border-gray-600 last:border-b-0;
    }

    .product-info-label {
        @apply w-1/4 px-4 py-2 bg-gray-100 dark:bg-gray-600 text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center justify-center;
    }

    .product-info-value {
        @apply flex-1 px-4 py-2 text-gray-900 dark:text-gray-100 flex items-center;
    }

    .product-info-select {
        @apply w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-gray-100 focus:border-blue-500 dark:focus:border-blue-400 focus:outline-none transition-colors duration-200;
    }

    .product-info-textarea {
        @apply w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-gray-100 focus:border-blue-500 dark:focus:border-blue-400 focus:outline-none transition-colors duration-200 resize-none;
    }

    .action-buttons {
        @apply flex gap-4 pt-4;
    }

    .action-button {
        @apply flex-1 py-3 px-6 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800;
    }

    .action-button-success {
        @apply bg-green-500 hover:bg-green-600 dark:bg-green-600 dark:hover:bg-green-700 text-white focus:ring-green-500;
    }

    .action-button-default {
        @apply bg-gray-500 hover:bg-gray-600 dark:bg-gray-600 dark:hover:bg-gray-700 text-white focus:ring-gray-500;
    }

    .action-button-neutral {
        @apply bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 focus:ring-gray-500;
    }

    .parts-section {
        @apply bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 p-4;
    }

    .parts-header {
        @apply text-sm font-bold text-gray-700 dark:text-gray-300 mb-3;
    }

    .parts-list {
        @apply space-y-2;
    }

    .parts-item {
        @apply flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600;
    }

    .parts-item-text {
        @apply text-gray-900 dark:text-gray-100;
    }

    .parts-delete-button {
        @apply btn btn-xs btn-error;
    }

    .checkbox-container {
        @apply flex items-center gap-2 mt-3;
    }

    .checkbox-input {
        @apply checkbox checkbox-primary;
    }

    .checkbox-label {
        @apply font-bold text-error cursor-pointer;
    }

    /* 구성품 추가 섹션 (상품 정보 내부) */
    .parts-controls {
        @apply w-full;
    }

    .parts-add-button {
        @apply px-4 py-2 bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800;
    }

    .parts-empty {
        @apply text-center py-3 text-gray-500 dark:text-gray-400 text-sm;
    }

    /* 팔레트 표시 관련 스타일 */
    .pallet-display {
        @apply text-center py-4;
    }

    .pallet-code {
        @apply text-4xl font-bold text-gray-900 dark:text-gray-100;
    }

    .pallet-grade {
        @apply text-4xl font-bold text-red-600 dark:text-red-400 mt-2;
    }

    .pallet-warning {
        @apply text-center py-4 text-2xl text-red-600 dark:text-red-400 font-bold;
    }

    .product-info-input {
        @apply w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-gray-100 focus:border-blue-500 dark:focus:border-blue-400 focus:outline-none transition-colors duration-200 text-right;
    }

    /* Print Barcode */
    #barcode {
        width: 35mm;
        height: 12mm;
        margin: auto;
    }

    #barcode_canvas {
        width: 51mm;
        height: 40mm;
        @apply px-1 flex flex-col justify-center items-center bg-white font-bold;
    }

    @media print {
        html, body {
            -webkit-print-color-adjust: exact;
            width: 51mm;
            height: 40mm;
        }

        body * {
            @apply invisible;
        }

        #barcode_canvas,
        #barcode_canvas * {
            @apply visible;
        }

        #barcode_canvas {
            @apply absolute inset-x-0 inset-y-0;
        }
    }

    /* 모던 카드 스타일 (점검완료 후 적재위치, 최근적재 리스트 등 공통) */
    .modern-card {
        @apply rounded-xl shadow-lg bg-base-100 dark:bg-base-200 border border-base-200 dark:border-base-300 mb-4;
    }

    .modern-card-header {
        /* 그라데이션 헤더, 다크모드 대응 */
        @apply rounded-t-xl px-4 py-2 font-bold text-base-content;
        background: linear-gradient(to right, hsl(var(--p) / 0.8), hsl(var(--s) / 0.6));
    }

    .dark .modern-card-header {
        background: linear-gradient(to right, hsl(var(--p) / 0.6), hsl(var(--s) / 0.4));
    }

    .modern-card-body {
        @apply px-4 py-3 bg-base-100 dark:bg-base-200 rounded-b-xl;
    }

    /* 모던 테이블 스타일 (카드 내부 테이블) */
    .modern-table {
        @apply table-auto w-full text-left bg-transparent;
    }

    .modern-th {
        @apply bg-base-200 text-base-content font-semibold px-3 py-2 border-b border-base-300;
        background-color: hsl(var(--b2));
        border-bottom-color: hsl(var(--b3));
    }

    .modern-td {
        @apply px-3 py-2 border-b border-base-200 text-base-content;
        border-bottom-color: hsl(var(--b2));
    }

    /* 다크모드 테이블 스타일 */
    .dark .modern-th {
        background-color: hsl(var(--b3));
        border-bottom-color: hsl(var(--b4));
    }

    .dark .modern-td {
        border-bottom-color: hsl(var(--b3));
    }

    /* 다크모드에서 카드 그림자 강조 */
    .dark .modern-card {
        box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.45);
    }

    /* 테마별 테이블 헤더 그라디언트 스타일 */
    .table-header-gradient {
        /* 기본 라이트 테마 */
        background: linear-gradient(to right, #374151, #6b7280);
        border-bottom: 2px solid #1f2937;
        @apply shadow-lg text-white text-center h-[50px];
    }

    /* 다크 테마 */
    [data-theme="dark"] .table-header-gradient {
        background: linear-gradient(to right, #374151, #6b7280);
        border-bottom: 2px solid #111827;
        @apply shadow-lg text-white text-center h-[50px];
    }

    /* 기타 테마별 스타일링 */
    [data-theme="cupcake"] .table-header-gradient {
        background: linear-gradient(to right, #faf7f5, #f0e6e3);
        color: #291334;
        border-bottom-color: #e6d6d3;
    }

    [data-theme="bumblebee"] .table-header-gradient {
        background: linear-gradient(to right, #f4d03f, #f39c12);
        color: #2c1810;
        border-bottom-color: #d68910;
    }

    [data-theme="emerald"] .table-header-gradient {
        background: linear-gradient(to right, #10b981, #059669);
        color: white;
        border-bottom-color: #047857;
    }

    [data-theme="corporate"] .table-header-gradient {
        background: linear-gradient(to right, #4b6bfb, #3b5bdb);
        color: white;
        border-bottom-color: #2e4a9e;
    }

    [data-theme="synthwave"] .table-header-gradient {
        background: linear-gradient(to right, #e779c1, #f972ce);
        color: #291d4d;
        border-bottom-color: #d946ef;
    }

    [data-theme="retro"] .table-header-gradient {
        background: linear-gradient(to right, #ef9995, #e779c1);
        color: #2a1f1d;
        border-bottom-color: #d946ef;
    }

    [data-theme="cyberpunk"] .table-header-gradient {
        background: linear-gradient(to right, #ff7598, #f972ce);
        color: #1a1a1a;
        border-bottom-color: #d946ef;
    }

    [data-theme="valentine"] .table-header-gradient {
        background: linear-gradient(to right, #e96d7b, #f972ce);
        color: #2a1f1d;
        border-bottom-color: #d946ef;
    }

    [data-theme="halloween"] .table-header-gradient {
        background: linear-gradient(to right, #f28c18, #ff6b35);
        color: #1a1a1a;
        border-bottom-color: #e65c00;
    }

    [data-theme="garden"] .table-header-gradient {
        background: linear-gradient(to right, #4ade80, #22c55e);
        color: #1a1a1a;
        border-bottom-color: #16a34a;
    }

    [data-theme="forest"] .table-header-gradient {
        background: linear-gradient(to right, #059669, #047857);
        color: white;
        border-bottom-color: #065f46;
    }

    [data-theme="aqua"] .table-header-gradient {
        background: linear-gradient(to right, #06b6d4, #0891b2);
        color: white;
        border-bottom-color: #0e7490;
    }

    [data-theme="lofi"] .table-header-gradient {
        background: linear-gradient(to right, #0a0a0a, #1a1a1a);
        color: #ffffff;
        border-bottom-color: #000000;
    }

    [data-theme="pastel"] .table-header-gradient {
        background: linear-gradient(to right, #ffb3ba, #ffdfba);
        color: #2a1f1d;
        border-bottom-color: #ffb3ba;
    }

    [data-theme="fantasy"] .table-header-gradient {
        background: linear-gradient(to right, #6d3a9c, #8b5cf6);
        color: white;
        border-bottom-color: #5b21b6;
    }

    [data-theme="wireframe"] .table-header-gradient {
        background: linear-gradient(to right, #b8b8b8, #a8a8a8);
        color: #000000;
        border-bottom-color: #787878;
    }

    [data-theme="black"] .table-header-gradient {
        background: linear-gradient(to right, #2a2a2a, #4a4a4a);
        color: #ffffff;
        border-bottom-color: #1a1a1a;
    }

    [data-theme="luxury"] .table-header-gradient {
        background: linear-gradient(to right, #1a1a1a, #2a2a2a);
        color: #d4af37;
        border-bottom-color: #000000;
    }

    [data-theme="dracula"] .table-header-gradient {
        background: linear-gradient(to right, #ff79c6, #bd93f9);
        color: #282a36;
        border-bottom-color: #6272a4;
    }

    [data-theme="cmyk"] .table-header-gradient {
        background: linear-gradient(to right, #45aeee, #f96aad);
        color: #1a1a1a;
        border-bottom-color: #e91e63;
    }

    [data-theme="autumn"] .table-header-gradient {
        background: linear-gradient(to right, #581c87, #7c3aed);
        color: white;
        border-bottom-color: #4c1d95;
    }

    [data-theme="business"] .table-header-gradient {
        background: linear-gradient(to right, #1d4ed8, #2563eb);
        color: white;
        border-bottom-color: #1e40af;
    }

    [data-theme="acid"] .table-header-gradient {
        background: linear-gradient(to right, #fbbf24, #f59e0b);
        color: #1a1a1a;
        border-bottom-color: #d97706;
    }

    [data-theme="lemonade"] .table-header-gradient {
        background: linear-gradient(to right, #fbbf24, #f59e0b);
        color: #1a1a1a;
        border-bottom-color: #d97706;
    }

    [data-theme="night"] .table-header-gradient {
        background: linear-gradient(to right, #1e293b, #334155);
        color: #f1f5f9;
        border-bottom-color: #0f172a;
    }

    [data-theme="coffee"] .table-header-gradient {
        background: linear-gradient(to right, #8b4513, #a0522d);
        color: white;
        border-bottom-color: #654321;
    }

    [data-theme="winter"] .table-header-gradient {
        background: linear-gradient(to right, #e2e8f0, #cbd5e1);
        color: #1e293b;
        border-bottom-color: #94a3b8;
    }

    /* 커스텀 모달 스타일 - 라이트 모드 */
    .custom-modal {
        background: linear-gradient(135deg, #ffffff 0%, #f3f4f6 100%);
        border: 2px solid #d1d5db;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    .custom-modal.modal-error {
        border-color: #dc2626;
        background: linear-gradient(135deg, #ffffff 0%, #fef2f2 100%);
        box-shadow: 0 10px 25px rgba(220, 38, 38, 0.2);
    }

    .custom-modal.modal-warning {
        border-color: #d97706;
        background: linear-gradient(135deg, #ffffff 0%, #fffbeb 100%);
        box-shadow: 0 10px 25px rgba(217, 119, 6, 0.2);
    }

    .custom-modal.modal-success {
        border-color: #059669;
        background: linear-gradient(135deg, #ffffff 0%, #f0fdf4 100%);
        box-shadow: 0 10px 25px rgba(5, 150, 105, 0.2);
    }

    .modal-title {
        color: #111827;
        text-shadow: none;
    }

    .modal-message {
        color: #374151;
        line-height: 1.6;
        text-shadow: none;
    }

    .modal-button {
        font-weight: 600;
        text-shadow: none;
        transition: all 0.2s ease-in-out;
    }

    .modal-button:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    /* 다크 모드 스타일 */
    .dark .custom-modal {
        background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
        border: 2px solid #4b5563;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
    }

    .dark .custom-modal.modal-error {
        border-color: #dc2626;
        background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
        box-shadow: 0 10px 25px rgba(220, 38, 38, 0.3);
    }

    .dark .custom-modal.modal-warning {
        border-color: #d97706;
        background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
        box-shadow: 0 10px 25px rgba(217, 119, 6, 0.3);
    }

    .dark .custom-modal.modal-success {
        border-color: #059669;
        background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
        box-shadow: 0 10px 25px rgba(5, 150, 105, 0.3);
    }

    .dark .modal-title {
        color: #f9fafb;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    }

    .dark .modal-message {
        color: #e5e7eb;
        line-height: 1.6;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .dark .modal-button {
        font-weight: 600;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        transition: all 0.2s ease-in-out;
    }

    .dark .modal-button:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }

    /* 사이드바 스크롤바 스타일 */
    .scrollbar-thin {
        scrollbar-width: thin;
    }

    .scrollbar-thin::-webkit-scrollbar {
        width: 6px;
    }

    .scrollbar-thin::-webkit-scrollbar-track {
        background: hsl(var(--b1));
    }

    .scrollbar-thin::-webkit-scrollbar-thumb {
        background: hsl(var(--b3));
        border-radius: 3px;
    }

    .scrollbar-thin::-webkit-scrollbar-thumb:hover {
        background: hsl(var(--b4));
    }

    /* TopNav - Dropdown Submenu */
    .top-nav-submenu {
        @apply menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-300 rounded-box w-44 border border-base-300;
    }

    /* 드롭다운 메뉴 z-index 강화 */
    .dropdown-content {
        z-index: 9999 !important;
    }

    /* 테마 선택 드롭다운 특별 처리 */
    .dropdown .dropdown-content {
        z-index: 9999 !important;
    }
}