/**
 * 안드로이드 환경 JWT 인증 시스템 통합 테스트
 *
 * 이 테스트는 안드로이드 환경에서 JWT 인증 플로우 전체를 검증합니다:
 * - Android Keystore 기반 토큰 저장소 동작 확인
 * - JWT 인증 플로우 (로그인, 토큰 갱신, 로그아웃) 테스트
 * - 안드로이드에서 제외된 플러그인 정상 처리 확인
 * - 플랫폼 감지 및 저장소 선택 로직 테스트
 */

import { describe, it, expect, beforeEach, afterEach, vi, beforeAll, afterAll } from 'vitest';

// JWT 유틸리티 함수들
import {
	isTokenValid,
	isTokenExpired,
	getTokenRemainingTime,
	getUserIdFromToken,
	debugToken
} from '$lib/utils/jwtUtils';

// 테스트용 모킹
import type { User } from '$lib/User';

// Tauri API 모킹 (안드로이드에서는 사용되지 않음)
const mockTauriStore = {
	get: vi.fn(),
	set: vi.fn(),
	save: vi.fn(),
	load: vi.fn(),
	clear: vi.fn(),
	delete: vi.fn(),
	entries: vi.fn(),
	keys: vi.fn(),
	values: vi.fn(),
	length: vi.fn(),
	reset: vi.fn()
};

// Tauri 환경 모킹 (안드로이드에서는 Store가 사용되지 않음)
vi.mock('@tauri-apps/plugin-store', () => ({
	Store: vi.fn().mockImplementation(() => mockTauriStore)
}));

// 플랫폼 서비스 모킹 (안드로이드 환경으로 설정)
vi.mock('$lib/services/platformService', async (importOriginal) => {
	const actual = await importOriginal();
	return {
		...actual,
		getCurrentPlatform: vi.fn().mockReturnValue('android'),
		isDesktop: vi.fn().mockReturnValue(false),
		isTauri: vi.fn().mockReturnValue(true),
		isAndroid: vi.fn().mockReturnValue(true),
		isIOS: vi.fn().mockReturnValue(false),
		getStorageType: vi.fn().mockReturnValue('memory'), // 안드로이드에서는 메모리 저장소 사용
		debugPlatform: vi.fn()
	};
});

// HTTP 클라이언트 모킹
const mockAxios = {
	post: vi.fn(),
	get: vi.fn(),
	put: vi.fn(),
	delete: vi.fn(),
	interceptors: {
		request: { use: vi.fn() },
		response: { use: vi.fn() }
	}
};

vi.mock('$lib/services/AxiosBackend', () => ({
	authClient: mockAxios
}));

// 네비게이션 모킹
vi.mock('$app/navigation', () => ({
	goto: vi.fn()
}));

// 환경 모킹
vi.mock('$app/environment', () => ({
	browser: true,
	dev: true
}));

describe('안드로이드 환경 JWT 인증 시스템 통합 테스트', () => {
	// 테스트용 데이터
	const testUser: User = {
		id: 1,
		username: 'testuser',
		name: '테스트 사용자',
		email: '<EMAIL>',
		role: 'user',
		department: '테스트부서',
		position: '테스트직급',
		phone: '010-1234-5678',
		created_at: '2024-01-01T00:00:00Z',
		updated_at: '2024-01-01T00:00:00Z'
	};

	const validAccessToken =
		'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxIiwiaWF0IjoxNzA0MDY3MjAwLCJleHAiOjk5OTk5OTk5OTksInR5cGUiOiJhY2Nlc3MifQ.valid-access-token';
	const expiredAccessToken =
		'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxIiwiaWF0IjoxNzA0MDY3MjAwLCJleHAiOjE3MDQwNjcyMDEsInR5cGUiOiJhY2Nlc3MifQ.expired-access-token';
	const validRefreshToken =
		'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxIiwiaWF0IjoxNzA0MDY3MjAwLCJleHAiOjk5OTk5OTk5OTksInR5cGUiOiJyZWZyZXNoIn0.valid-refresh-token';

	// 동적 import를 위한 변수들
	let getCurrentPlatform: any;
	let isAndroid: any;
	let isTauri: any;
	let getTokenStorage: any;
	let tokenService: any;

	beforeAll(async () => {
		// 전역 설정
		global.console.log = vi.fn();
		global.console.error = vi.fn();
		global.console.warn = vi.fn();

		// 동적 import로 서비스들 로드
		const platformServiceModule = await import('$lib/services/platformService');
		getCurrentPlatform = platformServiceModule.getCurrentPlatform;
		isAndroid = platformServiceModule.isAndroid;
		isTauri = platformServiceModule.isTauri;

		const tokenStorageModule = await import('$lib/services/tokenStorage');
		getTokenStorage = tokenStorageModule.getTokenStorage;

		const tokenServiceModule = await import('$lib/services/tokenService');
		tokenService = tokenServiceModule.tokenService;
	});

	beforeEach(() => {
		vi.clearAllMocks();

		// HTTP 클라이언트 모킹 초기화
		mockAxios.post.mockReset();
		mockAxios.get.mockReset();
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	afterAll(() => {
		vi.restoreAllMocks();
	});

	describe('안드로이드 플랫폼 감지', () => {
		it('안드로이드 플랫폼을 올바르게 감지해야 함', () => {
			expect(getCurrentPlatform()).toBe('android');
			expect(isAndroid()).toBe(true);
			expect(isTauri()).toBe(true);
		});

		it('안드로이드에서는 메모리 저장소를 사용해야 함', async () => {
			const storage = getTokenStorage();
			await storage.initialize();

			// 안드로이드에서는 메모리 저장소가 사용되어야 함
			expect(storage).toBeDefined();
			expect(typeof storage.storeTokens).toBe('function');
			expect(typeof storage.getAccessToken).toBe('function');
			expect(typeof storage.getRefreshToken).toBe('function');
			expect(typeof storage.clearTokens).toBe('function');

			// 초기화 로그 확인
			expect(console.log).toHaveBeenCalledWith(
				'[Memory Only] 토큰 저장소 초기화 완료 (메모리 전용)'
			);
		});
	});

	describe('안드로이드에서 제외된 플러그인 처리', () => {
		it('데스크탑 전용 플러그인이 안드로이드에서 제외되어야 함', () => {
			// 안드로이드 환경에서는 데스크탑 전용 플러그인들이 로드되지 않아야 함
			// 이는 Rust 코드의 조건부 컴파일로 처리됨

			// 플랫폼 감지가 올바르게 동작하는지 확인
			expect(getCurrentPlatform()).toBe('android');
			expect(isAndroid()).toBe(true);

			// 안드로이드에서는 Tauri Store 대신 메모리 저장소 사용
			const { getStorageType } = require('$lib/services/platformService');
			expect(getStorageType()).toBe('memory');
		});

		it('안드로이드에서 Tauri Store API가 호출되지 않아야 함', async () => {
			const storage = getTokenStorage();
			await storage.initialize();

			// 토큰 저장
			await storage.storeTokens(validAccessToken, validRefreshToken);

			// 안드로이드에서는 Tauri Store가 호출되지 않아야 함
			expect(mockTauriStore.set).not.toHaveBeenCalled();
			expect(mockTauriStore.save).not.toHaveBeenCalled();

			// 메모리 저장소 사용 확인
			const retrievedAccessToken = await storage.getAccessToken();
			const retrievedRefreshToken = await storage.getRefreshToken();

			expect(retrievedAccessToken).toBe(validAccessToken);
			expect(retrievedRefreshToken).toBe(validRefreshToken);
		});

		it('안드로이드에서 프린터 플러그인이 사용되지 않아야 함', () => {
			// 안드로이드에서는 프린터 플러그인이 조건부 컴파일로 제외됨
			// 이는 Rust 코드에서 처리되므로 여기서는 플랫폼 감지만 확인
			expect(isAndroid()).toBe(true);
			expect(getCurrentPlatform()).toBe('android');
		});

		it('안드로이드에서 업데이터 플러그인이 사용되지 않아야 함', () => {
			// 안드로이드에서는 업데이터 플러그인이 조건부 컴파일로 제외됨
			// 이는 Rust 코드에서 처리되므로 여기서는 플랫폼 감지만 확인
			expect(isAndroid()).toBe(true);
			expect(getCurrentPlatform()).toBe('android');
		});
	});

	describe('안드로이드 메모리 저장소 동작', () => {
		let tokenStorage: any;

		beforeEach(async () => {
			tokenStorage = getTokenStorage();
			await tokenStorage.initialize();
		});

		it('메모리 저장소가 초기화되어야 함', () => {
			expect(tokenStorage).toBeDefined();
			expect(typeof tokenStorage.storeTokens).toBe('function');
			expect(typeof tokenStorage.getAccessToken).toBe('function');
			expect(typeof tokenStorage.getRefreshToken).toBe('function');
			expect(typeof tokenStorage.clearTokens).toBe('function');
		});

		it('토큰을 메모리에 저장하고 조회할 수 있어야 함', async () => {
			// 토큰 저장
			await tokenStorage.storeTokens(validAccessToken, validRefreshToken);

			// 토큰 조회
			const retrievedAccessToken = await tokenStorage.getAccessToken();
			const retrievedRefreshToken = await tokenStorage.getRefreshToken();

			expect(retrievedAccessToken).toBe(validAccessToken);
			expect(retrievedRefreshToken).toBe(validRefreshToken);

			// Tauri Store는 호출되지 않아야 함
			expect(mockTauriStore.set).not.toHaveBeenCalled();
		});

		it('만료된 토큰을 자동으로 제거해야 함', async () => {
			// 만료된 토큰 저장
			await tokenStorage.storeTokens(expiredAccessToken, validRefreshToken);

			// 만료된 액세스 토큰 조회 시 null 반환되어야 함
			const retrievedAccessToken = await tokenStorage.getAccessToken();
			expect(retrievedAccessToken).toBeNull();

			// 유효한 리프레시 토큰은 그대로 반환되어야 함
			const retrievedRefreshToken = await tokenStorage.getRefreshToken();
			expect(retrievedRefreshToken).toBe(validRefreshToken);
		});

		it('토큰을 완전히 삭제할 수 있어야 함', async () => {
			// 토큰 저장
			await tokenStorage.storeTokens(validAccessToken, validRefreshToken);

			// 토큰 삭제
			await tokenStorage.clearTokens();

			// 토큰이 삭제되었는지 확인
			const retrievedAccessToken = await tokenStorage.getAccessToken();
			const retrievedRefreshToken = await tokenStorage.getRefreshToken();

			expect(retrievedAccessToken).toBeNull();
			expect(retrievedRefreshToken).toBeNull();

			// Tauri Store는 호출되지 않아야 함
			expect(mockTauriStore.delete).not.toHaveBeenCalled();
		});
	});

	describe('안드로이드 JWT 인증 플로우', () => {
		let authActions: any;
		let authState: any;

		beforeEach(async () => {
			// 동적으로 인증 스토어 로드
			const authStoreModule = await import('$lib/stores/authStore');
			authActions = authStoreModule.authActions;
			authState = authStoreModule.authState;

			// 인증 상태 초기화
			authState.set({
				isAuthenticated: false,
				isInitialized: false,
				isLoading: false,
				user: null,
				accessToken: null,
				refreshToken: null,
				tokenExpiresAt: null,
				error: null,
				debugInfo: {
					lastTokenRefresh: null,
					tokenRefreshCount: 0,
					platform: 'android'
				}
			});

			await authActions.initialize();
		});

		it('안드로이드에서 로그인 플로우가 동작해야 함', async () => {
			// 로그인 API 응답 모킹
			mockAxios.post.mockResolvedValueOnce({
				data: {
					data: {
						tokens: {
							access_token: validAccessToken,
							refresh_token: validRefreshToken,
							token_type: 'Bearer',
							expires_in: 900,
							user: testUser
						},
						user: testUser
					}
				}
			});

			// 로그인 실행
			await authActions.login({
				username: 'testuser',
				password: 'password123'
			});

			// API 호출 확인
			expect(mockAxios.post).toHaveBeenCalledWith('/api/auth/login', {
				username: 'testuser',
				password: 'password123'
			});

			// 인증 상태 확인
			const { get } = await import('svelte/store');
			const state = get(authState);
			expect(state.isAuthenticated).toBe(true);
			expect(state.user).toEqual(testUser);
			expect(state.accessToken).toBe(validAccessToken);
			expect(state.refreshToken).toBe(validRefreshToken);

			// Tauri Store는 호출되지 않아야 함 (메모리 저장소 사용)
			expect(mockTauriStore.set).not.toHaveBeenCalled();
		});

		it('안드로이드에서 토큰 갱신이 동작해야 함', async () => {
			// 기존 리프레시 토큰 설정
			await tokenService.storeTokens('old-access-token', validRefreshToken);

			// 토큰 갱신 API 응답 모킹
			const newTokenResponse = {
				access_token: 'new-access-token',
				refresh_token: 'new-refresh-token',
				token_type: 'Bearer',
				expires_in: 900
			};

			mockAxios.post.mockResolvedValueOnce({
				data: newTokenResponse
			});

			// 토큰 갱신 실행
			const result = await authActions.refreshAuth();

			expect(result).toBe(true);
			expect(mockAxios.post).toHaveBeenCalledWith('/api/auth/refresh', {
				refresh_token: validRefreshToken
			});

			// 새 토큰이 메모리에 저장되었는지 확인
			const newAccessToken = await tokenService.getAccessToken();
			const newRefreshToken = await tokenService.getRefreshToken();

			expect(newAccessToken).toBe('new-access-token');
			expect(newRefreshToken).toBe('new-refresh-token');

			// Tauri Store는 호출되지 않아야 함
			expect(mockTauriStore.set).not.toHaveBeenCalled();
		});

		it('안드로이드에서 로그아웃이 동작해야 함', async () => {
			// 초기 인증 상태 설정
			await tokenService.storeTokens(validAccessToken, validRefreshToken);
			authState.update((state: any) => ({
				...state,
				isAuthenticated: true,
				user: testUser,
				accessToken: validAccessToken,
				refreshToken: validRefreshToken
			}));

			// 로그아웃 API 응답 모킹
			mockAxios.post.mockResolvedValueOnce({ data: { message: '로그아웃 성공' } });

			// 로그아웃 실행
			await authActions.logout();

			// 서버 로그아웃 API 호출 확인
			expect(mockAxios.post).toHaveBeenCalledWith('/api/auth/logout');

			// 토큰이 메모리에서 삭제되었는지 확인
			const accessToken = await tokenService.getAccessToken();
			const refreshToken = await tokenService.getRefreshToken();

			expect(accessToken).toBeNull();
			expect(refreshToken).toBeNull();

			// 인증 상태 초기화 확인
			const { get } = await import('svelte/store');
			const state = get(authState);
			expect(state.isAuthenticated).toBe(false);
			expect(state.user).toBeNull();
			expect(state.accessToken).toBeNull();
			expect(state.refreshToken).toBeNull();

			// Tauri Store는 호출되지 않아야 함
			expect(mockTauriStore.delete).not.toHaveBeenCalled();
		});
	});

	describe('안드로이드 성능 및 메모리 관리', () => {
		let tokenStorage: any;

		beforeEach(async () => {
			tokenStorage = getTokenStorage();
			await tokenStorage.initialize();
		});

		it('안드로이드에서 메모리 저장소 접근이 효율적이어야 함', async () => {
			const startTime = Date.now();

			// 100회 토큰 저장/조회 테스트
			for (let i = 0; i < 100; i++) {
				await tokenStorage.storeTokens(`access-${i}`, `refresh-${i}`);
				await tokenStorage.getAccessToken();
				await tokenStorage.getRefreshToken();
			}

			const endTime = Date.now();
			const processingTime = endTime - startTime;

			console.log(`안드로이드 100회 토큰 저장/조회 시간: ${processingTime}ms`);

			// 메모리 저장소는 매우 빨라야 함
			expect(processingTime).toBeLessThan(100); // 100ms 이내
		});

		it('안드로이드에서 메모리 누수가 없어야 함', async () => {
			// 반복적인 토큰 저장/삭제
			for (let i = 0; i < 50; i++) {
				await tokenStorage.storeTokens(`access-${i}`, `refresh-${i}`);
				await tokenStorage.clearTokens();
			}

			// 최종 상태 확인
			const accessToken = await tokenStorage.getAccessToken();
			const refreshToken = await tokenStorage.getRefreshToken();

			expect(accessToken).toBeNull();
			expect(refreshToken).toBeNull();
		});

		it('JWT 토큰 유효성 검사가 안드로이드에서도 효율적이어야 함', () => {
			const startTime = Date.now();

			// 1000회 토큰 유효성 검사
			for (let i = 0; i < 1000; i++) {
				isTokenValid(validAccessToken);
				isTokenExpired(validAccessToken);
				getTokenRemainingTime(validAccessToken);
				getUserIdFromToken(validAccessToken);
			}

			const endTime = Date.now();
			const processingTime = endTime - startTime;

			console.log(`안드로이드 1000회 JWT 유효성 검사 시간: ${processingTime}ms`);

			// JWT 파싱은 플랫폼에 관계없이 빨라야 함
			expect(processingTime).toBeLessThan(100); // 100ms 이내
		});
	});

	describe('안드로이드 에러 처리', () => {
		let tokenStorage: any;

		beforeEach(async () => {
			tokenStorage = getTokenStorage();
			await tokenStorage.initialize();
		});

		it('안드로이드에서 메모리 저장소 오류를 안전하게 처리해야 함', async () => {
			// 메모리 저장소는 일반적으로 오류가 발생하지 않지만,
			// 예외적인 상황을 시뮬레이션

			// 정상적인 동작 확인
			await tokenStorage.storeTokens(validAccessToken, validRefreshToken);

			const accessToken = await tokenStorage.getAccessToken();
			const refreshToken = await tokenStorage.getRefreshToken();

			expect(accessToken).toBe(validAccessToken);
			expect(refreshToken).toBe(validRefreshToken);
		});

		it('안드로이드에서 네트워크 오류를 적절히 처리해야 함', async () => {
			const authStoreModule = await import('$lib/stores/authStore');
			const authActions = authStoreModule.authActions;

			// 네트워크 오류 모킹
			mockAxios.post.mockRejectedValueOnce({
				code: 'NETWORK_ERROR',
				message: 'Network Error'
			});

			// 로그인 시도
			await expect(
				authActions.login({
					username: 'testuser',
					password: 'password123'
				})
			).rejects.toThrow();

			// 에러 상태 확인
			const { get } = await import('svelte/store');
			const authState = authStoreModule.authState;
			const state = get(authState);
			expect(state.error).toBeDefined();
			expect(state.error?.type).toBe('NETWORK_ERROR');
			expect(state.isAuthenticated).toBe(false);
		});
	});

	describe('안드로이드 디버그 및 개발 도구', () => {
		it('안드로이드에서 디버그 정보를 제공해야 함', async () => {
			const authStoreModule = await import('$lib/stores/authStore');
			const authActions = authStoreModule.authActions;

			await authActions.initialize();

			// 디버그 정보 출력
			await authActions.debugAuth();
			await tokenService.debugTokens();

			// 콘솔 로그가 출력되었는지 확인
			expect(console.log).toHaveBeenCalledWith(
				expect.stringContaining('[Auth Store Debug]'),
				expect.any(Object)
			);
		});

		it('안드로이드에서 플랫폼 정보가 올바르게 표시되어야 함', async () => {
			await tokenService.initialize();
			const status = await tokenService.getTokenStatus();

			// 플랫폼 정보 확인
			expect(getCurrentPlatform()).toBe('android');
			expect(isAndroid()).toBe(true);

			// 토큰 상태 정보 확인
			expect(status).toMatchObject({
				hasAccessToken: false,
				hasRefreshToken: false,
				isAccessTokenValid: false,
				isRefreshTokenValid: false,
				userId: null
			});
		});
	});

	describe('안드로이드 크로스 플랫폼 호환성', () => {
		it('안드로이드와 데스크탑에서 동일한 JWT 토큰을 처리할 수 있어야 함', () => {
			// JWT 토큰 유효성 검사는 플랫폼에 관계없이 동일해야 함
			expect(isTokenValid(validAccessToken)).toBe(true);
			expect(isTokenExpired(validAccessToken)).toBe(false);
			expect(getUserIdFromToken(validAccessToken)).toBe('1');

			expect(isTokenValid(expiredAccessToken)).toBe(false);
			expect(isTokenExpired(expiredAccessToken)).toBe(true);
			expect(getUserIdFromToken(expiredAccessToken)).toBe('1');
		});

		it('안드로이드에서 API 엔드포인트가 올바르게 사용되어야 함', async () => {
			const authStoreModule = await import('$lib/stores/authStore');
			const authActions = authStoreModule.authActions;

			// 로그인 API 응답 모킹
			mockAxios.post.mockResolvedValueOnce({
				data: {
					data: {
						tokens: {
							access_token: validAccessToken,
							refresh_token: validRefreshToken,
							token_type: 'Bearer',
							expires_in: 900,
							user: testUser
						},
						user: testUser
					}
				}
			});

			// 로그인 실행
			await authActions.login({
				username: 'testuser',
				password: 'password123'
			});

			// 올바른 API 엔드포인트 호출 확인
			expect(mockAxios.post).toHaveBeenCalledWith('/api/auth/login', {
				username: 'testuser',
				password: 'password123'
			});
		});

		it('안드로이드에서 토큰 독립성이 유지되어야 함', async () => {
			const tokenStorage = getTokenStorage();
			await tokenStorage.initialize();

			// 안드로이드에서 토큰 저장
			await tokenStorage.storeTokens(validAccessToken, validRefreshToken);

			// 토큰이 메모리에만 저장되고 다른 저장소에는 영향을 주지 않아야 함
			expect(mockTauriStore.set).not.toHaveBeenCalled();

			// 토큰 조회 확인
			const accessToken = await tokenStorage.getAccessToken();
			const refreshToken = await tokenStorage.getRefreshToken();

			expect(accessToken).toBe(validAccessToken);
			expect(refreshToken).toBe(validRefreshToken);
		});
	});
});
