/**
 * 인증 플로우 통합 테스트 (14.1)
 *
 * 이 테스트는 JWT 인증 시스템의 전체 플로우를 검증합니다:
 * - 로그인, 로그아웃, 토큰 갱신 전체 플로우 테스트
 * - 토큰 만료 시나리오 및 자동 갱신 테스트
 * - 네트워크 오류 상황에서의 에러 처리 테스트
 *
 * 요구사항: 6.2, 6.4, 6.5
 */

import { describe, it, expect, beforeEach, afterEach, vi, beforeAll, afterAll } from 'vitest';
import { get } from 'svelte/store';
import type { User } from '$lib/User';

// 테스트용 모킹 설정
const mockTauriStore = {
	get: vi.fn(),
	set: vi.fn(),
	save: vi.fn(),
	load: vi.fn(),
	clear: vi.fn(),
	delete: vi.fn(),
	entries: vi.fn(),
	keys: vi.fn(),
	values: vi.fn(),
	length: vi.fn(),
	reset: vi.fn()
};

// Tauri 환경 모킹
vi.mock('@tauri-apps/plugin-store', () => ({
	Store: vi.fn().mockImplementation(() => mockTauriStore)
}));

// HTTP 클라이언트 모킹
const mockAxios = {
	post: vi.fn(),
	get: vi.fn(),
	put: vi.fn(),
	delete: vi.fn(),
	interceptors: {
		request: { use: vi.fn() },
		response: { use: vi.fn() }
	}
};

vi.mock('$lib/services/AxiosBackend', () => ({
	authClient: mockAxios
}));

// 플랫폼 서비스 모킹 (데스크탑 환경)
vi.mock('$lib/services/platformService', async () => {
	const actual = await vi.importActual('$lib/services/platformService');
	return {
		...actual,
		getCurrentPlatform: vi.fn().mockReturnValue('desktop'),
		isDesktop: vi.fn().mockReturnValue(true),
		isTauri: vi.fn().mockReturnValue(true),
		isAndroid: vi.fn().mockReturnValue(false),
		isIOS: vi.fn().mockReturnValue(false),
		debugPlatform: vi.fn()
	};
});
// 네비게이션 모킹
vi.mock('$app/navigation', () => ({
	goto: vi.fn()
}));

// 환경 모킹
vi.mock('$app/environment', () => ({
	browser: true,
	dev: true
}));

// 토큰 갱신 서비스 모킹
const mockTokenRefreshService = {
	refreshAccessToken: vi.fn(),
	waitForTokenRefresh: vi.fn(),
	handleRefreshFailure: vi.fn(),
	ensureValidToken: vi.fn(),
	isRefreshing: vi.fn().mockReturnValue(false),
	getQueueLength: vi.fn().mockReturnValue(0)
};

vi.mock('$lib/services/tokenRefreshService', () => ({
	tokenRefreshService: mockTokenRefreshService,
	refreshAccessToken: vi.fn(),
	waitForTokenRefresh: vi.fn(),
	ensureValidToken: vi.fn()
}));

describe('인증 플로우 통합 테스트 (14.1)', () => {
	// 테스트용 데이터
	const testUser: User = {
		id: 1,
		username: 'testuser',
		name: '테스트 사용자',
		email: '<EMAIL>',
		role: 'user',
		department: '테스트부서',
		position: '테스트직급',
		phone: '010-1234-5678',
		created_at: '2024-01-01T00:00:00Z',
		updated_at: '2024-01-01T00:00:00Z'
	};

	// 유효한 토큰 (미래 만료 시간)
	const validAccessToken =
		'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxIiwiaWF0IjoxNzA0MDY3MjAwLCJleHAiOjk5OTk5OTk5OTksInR5cGUiOiJhY2Nlc3MifQ.valid-access-token';
	const validRefreshToken =
		'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxIiwiaWF0IjoxNzA0MDY3MjAwLCJleHAiOjk5OTk5OTk5OTksInR5cGUiOiJyZWZyZXNoIn0.valid-refresh-token';

	// 만료된 토큰 (과거 만료 시간)
	const expiredAccessToken =
		'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxIiwiaWF0IjoxNzA0MDY3MjAwLCJleHAiOjE3MDQwNjcyMDEsInR5cGUiOiJhY2Nlc3MifQ.expired-access-token';
	const expiredRefreshToken =
		'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxIiwiaWF0IjoxNzA0MDY3MjAwLCJleHAiOjE3MDQwNjcyMDEsInR5cGUiOiJyZWZyZXNoIn0.expired-refresh-token';

	// 곧 만료될 토큰 (5분 후 만료)
	const soonToExpireToken = `eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxIiwiaWF0IjoxNzA0MDY3MjAwLCJleHAiOjE3MDQwNjc1MDAsInR5cGUiOiJhY2Nlc3MifQ.soon-to-expire-token`;

	const testTokenResponse = {
		access_token: validAccessToken,
		refresh_token: validRefreshToken,
		token_type: 'Bearer' as const,
		expires_in: 900, // 15분
		user: testUser
	};

	// 동적 import를 위한 변수들
	let tokenService: any;
	let authActions: any;
	let authState: any;
	let isAuthenticated: any;
	let currentUser: any;
	let tokenRefreshService: any;

	beforeAll(async () => {
		// 전역 설정
		global.console.log = vi.fn();
		global.console.error = vi.fn();
		global.console.warn = vi.fn();

		// 동적 import로 서비스들 로드
		const tokenServiceModule = await import('$lib/services/tokenService');
		tokenService = tokenServiceModule.tokenService;

		const authStoreModule = await import('$lib/stores/authStore');
		authActions = authStoreModule.authActions;
		authState = authStoreModule.authState;
		isAuthenticated = authStoreModule.isAuthenticated;
		currentUser = authStoreModule.currentUser;

		const tokenRefreshServiceModule = await import('$lib/services/tokenRefreshService');
		tokenRefreshService = tokenRefreshServiceModule.tokenRefreshService;
	});
	beforeEach(async () => {
		// 모든 모킹 초기화
		vi.clearAllMocks();

		// Tauri Store 모킹 초기화
		mockTauriStore.get.mockResolvedValue(null);
		mockTauriStore.set.mockResolvedValue(undefined);
		mockTauriStore.save.mockResolvedValue(undefined);
		mockTauriStore.load.mockResolvedValue(undefined);
		mockTauriStore.clear.mockResolvedValue(undefined);
		mockTauriStore.delete.mockResolvedValue(undefined);

		// HTTP 클라이언트 모킹 초기화
		mockAxios.post.mockReset();
		mockAxios.get.mockReset();

		// 토큰 갱신 서비스 모킹 초기화
		mockTokenRefreshService.refreshAccessToken.mockReset();
		mockTokenRefreshService.waitForTokenRefresh.mockReset();
		mockTokenRefreshService.handleRefreshFailure.mockReset();
		mockTokenRefreshService.ensureValidToken.mockReset();
		mockTokenRefreshService.isRefreshing.mockReturnValue(false);
		mockTokenRefreshService.getQueueLength.mockReturnValue(0);

		// 인증 상태 초기화
		authState.set({
			isAuthenticated: false,
			isInitialized: false,
			isLoading: false,
			user: null,
			accessToken: null,
			refreshToken: null,
			tokenExpiresAt: null,
			error: null,
			debugInfo: {
				lastTokenRefresh: null,
				tokenRefreshCount: 0,
				platform: 'desktop'
			}
		});
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	afterAll(() => {
		vi.restoreAllMocks();
	});

	describe('로그인, 로그아웃, 토큰 갱신 전체 플로우 테스트', () => {
		it('완전한 인증 플로우가 순서대로 동작해야 함', async () => {
			// 1. 초기화
			await authActions.initialize();
			let state = get(authState);
			expect(state.isInitialized).toBe(true);
			expect(state.isAuthenticated).toBe(false);

			// 2. 로그인
			mockAxios.post.mockResolvedValueOnce({
				data: {
					data: {
						tokens: testTokenResponse,
						user: testUser
					}
				}
			});

			await authActions.login({
				username: 'testuser',
				password: 'password123'
			});

			state = get(authState);
			expect(state.isAuthenticated).toBe(true);
			expect(state.user).toEqual(testUser);
			expect(state.accessToken).toBe(validAccessToken);
			expect(state.refreshToken).toBe(validRefreshToken);

			// 3. 토큰 갱신
			const newTokenResponse = {
				access_token: 'new-access-token',
				refresh_token: 'new-refresh-token',
				token_type: 'Bearer' as const,
				expires_in: 900
			};

			mockAxios.post.mockResolvedValueOnce({
				data: newTokenResponse
			});

			const refreshResult = await authActions.refreshAuth();
			expect(refreshResult).toBe(true);

			state = get(authState);
			expect(state.accessToken).toBe('new-access-token');
			expect(state.refreshToken).toBe('new-refresh-token');
			expect(state.debugInfo?.tokenRefreshCount).toBeGreaterThan(0);

			// 4. 로그아웃
			mockAxios.post.mockResolvedValueOnce({ data: { message: '로그아웃 성공' } });

			await authActions.logout();

			state = get(authState);
			expect(state.isAuthenticated).toBe(false);
			expect(state.user).toBeNull();
			expect(state.accessToken).toBeNull();
			expect(state.refreshToken).toBeNull();

			// API 호출 순서 확인
			expect(mockAxios.post).toHaveBeenNthCalledWith(1, '/api/auth/login', {
				username: 'testuser',
				password: 'password123'
			});
			expect(mockAxios.post).toHaveBeenNthCalledWith(2, '/api/auth/refresh', {
				refresh_token: validRefreshToken
			});
			expect(mockAxios.post).toHaveBeenNthCalledWith(3, '/api/auth/logout');
		});

		it('로그인 실패 후 재시도가 가능해야 함', async () => {
			await authActions.initialize();

			// 첫 번째 로그인 시도 - 실패
			mockAxios.post.mockRejectedValueOnce({
				response: {
					status: 401,
					data: { message: 'Invalid credentials' }
				}
			});

			await expect(
				authActions.login({
					username: 'wronguser',
					password: 'wrongpassword'
				})
			).rejects.toThrow();

			let state = get(authState);
			expect(state.isAuthenticated).toBe(false);
			expect(state.error?.type).toBe('INVALID_CREDENTIALS');

			// 에러 상태 초기화
			authActions.clearError();

			// 두 번째 로그인 시도 - 성공
			mockAxios.post.mockResolvedValueOnce({
				data: {
					data: {
						tokens: testTokenResponse,
						user: testUser
					}
				}
			});

			await authActions.login({
				username: 'testuser',
				password: 'password123'
			});

			state = get(authState);
			expect(state.isAuthenticated).toBe(true);
			expect(state.error).toBeNull();
			expect(state.user).toEqual(testUser);
		});

		it('로그아웃 실패 시에도 로컬 상태는 초기화되어야 함', async () => {
			// 로그인 상태 설정
			await authActions.initialize();
			authState.update((state) => ({
				...state,
				isAuthenticated: true,
				user: testUser,
				accessToken: validAccessToken,
				refreshToken: validRefreshToken
			}));

			// 서버 로그아웃 실패
			mockAxios.post.mockRejectedValueOnce({
				response: {
					status: 500,
					data: { message: 'Server error' }
				}
			});

			await authActions.logout();

			// 서버 로그아웃 실패해도 로컬 상태는 초기화되어야 함
			const state = get(authState);
			expect(state.isAuthenticated).toBe(false);
			expect(state.user).toBeNull();
			expect(state.accessToken).toBeNull();
			expect(state.refreshToken).toBeNull();

			// 에러는 기록되지만 로그아웃은 완료됨
			expect(state.error?.type).toBe('SERVER_ERROR');
		});
	});

	describe('토큰 만료 시나리오 및 자동 갱신 테스트', () => {
		beforeEach(async () => {
			await authActions.initialize();
		});

		it('액세스 토큰 만료 시 자동 갱신이 동작해야 함', async () => {
			// 만료된 액세스 토큰과 유효한 리프레시 토큰 설정
			mockTauriStore.get.mockImplementation((key: string) => {
				if (key === 'access_token') return Promise.resolve(expiredAccessToken);
				if (key === 'refresh_token') return Promise.resolve(validRefreshToken);
				return Promise.resolve(null);
			});

			// 토큰 갱신 서비스 모킹
			mockTokenRefreshService.waitForTokenRefresh.mockResolvedValue('new-access-token');

			// API 요청 시 401 오류 발생
			mockAxios.get.mockRejectedValueOnce({
				response: { status: 401 },
				config: { url: '/api/auth/me', method: 'get', headers: {} }
			});

			// 토큰 갱신 후 재시도 성공
			mockAxios.get.mockResolvedValueOnce({
				data: {
					data: {
						user: testUser
					}
				}
			});

			// 사용자 정보 로드 시도
			await authActions.loadUser();

			// 토큰 갱신이 시도되었는지 확인
			expect(mockTokenRefreshService.waitForTokenRefresh).toHaveBeenCalled();

			// 최종적으로 사용자 정보가 로드되었는지 확인
			const state = get(authState);
			expect(state.user).toEqual(testUser);
		});

		it('리프레시 토큰도 만료된 경우 로그아웃 처리해야 함', async () => {
			// 만료된 토큰들 설정
			mockTauriStore.get.mockImplementation((key: string) => {
				if (key === 'access_token') return Promise.resolve(expiredAccessToken);
				if (key === 'refresh_token') return Promise.resolve(expiredRefreshToken);
				return Promise.resolve(null);
			});

			// 토큰 갱신 실패
			mockTokenRefreshService.waitForTokenRefresh.mockRejectedValue({
				type: 'REFRESH_FAILED',
				message: '리프레시 토큰이 만료되었습니다'
			});

			// API 요청 시 401 오류 발생
			mockAxios.get.mockRejectedValueOnce({
				response: { status: 401 },
				config: { url: '/api/auth/me', method: 'get', headers: {} }
			});

			// 사용자 정보 로드 시도
			await authActions.loadUser();

			// 토큰 갱신이 시도되었는지 확인
			expect(mockTokenRefreshService.waitForTokenRefresh).toHaveBeenCalled();

			// 갱신 실패로 인한 로그아웃 처리 확인
			const state = get(authState);
			expect(state.isAuthenticated).toBe(false);
		});

		it('토큰이 곧 만료될 때 사전 갱신이 동작해야 함', async () => {
			// 곧 만료될 토큰 설정
			await tokenService.storeTokens(soonToExpireToken, validRefreshToken);

			// 토큰 만료 임박 확인
			const isExpiringSoon = await tokenService.isAccessTokenExpiringSoon(5);
			expect(isExpiringSoon).toBe(true);

			// 사전 갱신 시도
			mockTokenRefreshService.ensureValidToken.mockResolvedValue('new-access-token');

			const validToken = await mockTokenRefreshService.ensureValidToken(5);
			expect(validToken).toBe('new-access-token');
			expect(mockTokenRefreshService.ensureValidToken).toHaveBeenCalledWith(5);
		});

		it('동시 요청 시 토큰 갱신 중복 방지가 동작해야 함', async () => {
			// 토큰 갱신 중 상태 모킹
			mockTokenRefreshService.isRefreshing.mockReturnValue(true);
			mockTokenRefreshService.getQueueLength.mockReturnValue(3);

			// 대기열에 추가된 요청들
			const promise1 = mockTokenRefreshService.waitForTokenRefresh();
			const promise2 = mockTokenRefreshService.waitForTokenRefresh();
			const promise3 = mockTokenRefreshService.waitForTokenRefresh();

			// 모든 요청이 동일한 새 토큰을 받아야 함
			mockTokenRefreshService.waitForTokenRefresh.mockResolvedValue('shared-new-token');

			const results = await Promise.all([promise1, promise2, promise3]);

			expect(results).toEqual(['shared-new-token', 'shared-new-token', 'shared-new-token']);
			expect(mockTokenRefreshService.waitForTokenRefresh).toHaveBeenCalledTimes(3);
		});

		it('토큰 갱신 중 새로운 요청이 대기열에 추가되어야 함', async () => {
			// 토큰 갱신 진행 중
			mockTokenRefreshService.isRefreshing.mockReturnValue(true);
			mockTokenRefreshService.getQueueLength.mockReturnValue(1);

			// 새로운 요청 추가
			const waitPromise = mockTokenRefreshService.waitForTokenRefresh();
			mockTokenRefreshService.waitForTokenRefresh.mockResolvedValue('queued-token');

			const result = await waitPromise;
			expect(result).toBe('queued-token');

			// 대기열 관리 확인
			expect(mockTokenRefreshService.isRefreshing()).toBe(true);
			expect(mockTokenRefreshService.getQueueLength()).toBe(1);
		});
	});

	describe('네트워크 오류 상황에서의 에러 처리 테스트', () => {
		beforeEach(async () => {
			await authActions.initialize();
		});

		it('로그인 시 네트워크 오류를 적절히 처리해야 함', async () => {
			// 네트워크 오류 모킹
			mockAxios.post.mockRejectedValueOnce({
				code: 'NETWORK_ERROR',
				message: 'Network Error'
			});

			// 로그인 시도
			await expect(
				authActions.login({
					username: 'testuser',
					password: 'password123'
				})
			).rejects.toThrow();

			// 에러 상태 확인
			const state = get(authState);
			expect(state.error).toBeDefined();
			expect(state.error?.type).toBe('NETWORK_ERROR');
			expect(state.isAuthenticated).toBe(false);
			expect(state.isLoading).toBe(false);

			// 사용자 친화적 에러 메시지 확인
			const errorMessage = authActions.getErrorMessage();
			expect(errorMessage).toBe('네트워크 연결을 확인해주세요.');
		});

		it('토큰 갱신 시 네트워크 오류를 적절히 처리해야 함', async () => {
			// 기존 토큰 설정
			await tokenService.storeTokens('old-access-token', validRefreshToken);

			// 네트워크 오류로 토큰 갱신 실패
			mockAxios.post.mockRejectedValueOnce({
				code: 'NETWORK_ERROR',
				message: 'Network timeout'
			});

			// 토큰 갱신 시도
			const result = await authActions.refreshAuth();
			expect(result).toBe(false);

			// 에러 상태 확인
			const state = get(authState);
			expect(state.error?.type).toBe('REFRESH_FAILED');
			expect(state.isAuthenticated).toBe(false);
		});

		it('API 요청 시 연결 시간 초과를 처리해야 함', async () => {
			// 연결 시간 초과 오류 모킹
			mockAxios.get.mockRejectedValueOnce({
				code: 'ECONNABORTED',
				message: 'timeout of 30000ms exceeded'
			});

			// 사용자 정보 로드 시도
			await authActions.loadUser();

			// 에러 상태 확인
			const state = get(authState);
			expect(state.error?.type).toBe('SERVER_ERROR');
		});

		it('서버 응답 없음 상황을 처리해야 함', async () => {
			// 서버 응답 없음 모킹
			mockAxios.post.mockRejectedValueOnce({
				request: {},
				message: 'Network Error'
			});

			// 로그인 시도
			await expect(
				authActions.login({
					username: 'testuser',
					password: 'password123'
				})
			).rejects.toThrow();

			// 네트워크 오류로 처리되어야 함
			const state = get(authState);
			expect(state.error?.type).toBe('NETWORK_ERROR');
		});

		it('로그아웃 시 네트워크 오류가 발생해도 로컬 정리는 완료되어야 함', async () => {
			// 로그인 상태 설정
			authState.update((state) => ({
				...state,
				isAuthenticated: true,
				user: testUser,
				accessToken: validAccessToken,
				refreshToken: validRefreshToken
			}));

			// 네트워크 오류로 서버 로그아웃 실패
			mockAxios.post.mockRejectedValueOnce({
				code: 'NETWORK_ERROR',
				message: 'Network Error'
			});

			// 로그아웃 실행
			await authActions.logout();

			// 로컬 상태는 초기화되어야 함
			const state = get(authState);
			expect(state.isAuthenticated).toBe(false);
			expect(state.user).toBeNull();
			expect(state.accessToken).toBeNull();
			expect(state.refreshToken).toBeNull();

			// 네트워크 오류 알림은 표시되어야 함
			expect(state.error?.type).toBe('NETWORK_ERROR');
		});

		it('연속된 네트워크 오류 시 적절한 재시도 로직이 동작해야 함', async () => {
			// 첫 번째 시도 - 네트워크 오류
			mockAxios.post.mockRejectedValueOnce({
				code: 'NETWORK_ERROR',
				message: 'Network Error'
			});

			// 첫 번째 로그인 시도
			await expect(
				authActions.login({
					username: 'testuser',
					password: 'password123'
				})
			).rejects.toThrow();

			let state = get(authState);
			expect(state.error?.type).toBe('NETWORK_ERROR');

			// 에러 상태 초기화
			authActions.clearError();

			// 두 번째 시도 - 성공
			mockAxios.post.mockResolvedValueOnce({
				data: {
					data: {
						tokens: testTokenResponse,
						user: testUser
					}
				}
			});

			// 두 번째 로그인 시도
			await authActions.login({
				username: 'testuser',
				password: 'password123'
			});

			state = get(authState);
			expect(state.isAuthenticated).toBe(true);
			expect(state.error).toBeNull();
		});

		it('부분적 네트워크 장애 시 복구 가능해야 함', async () => {
			// 로그인은 성공
			mockAxios.post.mockResolvedValueOnce({
				data: {
					data: {
						tokens: testTokenResponse,
						user: testUser
					}
				}
			});

			await authActions.login({
				username: 'testuser',
				password: 'password123'
			});

			let state = get(authState);
			expect(state.isAuthenticated).toBe(true);

			// 사용자 정보 로드 시 네트워크 오류
			mockAxios.get.mockRejectedValueOnce({
				code: 'NETWORK_ERROR',
				message: 'Network Error'
			});

			await authActions.loadUser();

			// 인증 상태는 유지되어야 함
			state = get(authState);
			expect(state.isAuthenticated).toBe(true);
			expect(state.error?.type).toBe('SERVER_ERROR');

			// 에러 상태 초기화 후 재시도 가능
			authActions.clearError();

			mockAxios.get.mockResolvedValueOnce({
				data: {
					data: {
						user: testUser
					}
				}
			});

			await authActions.loadUser();

			state = get(authState);
			expect(state.user).toEqual(testUser);
			expect(state.error).toBeNull();
		});
	});

	describe('에러 복구 및 상태 관리', () => {
		beforeEach(async () => {
			await authActions.initialize();
		});

		it('에러 상태를 수동으로 초기화할 수 있어야 함', async () => {
			// 에러 상태 설정
			authState.update((state) => ({
				...state,
				error: {
					type: 'NETWORK_ERROR',
					message: '네트워크 오류'
				}
			}));

			let state = get(authState);
			expect(state.error).toBeDefined();

			// 에러 상태 초기화
			authActions.clearError();

			state = get(authState);
			expect(state.error).toBeNull();
		});

		it('사용자 친화적 에러 메시지를 제공해야 함', async () => {
			const errorTypes = [
				{ type: 'INVALID_CREDENTIALS', expected: '아이디 또는 비밀번호가 올바르지 않습니다.' },
				{ type: 'TOKEN_EXPIRED', expected: '로그인이 만료되었습니다. 다시 로그인해주세요.' },
				{ type: 'REFRESH_FAILED', expected: '인증 갱신에 실패했습니다. 다시 로그인해주세요.' },
				{ type: 'NETWORK_ERROR', expected: '네트워크 연결을 확인해주세요.' },
				{ type: 'VALIDATION_ERROR', expected: '입력 정보를 확인해주세요.' },
				{ type: 'SERVER_ERROR', expected: '서버 오류가 발생했습니다. 잠시 후 다시 시도해주세요.' },
				{ type: 'UNKNOWN_ERROR', expected: '알 수 없는 오류가 발생했습니다.' }
			];

			for (const { type, expected } of errorTypes) {
				const errorMessage = authActions.getErrorMessage({
					type,
					message: 'Test error'
				});
				expect(errorMessage).toBe(expected);
			}
		});

		it('디버그 정보가 개발 환경에서만 제공되어야 함', async () => {
			// 로그인 상태 설정
			authState.update((state) => ({
				...state,
				isAuthenticated: true,
				user: testUser,
				debugInfo: {
					lastTokenRefresh: new Date(),
					tokenRefreshCount: 5,
					platform: 'desktop'
				}
			}));

			// 디버그 정보 출력
			await authActions.debugAuth();

			// 개발 환경에서 디버그 로그 출력 확인
			expect(console.log).toHaveBeenCalledWith(
				expect.stringContaining('[Auth Store Debug]'),
				expect.objectContaining({
					isAuthenticated: true,
					hasUser: true,
					debugInfo: expect.objectContaining({
						tokenRefreshCount: 5,
						platform: 'desktop'
					})
				})
			);
		});
	});
});
