/**
 * 앱 재시작 후 인증 상태 복원 테스트 (14.2)
 *
 * 이 테스트는 앱 재시작 시나리오에서 인증 상태 복원을 검증합니다:
 * - 앱 종료 후 재시작 시 토큰 자동 로드 확인
 * - 만료된 토큰의 자동 갱신 처리 확인
 * - 인증 상태 복원 실패 시 로그인 페이지 이동 확인
 *
 * 요구사항: 5.4, 8.3
 */

import { describe, it, expect, beforeEach, afterEach, vi, beforeAll, afterAll } from 'vitest';
import { get } from 'svelte/store';
import type { User } from '$lib/User';

// 테스트용 모킹 설정
const mockTauriStore = {
	get: vi.fn(),
	set: vi.fn(),
	save: vi.fn(),
	load: vi.fn(),
	clear: vi.fn(),
	delete: vi.fn(),
	entries: vi.fn(),
	keys: vi.fn(),
	values: vi.fn(),
	length: vi.fn(),
	reset: vi.fn()
};

// Tauri 환경 모킹
vi.mock('@tauri-apps/plugin-store', () => ({
	Store: vi.fn().mockImplementation(() => mockTauriStore)
}));

// HTTP 클라이언트 모킹
const mockAxios = {
	post: vi.fn(),
	get: vi.fn(),
	put: vi.fn(),
	delete: vi.fn(),
	interceptors: {
		request: { use: vi.fn() },
		response: { use: vi.fn() }
	}
};

vi.mock('$lib/services/AxiosBackend', () => ({
	authClient: mockAxios
}));

// 플랫폼 서비스 모킹 (데스크탑 환경)
vi.mock('$lib/services/platformService', async () => {
	const actual = await vi.importActual('$lib/services/platformService');
	return {
		...actual,
		getCurrentPlatform: vi.fn().mockReturnValue('desktop'),
		isDesktop: vi.fn().mockReturnValue(true),
		isTauri: vi.fn().mockReturnValue(true),
		isAndroid: vi.fn().mockReturnValue(false),
		isIOS: vi.fn().mockReturnValue(false),
		debugPlatform: vi.fn()
	};
});

// 네비게이션 모킹
const mockGoto = vi.fn();
vi.mock('$app/navigation', () => ({
	goto: mockGoto
}));

// 환경 모킹
vi.mock('$app/environment', () => ({
	browser: true,
	dev: true
}));
describe('앱 재시작 후 인증 상태 복원 테스트 (14.2)', () => {
	// 테스트용 데이터
	const testUser: User = {
		id: 1,
		username: 'testuser',
		name: '테스트 사용자',
		email: '<EMAIL>',
		role: 'user',
		department: '테스트부서',
		position: '테스트직급',
		phone: '010-1234-5678',
		created_at: '2024-01-01T00:00:00Z',
		updated_at: '2024-01-01T00:00:00Z'
	};

	// 유효한 토큰 (미래 만료 시간)
	const validAccessToken =
		'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxIiwiaWF0IjoxNzA0MDY3MjAwLCJleHAiOjk5OTk5OTk5OTksInR5cGUiOiJhY2Nlc3MifQ.valid-access-token';
	const validRefreshToken =
		'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxIiwiaWF0IjoxNzA0MDY3MjAwLCJleHAiOjk5OTk5OTk5OTksInR5cGUiOiJyZWZyZXNoIn0.valid-refresh-token';

	// 만료된 토큰 (과거 만료 시간)
	const expiredAccessToken =
		'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxIiwiaWF0IjoxNzA0MDY3MjAwLCJleHAiOjE3MDQwNjcyMDEsInR5cGUiOiJhY2Nlc3MifQ.expired-access-token';
	const expiredRefreshToken =
		'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxIiwiaWF0IjoxNzA0MDY3MjAwLCJleHAiOjE3MDQwNjcyMDEsInR5cGUiOiJyZWZyZXNoIn0.expired-refresh-token';

	// 곧 만료될 토큰 (현재 시간 + 2분)
	const soonToExpireToken = (() => {
		const now = Math.floor(Date.now() / 1000);
		const exp = now + 120; // 2분 후 만료
		return `eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxIiwiaWF0IjoxNzA0MDY3MjAwLCJleHAiOiR7ZXhwfSwidHlwZSI6ImFjY2VzcyJ9.soon-to-expire-token`;
	})();

	// 동적 import를 위한 변수들
	let tokenService: any;
	let authActions: any;
	let authState: any;
	let isAuthenticated: any;
	let currentUser: any;
	let getCurrentAuthState: any;
	let requireAuth: any;
	let redirectIfAuthenticated: any;

	beforeAll(async () => {
		// 전역 설정
		global.console.log = vi.fn();
		global.console.error = vi.fn();
		global.console.warn = vi.fn();

		// 동적 import로 서비스들 로드
		const tokenServiceModule = await import('$lib/services/tokenService');
		tokenService = tokenServiceModule.tokenService;

		const authStoreModule = await import('$lib/stores/authStore');
		authActions = authStoreModule.authActions;
		authState = authStoreModule.authState;
		isAuthenticated = authStoreModule.isAuthenticated;
		currentUser = authStoreModule.currentUser;
		getCurrentAuthState = authStoreModule.getCurrentAuthState;
		requireAuth = authStoreModule.requireAuth;
		redirectIfAuthenticated = authStoreModule.redirectIfAuthenticated;
	});

	beforeEach(async () => {
		// 모든 모킹 초기화
		vi.clearAllMocks();

		// Tauri Store 모킹 초기화
		mockTauriStore.get.mockResolvedValue(null);
		mockTauriStore.set.mockResolvedValue(undefined);
		mockTauriStore.save.mockResolvedValue(undefined);
		mockTauriStore.load.mockResolvedValue(undefined);
		mockTauriStore.clear.mockResolvedValue(undefined);
		mockTauriStore.delete.mockResolvedValue(undefined);

		// HTTP 클라이언트 모킹 초기화
		mockAxios.post.mockReset();
		mockAxios.get.mockReset();

		// 네비게이션 모킹 초기화
		mockGoto.mockReset();

		// 인증 상태 초기화 (앱 재시작 상태 시뮬레이션)
		authState.set({
			isAuthenticated: false,
			isInitialized: false,
			isLoading: false,
			user: null,
			accessToken: null,
			refreshToken: null,
			tokenExpiresAt: null,
			error: null,
			debugInfo: {
				lastTokenRefresh: null,
				tokenRefreshCount: 0,
				platform: 'desktop'
			}
		});
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	afterAll(() => {
		vi.restoreAllMocks();
	});

	describe('앱 종료 후 재시작 시 토큰 자동 로드 확인', () => {
		it('유효한 토큰이 저장되어 있을 때 자동으로 인증 상태를 복원해야 함', async () => {
			// 저장된 유효한 토큰 모킹
			mockTauriStore.get.mockImplementation((key: string) => {
				if (key === 'access_token') return Promise.resolve(validAccessToken);
				if (key === 'refresh_token') return Promise.resolve(validRefreshToken);
				return Promise.resolve(null);
			});

			// 사용자 정보 API 응답 모킹
			mockAxios.get.mockResolvedValueOnce({
				data: {
					data: {
						user: testUser
					}
				}
			});

			// 앱 재시작 시뮬레이션 - 인증 시스템 초기화
			await authActions.initialize();

			// 인증 상태 확인
			const state = get(authState);
			expect(state.isAuthenticated).toBe(true);
			expect(state.isInitialized).toBe(true);
			expect(state.user).toEqual(testUser);
			expect(state.accessToken).toBe(validAccessToken);
			expect(state.refreshToken).toBe(validRefreshToken);

			// 토큰 로드 확인
			expect(mockTauriStore.get).toHaveBeenCalledWith('access_token');
			expect(mockTauriStore.get).toHaveBeenCalledWith('refresh_token');

			// 사용자 정보 로드 API 호출 확인
			expect(mockAxios.get).toHaveBeenCalledWith('/api/auth/me');

			// 디버그 정보 확인
			expect(state.debugInfo?.platform).toBe('desktop');
		});

		it('토큰이 없을 때 비인증 상태로 초기화해야 함', async () => {
			// 저장된 토큰이 없음
			mockTauriStore.get.mockResolvedValue(null);

			// 앱 재시작 시뮬레이션 - 인증 시스템 초기화
			await authActions.initialize();

			// 비인증 상태 확인
			const state = get(authState);
			expect(state.isAuthenticated).toBe(false);
			expect(state.isInitialized).toBe(true);
			expect(state.user).toBeNull();
			expect(state.accessToken).toBeNull();
			expect(state.refreshToken).toBeNull();

			// 사용자 정보 API가 호출되지 않았는지 확인
			expect(mockAxios.get).not.toHaveBeenCalled();
		});

		it('액세스 토큰만 있고 리프레시 토큰이 없을 때 비인증 상태로 처리해야 함', async () => {
			// 액세스 토큰만 저장됨
			mockTauriStore.get.mockImplementation((key: string) => {
				if (key === 'access_token') return Promise.resolve(validAccessToken);
				if (key === 'refresh_token') return Promise.resolve(null);
				return Promise.resolve(null);
			});

			// 앱 재시작 시뮬레이션
			await authActions.initialize();

			// 비인증 상태로 처리되어야 함
			const state = get(authState);
			expect(state.isAuthenticated).toBe(false);
			expect(state.accessToken).toBe(validAccessToken);
			expect(state.refreshToken).toBeNull();

			// 사용자 정보 로드가 시도되지 않아야 함
			expect(mockAxios.get).not.toHaveBeenCalled();
		});

		it('리프레시 토큰만 있고 액세스 토큰이 없을 때 비인증 상태로 처리해야 함', async () => {
			// 리프레시 토큰만 저장됨
			mockTauriStore.get.mockImplementation((key: string) => {
				if (key === 'access_token') return Promise.resolve(null);
				if (key === 'refresh_token') return Promise.resolve(validRefreshToken);
				return Promise.resolve(null);
			});

			// 앱 재시작 시뮬레이션
			await authActions.initialize();

			// 비인증 상태로 처리되어야 함
			const state = get(authState);
			expect(state.isAuthenticated).toBe(false);
			expect(state.accessToken).toBeNull();
			expect(state.refreshToken).toBe(validRefreshToken);

			// 사용자 정보 로드가 시도되지 않아야 함
			expect(mockAxios.get).not.toHaveBeenCalled();
		});

		it('토큰 로드 중 오류가 발생해도 안전하게 처리해야 함', async () => {
			// 토큰 로드 오류 모킹
			mockTauriStore.get.mockRejectedValue(new Error('Storage access denied'));

			// 앱 재시작 시뮬레이션
			await authActions.initialize();

			// 오류가 발생해도 초기화는 완료되어야 함
			const state = get(authState);
			expect(state.isInitialized).toBe(true);
			expect(state.isAuthenticated).toBe(false);
			expect(state.error?.type).toBe('UNKNOWN_ERROR');
		});
	});
	describe('만료된 토큰의 자동 갱신 처리 확인', () => {
		it('만료된 액세스 토큰과 유효한 리프레시 토큰이 있을 때 자동 갱신해야 함', async () => {
			// 만료된 액세스 토큰과 유효한 리프레시 토큰
			mockTauriStore.get.mockImplementation((key: string) => {
				if (key === 'access_token') return Promise.resolve(expiredAccessToken);
				if (key === 'refresh_token') return Promise.resolve(validRefreshToken);
				return Promise.resolve(null);
			});

			// 사용자 정보 API에서 401 오류 (만료된 토큰)
			mockAxios.get.mockRejectedValueOnce({
				response: { status: 401 }
			});

			// 토큰 갱신 API 응답
			const newTokenResponse = {
				access_token: 'new-access-token',
				refresh_token: 'new-refresh-token',
				token_type: 'Bearer',
				expires_in: 900
			};

			mockAxios.post.mockResolvedValueOnce({
				data: newTokenResponse
			});

			// 갱신 후 사용자 정보 API 응답
			mockAxios.get.mockResolvedValueOnce({
				data: {
					data: {
						user: testUser
					}
				}
			});

			// 앱 재시작 시뮬레이션
			await authActions.initialize();

			// 토큰 갱신이 시도되었는지 확인
			expect(mockAxios.post).toHaveBeenCalledWith('/api/auth/refresh', {
				refresh_token: validRefreshToken
			});

			// 새 토큰이 저장되었는지 확인
			expect(mockTauriStore.set).toHaveBeenCalledWith('access_token', 'new-access-token');
			expect(mockTauriStore.set).toHaveBeenCalledWith('refresh_token', 'new-refresh-token');

			// 최종 인증 상태 확인
			const state = get(authState);
			expect(state.isAuthenticated).toBe(true);
			expect(state.user).toEqual(testUser);
			expect(state.accessToken).toBe('new-access-token');
			expect(state.refreshToken).toBe('new-refresh-token');

			// 디버그 정보에 갱신 기록 확인
			expect(state.debugInfo?.tokenRefreshCount).toBeGreaterThan(0);
			expect(state.debugInfo?.lastTokenRefresh).toBeInstanceOf(Date);
		});

		it('곧 만료될 토큰이 있을 때 사전 갱신을 시도해야 함', async () => {
			// 곧 만료될 토큰 설정
			mockTauriStore.get.mockImplementation((key: string) => {
				if (key === 'access_token') return Promise.resolve(soonToExpireToken);
				if (key === 'refresh_token') return Promise.resolve(validRefreshToken);
				return Promise.resolve(null);
			});

			// 토큰 갱신 API 응답
			const newTokenResponse = {
				access_token: 'refreshed-access-token',
				refresh_token: 'refreshed-refresh-token',
				token_type: 'Bearer',
				expires_in: 900
			};

			mockAxios.post.mockResolvedValueOnce({
				data: newTokenResponse
			});

			// 사용자 정보 API 응답
			mockAxios.get.mockResolvedValueOnce({
				data: {
					data: {
						user: testUser
					}
				}
			});

			// 앱 재시작 시뮬레이션
			await authActions.initialize();

			// 토큰이 곧 만료되는지 확인
			const isExpiringSoon = await tokenService.isAccessTokenExpiringSoon(5);
			expect(isExpiringSoon).toBe(true);

			// 사전 갱신이 시도되었는지 확인 (사용자 정보 로드 시)
			const state = get(authState);
			expect(state.isAuthenticated).toBe(true);
			expect(state.user).toEqual(testUser);
		});

		it('모든 토큰이 만료된 경우 비인증 상태로 처리해야 함', async () => {
			// 모든 토큰이 만료됨
			mockTauriStore.get.mockImplementation((key: string) => {
				if (key === 'access_token') return Promise.resolve(expiredAccessToken);
				if (key === 'refresh_token') return Promise.resolve(expiredRefreshToken);
				return Promise.resolve(null);
			});

			// 사용자 정보 API에서 401 오류
			mockAxios.get.mockRejectedValueOnce({
				response: { status: 401 }
			});

			// 토큰 갱신도 401 오류 (리프레시 토큰 만료)
			mockAxios.post.mockRejectedValueOnce({
				response: { status: 401 }
			});

			// 앱 재시작 시뮬레이션
			await authActions.initialize();

			// 토큰 갱신이 시도되었는지 확인
			expect(mockAxios.post).toHaveBeenCalledWith('/api/auth/refresh', {
				refresh_token: expiredRefreshToken
			});

			// 갱신 실패로 비인증 상태가 되어야 함
			const state = get(authState);
			expect(state.isAuthenticated).toBe(false);
			expect(state.user).toBeNull();
			expect(state.error?.type).toBe('REFRESH_FAILED');

			// 로그인 페이지로 리다이렉트되어야 함
			expect(mockGoto).toHaveBeenCalledWith('/login');
		});

		it('토큰 갱신 중 네트워크 오류가 발생하면 적절히 처리해야 함', async () => {
			// 만료된 액세스 토큰과 유효한 리프레시 토큰
			mockTauriStore.get.mockImplementation((key: string) => {
				if (key === 'access_token') return Promise.resolve(expiredAccessToken);
				if (key === 'refresh_token') return Promise.resolve(validRefreshToken);
				return Promise.resolve(null);
			});

			// 사용자 정보 API에서 401 오류
			mockAxios.get.mockRejectedValueOnce({
				response: { status: 401 }
			});

			// 토큰 갱신 시 네트워크 오류
			mockAxios.post.mockRejectedValueOnce({
				code: 'NETWORK_ERROR',
				message: 'Network Error'
			});

			// 앱 재시작 시뮬레이션
			await authActions.initialize();

			// 네트워크 오류로 인한 갱신 실패 처리 확인
			const state = get(authState);
			expect(state.isAuthenticated).toBe(false);
			expect(state.error?.type).toBe('REFRESH_FAILED');

			// 로그인 페이지로 리다이렉트되어야 함
			expect(mockGoto).toHaveBeenCalledWith('/login');
		});

		it('토큰 갱신 성공 후 사용자 정보 로드 실패 시 재시도해야 함', async () => {
			// 만료된 액세스 토큰과 유효한 리프레시 토큰
			mockTauriStore.get.mockImplementation((key: string) => {
				if (key === 'access_token') return Promise.resolve(expiredAccessToken);
				if (key === 'refresh_token') return Promise.resolve(validRefreshToken);
				return Promise.resolve(null);
			});

			// 첫 번째 사용자 정보 API에서 401 오류
			mockAxios.get.mockRejectedValueOnce({
				response: { status: 401 }
			});

			// 토큰 갱신 성공
			const newTokenResponse = {
				access_token: 'new-access-token',
				refresh_token: 'new-refresh-token',
				token_type: 'Bearer',
				expires_in: 900
			};

			mockAxios.post.mockResolvedValueOnce({
				data: newTokenResponse
			});

			// 갱신 후 사용자 정보 로드 성공
			mockAxios.get.mockResolvedValueOnce({
				data: {
					data: {
						user: testUser
					}
				}
			});

			// 앱 재시작 시뮬레이션
			await authActions.initialize();

			// 토큰 갱신과 사용자 정보 로드가 모두 성공했는지 확인
			const state = get(authState);
			expect(state.isAuthenticated).toBe(true);
			expect(state.user).toEqual(testUser);
			expect(state.accessToken).toBe('new-access-token');

			// API 호출 순서 확인
			expect(mockAxios.get).toHaveBeenCalledTimes(2); // 첫 번째 실패, 두 번째 성공
			expect(mockAxios.post).toHaveBeenCalledTimes(1); // 토큰 갱신
		});
	});

	describe('인증 상태 복원 실패 시 로그인 페이지 이동 확인', () => {
		it('토큰 서비스 초기화 실패 시 로그인 페이지로 이동해야 함', async () => {
			// 토큰 서비스 초기화 실패 모킹
			const originalInitialize = tokenService.initialize;
			tokenService.initialize = vi
				.fn()
				.mockRejectedValue(new Error('Token service initialization failed'));

			// 앱 재시작 시뮬레이션
			await authActions.initialize();

			// 에러 상태 확인
			const state = get(authState);
			expect(state.error?.type).toBe('UNKNOWN_ERROR');
			expect(state.isAuthenticated).toBe(false);

			// 원래 함수 복원
			tokenService.initialize = originalInitialize;
		});

		it('사용자 정보 로드 실패 시 적절한 에러 처리를 해야 함', async () => {
			// 유효한 토큰 설정
			mockTauriStore.get.mockImplementation((key: string) => {
				if (key === 'access_token') return Promise.resolve(validAccessToken);
				if (key === 'refresh_token') return Promise.resolve(validRefreshToken);
				return Promise.resolve(null);
			});

			// 사용자 정보 API에서 서버 오류
			mockAxios.get.mockRejectedValueOnce({
				response: { status: 500 }
			});

			// 앱 재시작 시뮬레이션
			await authActions.initialize();

			// 토큰은 로드되었지만 사용자 정보 로드 실패
			const state = get(authState);
			expect(state.accessToken).toBe(validAccessToken);
			expect(state.refreshToken).toBe(validRefreshToken);
			expect(state.user).toBeNull();
			expect(state.error?.type).toBe('SERVER_ERROR');

			// 로그인 페이지로 리다이렉트되지 않아야 함 (토큰은 유효하므로)
			expect(mockGoto).not.toHaveBeenCalledWith('/login');
		});

		it('인증 가드 함수가 올바르게 동작해야 함', async () => {
			// 비인증 상태에서 requireAuth 호출
			authState.set({
				isAuthenticated: false,
				isInitialized: true,
				isLoading: false,
				user: null,
				accessToken: null,
				refreshToken: null,
				tokenExpiresAt: null,
				error: null,
				debugInfo: null
			});

			const result = await requireAuth();
			expect(result).toBe(false);
			expect(mockGoto).toHaveBeenCalledWith('/login');
		});

		it('인증된 사용자가 로그인 페이지 접근 시 리다이렉트해야 함', async () => {
			// 인증 상태 설정
			authState.set({
				isAuthenticated: true,
				isInitialized: true,
				isLoading: false,
				user: testUser,
				accessToken: validAccessToken,
				refreshToken: validRefreshToken,
				tokenExpiresAt: null,
				error: null,
				debugInfo: null
			});

			const result = await redirectIfAuthenticated('/dashboard');
			expect(result).toBe(true);
			expect(mockGoto).toHaveBeenCalledWith('/dashboard');
		});

		it('복원 과정에서 여러 오류가 발생해도 안전하게 처리해야 함', async () => {
			// 토큰 로드는 성공하지만 형식이 잘못됨
			mockTauriStore.get.mockImplementation((key: string) => {
				if (key === 'access_token') return Promise.resolve('invalid-token-format');
				if (key === 'refresh_token') return Promise.resolve('invalid-refresh-token');
				return Promise.resolve(null);
			});

			// 앱 재시작 시뮬레이션
			await authActions.initialize();

			// 잘못된 토큰 형식으로 인해 비인증 상태가 되어야 함
			const state = get(authState);
			expect(state.isAuthenticated).toBe(false);
			expect(state.isInitialized).toBe(true);

			// 사용자 정보 로드가 시도되지 않아야 함
			expect(mockAxios.get).not.toHaveBeenCalled();
		});

		it('부분적 복원 실패 시 사용자에게 적절한 피드백을 제공해야 함', async () => {
			// 유효한 토큰 설정
			mockTauriStore.get.mockImplementation((key: string) => {
				if (key === 'access_token') return Promise.resolve(validAccessToken);
				if (key === 'refresh_token') return Promise.resolve(validRefreshToken);
				return Promise.resolve(null);
			});

			// 사용자 정보 로드 실패 (네트워크 오류)
			mockAxios.get.mockRejectedValueOnce({
				code: 'NETWORK_ERROR',
				message: 'Network Error'
			});

			// 앱 재시작 시뮬레이션
			await authActions.initialize();

			// 토큰은 복원되었지만 사용자 정보 로드 실패
			const state = get(authState);
			expect(state.accessToken).toBe(validAccessToken);
			expect(state.refreshToken).toBe(validRefreshToken);
			expect(state.user).toBeNull();
			expect(state.error?.type).toBe('SERVER_ERROR');

			// 사용자 친화적 에러 메시지 확인
			const errorMessage = authActions.getErrorMessage();
			expect(errorMessage).toBe('서버 오류가 발생했습니다. 잠시 후 다시 시도해주세요.');
		});
	});

	describe('복원 성능 및 사용자 경험', () => {
		it('복원 과정이 효율적으로 수행되어야 함', async () => {
			// 유효한 토큰 설정
			mockTauriStore.get.mockImplementation((key: string) => {
				if (key === 'access_token') return Promise.resolve(validAccessToken);
				if (key === 'refresh_token') return Promise.resolve(validRefreshToken);
				return Promise.resolve(null);
			});

			// 사용자 정보 API 응답
			mockAxios.get.mockResolvedValueOnce({
				data: {
					data: {
						user: testUser
					}
				}
			});

			const startTime = Date.now();

			// 앱 재시작 시뮬레이션
			await authActions.initialize();

			const endTime = Date.now();
			const restorationTime = endTime - startTime;

			console.log(`인증 상태 복원 시간: ${restorationTime}ms`);

			// 복원 시간이 합리적인 범위 내에 있어야 함
			expect(restorationTime).toBeLessThan(1000); // 1초 이내

			// 복원 성공 확인
			const state = get(authState);
			expect(state.isAuthenticated).toBe(true);
			expect(state.isInitialized).toBe(true);
			expect(state.user).toEqual(testUser);
		});

		it('복원 중 로딩 상태가 적절히 관리되어야 함', async () => {
			// 유효한 토큰 설정
			mockTauriStore.get.mockImplementation((key: string) => {
				if (key === 'access_token') return Promise.resolve(validAccessToken);
				if (key === 'refresh_token') return Promise.resolve(validRefreshToken);
				return Promise.resolve(null);
			});

			// 사용자 정보 API 응답 (지연 시뮬레이션)
			mockAxios.get.mockImplementation(
				() =>
					new Promise((resolve) =>
						setTimeout(
							() =>
								resolve({
									data: {
										data: {
											user: testUser
										}
									}
								}),
							100
						)
					)
			);

			// 복원 시작
			const initPromise = authActions.initialize();

			// 초기 로딩 상태 확인
			let state = get(authState);
			expect(state.isLoading).toBe(true);
			expect(state.isInitialized).toBe(false);

			// 복원 완료 대기
			await initPromise;

			// 최종 상태 확인
			state = get(authState);
			expect(state.isLoading).toBe(false);
			expect(state.isInitialized).toBe(true);
			expect(state.isAuthenticated).toBe(true);
		});

		it('복원 실패 시에도 앱이 사용 가능한 상태가 되어야 함', async () => {
			// 토큰 로드 실패
			mockTauriStore.get.mockRejectedValue(new Error('Storage error'));

			// 앱 재시작 시뮬레이션
			await authActions.initialize();

			// 실패해도 앱은 초기화 완료 상태가 되어야 함
			const state = get(authState);
			expect(state.isInitialized).toBe(true);
			expect(state.isLoading).toBe(false);
			expect(state.isAuthenticated).toBe(false);

			// 에러 상태는 기록되어야 함
			expect(state.error).toBeDefined();
		});

		it('복원 과정에서 디버그 정보가 올바르게 설정되어야 함', async () => {
			// 유효한 토큰 설정
			mockTauriStore.get.mockImplementation((key: string) => {
				if (key === 'access_token') return Promise.resolve(validAccessToken);
				if (key === 'refresh_token') return Promise.resolve(validRefreshToken);
				return Promise.resolve(null);
			});

			// 사용자 정보 API 응답
			mockAxios.get.mockResolvedValueOnce({
				data: {
					data: {
						user: testUser
					}
				}
			});

			// 앱 재시작 시뮬레이션
			await authActions.initialize();

			// 디버그 정보 확인
			const state = get(authState);
			expect(state.debugInfo).toBeDefined();
			expect(state.debugInfo?.platform).toBe('desktop');
			expect(state.debugInfo?.tokenRefreshCount).toBe(0); // 초기 복원 시에는 갱신 없음
			expect(state.debugInfo?.lastTokenRefresh).toBeNull();

			// 개발 환경에서 디버그 로그 출력 확인
			expect(console.log).toHaveBeenCalledWith('[Auth Store] 초기화 완료');
		});
	});
});
