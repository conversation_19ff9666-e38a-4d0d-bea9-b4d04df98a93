/**
 * 배치 팔레트 적재 기능 API 통합 테스트
 *
 * 이 파일은 서버 API와의 통신을 포함한 전체 시스템의 통합 테스트를 구현합니다.
 * 실제 API 호출을 모킹하여 다양한 네트워크 상황과 서버 응답을 시뮬레이션합니다.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
	addProduct,
	submitAllProducts,
	retryFailedProducts,
	clearAllProducts
} from '../services/batchProductService';
import { saveProducts, saveProduct, getPalletInfo } from '../services/palletApiService';
import { initBatchStorage } from '../utils/batchStorageUtils';
import type { BatchProductData } from '../types/batchTypes';

// authClient 모킹
vi.mock('../services/AxiosBackend', () => ({
	authClient: {
		post: vi.fn(),
		get: vi.fn(),
		put: vi.fn(),
		delete: vi.fn()
	}
}));

// 로컬스토리지 모킹
const mockLocalStorage = (() => {
	let store: Record<string, string> = {};
	return {
		getItem: (key: string) => store[key] || null,
		setItem: (key: string, value: string) => {
			store[key] = value;
		},
		removeItem: (key: string) => {
			delete store[key];
		},
		clear: () => {
			store = {};
		},
		get length() {
			return Object.keys(store).length;
		},
		key: (index: number) => Object.keys(store)[index] || null
	};
})();

Object.defineProperty(window, 'localStorage', { value: mockLocalStorage });

describe('배치 팔레트 적재 API 통합 테스트', () => {
	const testPalletId = 'A-1-1-1-1';
	let mockAuthClient: any;

	beforeEach(async () => {
		mockLocalStorage.clear();
		initBatchStorage(true);
		vi.clearAllMocks();

		// authClient 모킹 가져오기
		const axiosBackend = await import('../services/AxiosBackend');
		mockAuthClient = vi.mocked(axiosBackend.authClient);
	});

	afterEach(() => {
		mockLocalStorage.clear();
		vi.restoreAllMocks();
	});

	describe('상품 일괄 저장 API 테스트', () => {
		it('성공적인 일괄 저장 시나리오', async () => {
			// 서버 응답 모킹 - 모든 상품 성공
			mockAuthClient.post.mockResolvedValueOnce({
				data: {
					success: true,
					message: '3개 상품이 성공적으로 저장되었습니다.',
					results: [
						{ qaid: 'QA001', success: true },
						{ qaid: 'QA002', success: true },
						{ qaid: 'QA003', success: true }
					]
				}
			});

			// 테스트 상품 추가
			await addProduct('QA001', { name: '상품 1' }, testPalletId);
			await addProduct('QA002', { name: '상품 2' }, testPalletId);
			await addProduct('QA003', { name: '상품 3' }, testPalletId);

			// 일괄 저장 실행
			const result = await submitAllProducts(testPalletId);

			// 결과 검증
			expect(result.success).toBe(true);
			expect(result.successIds).toHaveLength(3);
			expect(result.failedItems).toHaveLength(0);
			expect(result.message).toContain('3개 상품이 성공적으로 저장');

			// API 호출 검증
			expect(mockAuthClient.post).toHaveBeenCalledWith('/api/pallets/products/batch', {
				products: expect.arrayContaining([
					expect.objectContaining({
						qaid: 'QA001',
						palletId: testPalletId
					}),
					expect.objectContaining({
						qaid: 'QA002',
						palletId: testPalletId
					}),
					expect.objectContaining({
						qaid: 'QA003',
						palletId: testPalletId
					})
				])
			});
		});

		it('부분 실패 시나리오', async () => {
			// 서버 응답 모킹 - 일부 상품 실패
			mockAuthClient.post.mockResolvedValueOnce({
				data: {
					success: false,
					message: '2개 성공, 1개 실패',
					results: [
						{ qaid: 'QA001', success: true },
						{ qaid: 'QA002', success: false, error: '재고 부족' },
						{ qaid: 'QA003', success: true }
					]
				}
			});

			// 테스트 상품 추가
			await addProduct('QA001', { name: '상품 1' }, testPalletId);
			await addProduct('QA002', { name: '상품 2' }, testPalletId);
			await addProduct('QA003', { name: '상품 3' }, testPalletId);

			// 일괄 저장 실행
			const result = await submitAllProducts(testPalletId);

			// 결과 검증
			expect(result.success).toBe(false);
			expect(result.successIds).toHaveLength(2);
			expect(result.failedItems).toHaveLength(1);
			expect(result.failedItems[0].qaid).toBe('QA002');
			expect(result.failedItems[0].error).toBe('재고 부족');
			expect(result.message).toContain('2개 성공, 1개 실패');
		});

		it('네트워크 오류 시나리오', async () => {
			// 네트워크 오류 모킹
			mockAuthClient.post.mockRejectedValueOnce(new Error('네트워크 연결 실패'));

			// 테스트 상품 추가
			await addProduct('QA001', { name: '상품 1' }, testPalletId);

			// 일괄 저장 실행
			const result = await submitAllProducts(testPalletId);

			// 결과 검증
			expect(result.success).toBe(false);
			expect(result.successIds).toHaveLength(0);
			expect(result.failedItems).toHaveLength(1);
			expect(result.message).toContain('네트워크 연결 실패');
			expect(result.failedItems[0].error).toContain('네트워크 연결 실패');
		});

		it('서버 내부 오류 시나리오', async () => {
			// 서버 오류 응답 모킹
			mockAuthClient.post.mockRejectedValueOnce({
				response: {
					status: 500,
					data: {
						error: '서버 내부 오류'
					}
				},
				message: '서버 내부 오류'
			});

			// 테스트 상품 추가
			await addProduct('QA001', { name: '상품 1' }, testPalletId);

			// 일괄 저장 실행
			const result = await submitAllProducts(testPalletId);

			// 결과 검증
			expect(result.success).toBe(false);
			expect(result.message).toContain('서버 내부 오류');
		});

		it('타임아웃 시나리오', async () => {
			// 타임아웃 오류 모킹
			mockAuthClient.post.mockRejectedValueOnce({
				code: 'ECONNABORTED',
				message: '요청 시간 초과'
			});

			// 테스트 상품 추가
			await addProduct('QA001', { name: '상품 1' }, testPalletId);

			// 일괄 저장 실행
			const result = await submitAllProducts(testPalletId);

			// 결과 검증
			expect(result.success).toBe(false);
			expect(result.message).toContain('요청 시간 초과');
		});
	});

	describe('개별 상품 저장 API 테스트', () => {
		it('개별 상품 저장 성공', async () => {
			// 서버 응답 모킹
			mockAuthClient.post.mockResolvedValueOnce({
				data: {
					success: true,
					message: '상품이 성공적으로 저장되었습니다.'
				}
			});

			// 테스트 상품 데이터
			const testProduct: BatchProductData = {
				id: 'test-1',
				qaid: 'QA001',
				palletId: testPalletId,
				status: 'pending',
				timestamp: Date.now(),
				productInfo: { name: '테스트 상품' }
			};

			// 개별 저장 실행
			const result = await saveProduct(testProduct);

			// 결과 검증
			expect(result.success).toBe(true);
			expect(result.message).toContain('성공적으로 저장');

			// API 호출 검증
			expect(mockAuthClient.post).toHaveBeenCalledWith('/api/pallets/products', {
				qaid: 'QA001',
				palletId: testPalletId,
				productInfo: { name: '테스트 상품' }
			});
		});

		it('개별 상품 저장 실패', async () => {
			// 서버 오류 응답 모킹
			mockAuthClient.post.mockRejectedValueOnce(new Error('상품 검증 실패'));

			// 테스트 상품 데이터
			const testProduct: BatchProductData = {
				id: 'test-1',
				qaid: 'INVALID',
				palletId: testPalletId,
				status: 'pending',
				timestamp: Date.now()
			};

			// 개별 저장 실행
			const result = await saveProduct(testProduct);

			// 결과 검증
			expect(result.success).toBe(false);
			expect(result.message).toBe('상품 저장 실패');
			expect(result.error).toContain('상품 검증 실패');
		});
	});

	describe('팔레트 정보 조회 API 테스트', () => {
		it('팔레트 정보 조회 성공', async () => {
			// 서버 응답 모킹
			const mockPalletData = {
				id: testPalletId,
				name: '테스트 팔레트',
				status: 'active',
				products: []
			};

			mockAuthClient.get.mockResolvedValueOnce({
				data: mockPalletData
			});

			// 팔레트 정보 조회 실행
			const result = await getPalletInfo(testPalletId);

			// 결과 검증
			expect(result.success).toBe(true);
			expect(result.data).toEqual(mockPalletData);

			// API 호출 검증
			expect(mockAuthClient.get).toHaveBeenCalledWith(`/api/pallets/${testPalletId}`);
		});

		it('팔레트 정보 조회 실패', async () => {
			// 서버 오류 응답 모킹
			mockAuthClient.get.mockRejectedValueOnce(new Error('팔레트를 찾을 수 없습니다'));

			// 팔레트 정보 조회 실행
			const result = await getPalletInfo('INVALID_PALLET');

			// 결과 검증
			expect(result.success).toBe(false);
			expect(result.error).toContain('팔레트를 찾을 수 없습니다');
		});
	});

	describe('실패 상품 재시도 테스트', () => {
		it('실패 상품 재시도 성공', async () => {
			// 첫 번째 호출 - 일부 실패
			mockAuthClient.post.mockResolvedValueOnce({
				data: {
					success: false,
					message: '1개 성공, 1개 실패',
					results: [
						{ qaid: 'QA001', success: true },
						{ qaid: 'QA002', success: false, error: '일시적 오류' }
					]
				}
			});

			// 두 번째 호출 (재시도) - 성공
			mockAuthClient.post.mockResolvedValueOnce({
				data: {
					success: true,
					message: '1개 상품이 성공적으로 저장되었습니다.',
					results: [{ qaid: 'QA002', success: true }]
				}
			});

			// 테스트 상품 추가
			await addProduct('QA001', { name: '상품 1' }, testPalletId);
			await addProduct('QA002', { name: '상품 2' }, testPalletId);

			// 첫 번째 저장 시도
			const firstResult = await submitAllProducts(testPalletId);
			expect(firstResult.success).toBe(false);
			expect(firstResult.failedItems).toHaveLength(1);

			// 실패 상품 재시도
			const retryResult = await retryFailedProducts(testPalletId);
			expect(retryResult.success).toBe(true);
			expect(retryResult.successIds).toHaveLength(1);

			// API가 두 번 호출되었는지 확인
			expect(mockAuthClient.post).toHaveBeenCalledTimes(2);
		});

		it('재시도에서도 실패하는 경우', async () => {
			// 첫 번째 호출 - 실패
			mockAuthClient.post.mockResolvedValueOnce({
				data: {
					success: false,
					message: '저장 실패',
					results: [{ qaid: 'QA001', success: false, error: '영구적 오류' }]
				}
			});

			// 두 번째 호출 (재시도) - 여전히 실패
			mockAuthClient.post.mockResolvedValueOnce({
				data: {
					success: false,
					message: '저장 실패',
					results: [{ qaid: 'QA001', success: false, error: '영구적 오류' }]
				}
			});

			// 테스트 상품 추가
			await addProduct('QA001', { name: '문제 상품' }, testPalletId);

			// 첫 번째 저장 시도
			const firstResult = await submitAllProducts(testPalletId);
			expect(firstResult.success).toBe(false);

			// 실패 상품 재시도
			const retryResult = await retryFailedProducts(testPalletId);
			expect(retryResult.success).toBe(false);
			expect(retryResult.failedItems).toHaveLength(1);
			expect(retryResult.failedItems[0].error).toBe('영구적 오류');
		});
	});

	describe('대량 데이터 처리 성능 테스트', () => {
		it('대량 상품 일괄 저장 성능', async () => {
			const productCount = 100;

			// 서버 응답 모킹 - 모든 상품 성공
			const mockResults = Array.from({ length: productCount }, (_, i) => ({
				qaid: `QA${(i + 1).toString().padStart(3, '0')}`,
				success: true
			}));

			mockAuthClient.post.mockResolvedValueOnce({
				data: {
					success: true,
					message: `${productCount}개 상품이 성공적으로 저장되었습니다.`,
					results: mockResults
				}
			});

			// 대량 상품 추가
			for (let i = 1; i <= productCount; i++) {
				await addProduct(`QA${i.toString().padStart(3, '0')}`, { name: `상품 ${i}` }, testPalletId);
			}

			// 일괄 저장 실행 및 시간 측정
			const startTime = Date.now();
			const result = await submitAllProducts(testPalletId);
			const endTime = Date.now();

			// 결과 검증
			expect(result.success).toBe(true);
			expect(result.successIds).toHaveLength(productCount);

			// 성능 검증 (5초 이내)
			const processingTime = endTime - startTime;
			expect(processingTime).toBeLessThan(5000);

			console.log(`${productCount}개 상품 API 처리 시간: ${processingTime}ms`);

			// API가 한 번만 호출되었는지 확인 (일괄 처리)
			expect(mockAuthClient.post).toHaveBeenCalledTimes(1);

			// 전송된 데이터 크기 확인
			const sentData = mockAuthClient.post.mock.calls[0][1];
			expect(sentData.products).toHaveLength(productCount);
		});

		it('네트워크 지연 상황에서의 처리', async () => {
			// 지연된 응답 시뮬레이션
			mockAuthClient.post.mockImplementationOnce(
				() =>
					new Promise((resolve) => {
						setTimeout(() => {
							resolve({
								data: {
									success: true,
									message: '지연된 저장 성공',
									results: [{ qaid: 'QA001', success: true }]
								}
							});
						}, 1000); // 1초 지연
					})
			);

			// 테스트 상품 추가
			await addProduct('QA001', { name: '지연 테스트 상품' }, testPalletId);

			// 저장 실행 및 시간 측정
			const startTime = Date.now();
			const result = await submitAllProducts(testPalletId);
			const endTime = Date.now();

			// 결과 검증
			expect(result.success).toBe(true);

			// 지연 시간 확인 (최소 1초)
			const processingTime = endTime - startTime;
			expect(processingTime).toBeGreaterThanOrEqual(1000);

			console.log(`네트워크 지연 처리 시간: ${processingTime}ms`);
		});
	});

	describe('동시성 및 경쟁 상태 테스트', () => {
		it('동시 저장 요청 처리', async () => {
			// 각 요청에 대한 응답 모킹
			mockAuthClient.post
				.mockResolvedValueOnce({
					data: {
						success: true,
						message: '첫 번째 저장 성공',
						results: [{ qaid: 'QA001', success: true }]
					}
				})
				.mockResolvedValueOnce({
					data: {
						success: true,
						message: '두 번째 저장 성공',
						results: [{ qaid: 'QA002', success: true }]
					}
				});

			// 서로 다른 팔레트에 상품 추가
			await addProduct('QA001', { name: '상품 1' }, 'A-1-1-1-1');
			await addProduct('QA002', { name: '상품 2' }, 'A-1-1-1-2');

			// 동시 저장 요청
			const [result1, result2] = await Promise.all([
				submitAllProducts('A-1-1-1-1'),
				submitAllProducts('A-1-1-1-2')
			]);

			// 결과 검증
			expect(result1.success).toBe(true);
			expect(result2.success).toBe(true);

			// 두 번의 API 호출이 발생했는지 확인
			expect(mockAuthClient.post).toHaveBeenCalledTimes(2);
		});

		it('같은 팔레트에 대한 중복 저장 요청 방지', async () => {
			// 지연된 응답으로 경쟁 상태 시뮬레이션
			let resolveFirst: (value: any) => void;
			const firstPromise = new Promise((resolve) => {
				resolveFirst = resolve;
			});

			mockAuthClient.post.mockReturnValueOnce(firstPromise);

			// 테스트 상품 추가
			await addProduct('QA001', { name: '상품 1' }, testPalletId);

			// 첫 번째 저장 요청 시작
			const firstSavePromise = submitAllProducts(testPalletId);

			// 두 번째 저장 요청 (동시)
			const secondSavePromise = submitAllProducts(testPalletId);

			// 첫 번째 요청 완료
			resolveFirst!({
				data: {
					success: true,
					message: '저장 성공',
					results: [{ qaid: 'QA001', success: true }]
				}
			});

			// 결과 대기
			const [firstResult, secondResult] = await Promise.all([firstSavePromise, secondSavePromise]);

			// 첫 번째 요청만 성공하고, 두 번째는 중복 요청으로 처리되어야 함
			expect(firstResult.success).toBe(true);
			// 실제 구현에서는 중복 요청 방지 로직이 있어야 함

			// API가 한 번만 호출되었는지 확인
			expect(mockAuthClient.post).toHaveBeenCalledTimes(1);
		});
	});

	describe('오류 복구 및 재시도 전략 테스트', () => {
		it('일시적 네트워크 오류 후 자동 복구', async () => {
			// 첫 번째 호출 - 네트워크 오류
			mockAuthClient.post.mockRejectedValueOnce(new Error('ECONNRESET'));

			// 두 번째 호출 - 성공
			mockAuthClient.post.mockResolvedValueOnce({
				data: {
					success: true,
					message: '재시도 성공',
					results: [{ qaid: 'QA001', success: true }]
				}
			});

			// 테스트 상품 추가
			await addProduct('QA001', { name: '네트워크 테스트 상품' }, testPalletId);

			// 첫 번째 저장 시도 (실패)
			const firstResult = await submitAllProducts(testPalletId);
			expect(firstResult.success).toBe(false);

			// 재시도 (성공)
			const retryResult = await retryFailedProducts(testPalletId);
			expect(retryResult.success).toBe(true);
		});

		it('서버 과부하 상황에서의 처리', async () => {
			// 서버 과부하 응답 모킹
			mockAuthClient.post.mockRejectedValueOnce({
				response: {
					status: 503,
					data: {
						error: 'Service Unavailable'
					}
				},
				message: '서비스를 사용할 수 없습니다'
			});

			// 테스트 상품 추가
			await addProduct('QA001', { name: '과부하 테스트 상품' }, testPalletId);

			// 저장 시도
			const result = await submitAllProducts(testPalletId);

			// 결과 검증
			expect(result.success).toBe(false);
			expect(result.message).toContain('서비스를 사용할 수 없습니다');
		});
	});

	describe('데이터 무결성 테스트', () => {
		it('전송 데이터 형식 검증', async () => {
			// 서버 응답 모킹
			mockAuthClient.post.mockResolvedValueOnce({
				data: {
					success: true,
					message: '저장 성공',
					results: [{ qaid: 'QA001', success: true }]
				}
			});

			// 복잡한 상품 정보를 가진 상품 추가
			await addProduct(
				'QA001',
				{
					name: '복잡한 상품',
					description: '상세 설명',
					category: 'A급',
					weight: 1.5,
					dimensions: { width: 10, height: 20, depth: 30 },
					metadata: {
						scannedAt: new Date().toISOString(),
						operator: 'test-user'
					}
				},
				testPalletId
			);

			// 저장 실행
			const result = await submitAllProducts(testPalletId);

			// 결과 검증
			expect(result.success).toBe(true);

			// 전송된 데이터 형식 검증
			const sentData = mockAuthClient.post.mock.calls[0][1];
			expect(sentData.products[0]).toEqual({
				qaid: 'QA001',
				palletId: testPalletId,
				productInfo: expect.objectContaining({
					name: '복잡한 상품',
					description: '상세 설명',
					category: 'A급',
					weight: 1.5,
					dimensions: expect.objectContaining({
						width: 10,
						height: 20,
						depth: 30
					}),
					metadata: expect.objectContaining({
						scannedAt: expect.any(String),
						operator: 'test-user'
					})
				})
			});
		});

		it('특수 문자 및 유니코드 처리', async () => {
			// 서버 응답 모킹
			mockAuthClient.post.mockResolvedValueOnce({
				data: {
					success: true,
					message: '저장 성공',
					results: [{ qaid: 'QA001', success: true }]
				}
			});

			// 특수 문자가 포함된 상품 추가
			await addProduct(
				'QA001',
				{
					name: '특수문자 테스트 & < > " \' 상품',
					description: '한글 설명 및 이모지 🚀 테스트',
					notes: 'JSON 특수문자: {"key": "value"}'
				},
				testPalletId
			);

			// 저장 실행
			const result = await submitAllProducts(testPalletId);

			// 결과 검증
			expect(result.success).toBe(true);

			// 특수 문자가 올바르게 전송되었는지 확인
			const sentData = mockAuthClient.post.mock.calls[0][1];
			expect(sentData.products[0].productInfo.name).toBe('특수문자 테스트 & < > " \' 상품');
			expect(sentData.products[0].productInfo.description).toBe('한글 설명 및 이모지 🚀 테스트');
		});
	});
});
