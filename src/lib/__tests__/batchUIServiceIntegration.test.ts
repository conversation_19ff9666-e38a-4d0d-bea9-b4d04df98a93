/**
 * 배치 팔레트 적재 UI-서비스 통합 테스트
 *
 * 이 파일은 UI 컴포넌트와 서비스 레이어 간의 전체적인 통합을 테스트합니다.
 * 실제 사용자 워크플로우를 시뮬레이션하여 전체 시스템의 동작을 검증합니다.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/svelte';
import BatchStatusIndicator from '../components/BatchProcessing/BatchStatusIndicator.svelte';
import BatchSaveButton from '../components/BatchProcessing/BatchSaveButton.svelte';
import BatchProductList from '../components/BatchProcessing/BatchProductList.svelte';
import BatchErrorList from '../components/BatchProcessing/BatchErrorList.svelte';
import BatchStorageWarning from '../components/BatchProcessing/BatchStorageWarning.svelte';
import {
	addProduct,
	updateProduct,
	removeProduct,
	submitAllProducts,
	retryFailedProducts,
	clearAllProducts,
	getAllProducts,
	getProductsByStatus
} from '../services/batchProductService';
import { initBatchStorage, checkStorageCapacity } from '../utils/batchStorageUtils';

// 로컬스토리지 모킹
const mockLocalStorage = (() => {
	let store: Record<string, string> = {};
	return {
		getItem: (key: string) => store[key] || null,
		setItem: (key: string, value: string) => {
			store[key] = value;
		},
		removeItem: (key: string) => {
			delete store[key];
		},
		clear: () => {
			store = {};
		},
		get length() {
			return Object.keys(store).length;
		},
		key: (index: number) => Object.keys(store)[index] || null
	};
})();

Object.defineProperty(window, 'localStorage', { value: mockLocalStorage });

// palletApiService 모킹
const mockSaveProducts = vi.fn();
vi.mock('../services/palletApiService', () => ({
	saveProducts: mockSaveProducts
}));

// confirm 모킹
Object.defineProperty(window, 'confirm', {
	value: vi.fn(() => true)
});

describe('배치 팔레트 적재 UI-서비스 통합 테스트', () => {
	const testPalletId = 'A-1-1-1-1';

	beforeEach(() => {
		mockLocalStorage.clear();
		initBatchStorage(true);
		vi.clearAllMocks();
		vi.mocked(window.confirm).mockReturnValue(true);
		mockSaveProducts.mockResolvedValue({
			success: true,
			message: '저장 성공',
			successIds: ['1', '2', '3'],
			failedItems: []
		});
	});

	afterEach(() => {
		mockLocalStorage.clear();
		vi.restoreAllMocks();
	});

	describe('전체 워크플로우 통합 테스트', () => {
		it('상품 스캔부터 저장까지 전체 프로세스', async () => {
			// 1. 초기 상태 - 모든 컴포넌트 렌더링
			const statusIndicator = render(BatchStatusIndicator, {
				props: { palletId: testPalletId, autoRefresh: true, refreshInterval: 100 }
			});

			const saveButton = render(BatchSaveButton, {
				props: { palletId: testPalletId }
			});

			const productList = render(BatchProductList, {
				props: { palletId: testPalletId }
			});

			// 초기 상태 확인
			expect(screen.getByText('저장된 상품이 없습니다')).toBeInTheDocument();
			expect(screen.getByText('저장할 상품 없음')).toBeInTheDocument();

			// 2. 상품 스캔 시뮬레이션
			await addProduct('QA001', { name: '첫 번째 상품' }, testPalletId);
			await addProduct('QA002', { name: '두 번째 상품' }, testPalletId);
			await addProduct('QA003', { name: '세 번째 상품' }, testPalletId);

			// 3. 상태 업데이트 확인
			await waitFor(() => {
				expect(screen.getByText(/3개 상품이 임시 저장됨/)).toBeInTheDocument();
				expect(screen.getByText('일괄 저장 (3개)')).toBeInTheDocument();
				expect(screen.getByText('QA001')).toBeInTheDocument();
				expect(screen.getByText('QA002')).toBeInTheDocument();
				expect(screen.getByText('QA003')).toBeInTheDocument();
			});

			// 4. 일괄 저장 실행
			const saveButtonElement = screen.getByText('일괄 저장 (3개)');
			await fireEvent.click(saveButtonElement);

			// 5. 저장 완료 상태 확인
			await waitFor(
				() => {
					expect(screen.getByText(/3개 상품 저장 완료/)).toBeInTheDocument();
					expect(screen.getByText('저장 완료')).toBeInTheDocument();
				},
				{ timeout: 2000 }
			);

			// 6. API 호출 확인
			expect(mockSaveProducts).toHaveBeenCalledWith(
				expect.arrayContaining([
					expect.objectContaining({ qaid: 'QA001' }),
					expect.objectContaining({ qaid: 'QA002' }),
					expect.objectContaining({ qaid: 'QA003' })
				])
			);

			// 컴포넌트 정리
			statusIndicator.unmount();
			saveButton.unmount();
			productList.unmount();
		});

		it('부분 실패 시나리오에서 UI 연동', async () => {
			// 부분 실패 응답 모킹
			mockSaveProducts.mockResolvedValueOnce({
				success: false,
				message: '2개 성공, 1개 실패',
				successIds: ['1', '2'],
				failedItems: [{ id: '3', qaid: 'QA003', error: '재고 부족' }]
			});

			// 컴포넌트 렌더링
			const statusIndicator = render(BatchStatusIndicator, {
				props: { palletId: testPalletId, autoRefresh: true, refreshInterval: 100 }
			});

			const saveButton = render(BatchSaveButton, {
				props: { palletId: testPalletId }
			});

			const errorList = render(BatchErrorList, {
				props: { palletId: testPalletId, autoRefresh: true, refreshInterval: 100 }
			});

			// 상품 추가
			await addProduct('QA001', { name: '성공 상품 1' }, testPalletId);
			await addProduct('QA002', { name: '성공 상품 2' }, testPalletId);
			await addProduct('QA003', { name: '실패 상품' }, testPalletId);

			// 일괄 저장 실행
			const saveButtonElement = screen.getByText('일괄 저장 (3개)');
			await fireEvent.click(saveButtonElement);

			// 부분 실패 상태 확인
			await waitFor(
				() => {
					expect(screen.getByText('2개 성공, 1개 실패')).toBeInTheDocument();
					expect(screen.getByText(/실패.*1개/)).toBeInTheDocument();
				},
				{ timeout: 2000 }
			);

			// 오류 목록 확인
			await waitFor(() => {
				expect(screen.getByText('QA003')).toBeInTheDocument();
				expect(screen.getByText('재고 부족')).toBeInTheDocument();
			});

			// 재시도 버튼 확인
			expect(screen.getByText('재시도')).toBeInTheDocument();

			// 컴포넌트 정리
			statusIndicator.unmount();
			saveButton.unmount();
			errorList.unmount();
		});

		it('재시도 기능 UI 연동', async () => {
			// 첫 번째 호출 - 실패
			mockSaveProducts.mockResolvedValueOnce({
				success: false,
				message: '저장 실패',
				successIds: [],
				failedItems: [{ id: '1', qaid: 'QA001', error: '일시적 오류' }]
			});

			// 두 번째 호출 (재시도) - 성공
			mockSaveProducts.mockResolvedValueOnce({
				success: true,
				message: '재시도 성공',
				successIds: ['1'],
				failedItems: []
			});

			// 컴포넌트 렌더링
			const statusIndicator = render(BatchStatusIndicator, {
				props: { palletId: testPalletId, autoRefresh: true, refreshInterval: 100 }
			});

			const errorList = render(BatchErrorList, {
				props: { palletId: testPalletId, autoRefresh: true, refreshInterval: 100 }
			});

			// 상품 추가 및 첫 번째 저장 시도
			await addProduct('QA001', { name: '재시도 테스트 상품' }, testPalletId);
			await submitAllProducts(testPalletId);

			// 실패 상태 확인
			await waitFor(() => {
				expect(screen.getByText('QA001')).toBeInTheDocument();
				expect(screen.getByText('일시적 오류')).toBeInTheDocument();
			});

			// 재시도 버튼 클릭
			const retryButton = screen.getByText('재시도');
			await fireEvent.click(retryButton);

			// 재시도 성공 확인
			await waitFor(
				() => {
					expect(screen.getByText(/1개 상품 저장 완료/)).toBeInTheDocument();
				},
				{ timeout: 2000 }
			);

			// API가 두 번 호출되었는지 확인
			expect(mockSaveProducts).toHaveBeenCalledTimes(2);

			// 컴포넌트 정리
			statusIndicator.unmount();
			errorList.unmount();
		});
	});

	describe('다중 컴포넌트 상호작용 테스트', () => {
		it('상품 목록에서 삭제 시 상태 표시기 업데이트', async () => {
			// 컴포넌트 렌더링
			const statusIndicator = render(BatchStatusIndicator, {
				props: { palletId: testPalletId, autoRefresh: true, refreshInterval: 100 }
			});

			const productList = render(BatchProductList, {
				props: { palletId: testPalletId, showActions: true }
			});

			// 상품 추가
			await addProduct('QA001', { name: '삭제 테스트 상품 1' }, testPalletId);
			await addProduct('QA002', { name: '삭제 테스트 상품 2' }, testPalletId);
			await addProduct('QA003', { name: '삭제 테스트 상품 3' }, testPalletId);

			// 초기 상태 확인
			await waitFor(() => {
				expect(screen.getByText(/3개 상품이 임시 저장됨/)).toBeInTheDocument();
				expect(screen.getByText('3개')).toBeInTheDocument(); // 상품 목록 헤더
			});

			// 전체 삭제 실행
			const clearAllButton = screen.getByText('전체 삭제');
			await fireEvent.click(clearAllButton);

			// 삭제 후 상태 업데이트 확인
			await waitFor(() => {
				expect(screen.getByText('저장된 상품이 없습니다')).toBeInTheDocument();
				expect(screen.getByText('0개')).toBeInTheDocument();
			});

			// 컴포넌트 정리
			statusIndicator.unmount();
			productList.unmount();
		});

		it('저장 버튼과 상태 표시기 실시간 연동', async () => {
			// 지연된 저장 응답 시뮬레이션
			let resolvePromise: (value: any) => void;
			const delayedPromise = new Promise((resolve) => {
				resolvePromise = resolve;
			});
			mockSaveProducts.mockReturnValueOnce(delayedPromise);

			// 컴포넌트 렌더링
			const statusIndicator = render(BatchStatusIndicator, {
				props: { palletId: testPalletId, autoRefresh: true, refreshInterval: 50 }
			});

			const saveButton = render(BatchSaveButton, {
				props: { palletId: testPalletId }
			});

			// 상품 추가
			await addProduct('QA001', { name: '지연 테스트 상품' }, testPalletId);

			// 저장 버튼 클릭
			const saveButtonElement = screen.getByText('일괄 저장 (1개)');
			await fireEvent.click(saveButtonElement);

			// 저장 중 상태 확인
			await waitFor(() => {
				expect(screen.getByText(/저장 중/)).toBeInTheDocument();
				expect(screen.getByText(/전송 중/)).toBeInTheDocument();
			});

			// 저장 완료
			resolvePromise!({
				success: true,
				message: '저장 성공',
				successIds: ['1'],
				failedItems: []
			});

			// 완료 상태 확인
			await waitFor(
				() => {
					expect(screen.getByText('저장 완료')).toBeInTheDocument();
					expect(screen.getByText(/1개 상품 저장 완료/)).toBeInTheDocument();
				},
				{ timeout: 2000 }
			);

			// 컴포넌트 정리
			statusIndicator.unmount();
			saveButton.unmount();
		});

		it('스토리지 경고와 다른 컴포넌트 연동', async () => {
			// 컴포넌트 렌더링
			const statusIndicator = render(BatchStatusIndicator, {
				props: { palletId: testPalletId, autoRefresh: true, refreshInterval: 100 }
			});

			const storageWarning = render(BatchStorageWarning, {
				props: { autoRefresh: true, refreshInterval: 100 }
			});

			const saveButton = render(BatchSaveButton, {
				props: { palletId: testPalletId }
			});

			// 대량 상품 추가 (스토리지 경고 유발)
			for (let i = 1; i <= 150; i++) {
				await addProduct(`QA${i.toString().padStart(3, '0')}`, { name: `상품 ${i}` }, testPalletId);
			}

			// 스토리지 경고 확인
			await waitFor(() => {
				expect(screen.getByText(/150개의 상품이 임시 저장/)).toBeInTheDocument();
				expect(screen.getByText(/자동 저장을 권장/)).toBeInTheDocument();
			});

			// 권장 저장 버튼 클릭
			const recommendedSaveButton = screen.getByText('지금 저장');
			await fireEvent.click(recommendedSaveButton);

			// 저장 완료 후 경고 해제 확인
			await waitFor(
				() => {
					expect(screen.getByText(/150개 상품 저장 완료/)).toBeInTheDocument();
				},
				{ timeout: 2000 }
			);

			// 컴포넌트 정리
			statusIndicator.unmount();
			storageWarning.unmount();
			saveButton.unmount();
		});
	});

	describe('팔레트 전환 시 UI 연동 테스트', () => {
		it('팔레트 전환 시 모든 컴포넌트 업데이트', async () => {
			const pallet1 = 'A-1-1-1-1';
			const pallet2 = 'A-1-1-1-2';

			// 각 팔레트에 상품 추가
			await addProduct('QA001', { name: '팔레트1 상품1' }, pallet1);
			await addProduct('QA002', { name: '팔레트1 상품2' }, pallet1);
			await addProduct('QA003', { name: '팔레트2 상품1' }, pallet2);

			// 팔레트1 컴포넌트 렌더링
			const statusIndicator = render(BatchStatusIndicator, {
				props: { palletId: pallet1, autoRefresh: true, refreshInterval: 100 }
			});

			const productList = render(BatchProductList, {
				props: { palletId: pallet1 }
			});

			const saveButton = render(BatchSaveButton, {
				props: { palletId: pallet1 }
			});

			// 팔레트1 상태 확인
			await waitFor(() => {
				expect(screen.getByText(/2개 상품이 임시 저장됨/)).toBeInTheDocument();
				expect(screen.getByText(`(${pallet1})`)).toBeInTheDocument();
				expect(screen.getByText('QA001')).toBeInTheDocument();
				expect(screen.getByText('QA002')).toBeInTheDocument();
				expect(screen.getByText('일괄 저장 (2개)')).toBeInTheDocument();
			});

			// 팔레트2로 전환
			statusIndicator.component.$set({ palletId: pallet2 });
			productList.component.$set({ palletId: pallet2 });
			saveButton.component.$set({ palletId: pallet2 });

			// 팔레트2 상태 확인
			await waitFor(() => {
				expect(screen.getByText(/1개 상품이 임시 저장됨/)).toBeInTheDocument();
				expect(screen.getByText(`(${pallet2})`)).toBeInTheDocument();
				expect(screen.getByText('QA003')).toBeInTheDocument();
				expect(screen.queryByText('QA001')).not.toBeInTheDocument();
				expect(screen.queryByText('QA002')).not.toBeInTheDocument();
				expect(screen.getByText('일괄 저장 (1개)')).toBeInTheDocument();
			});

			// 컴포넌트 정리
			statusIndicator.unmount();
			productList.unmount();
			saveButton.unmount();
		});

		it('빈 팔레트로 전환 시 UI 상태', async () => {
			const existingPallet = 'A-1-1-1-1';
			const emptyPallet = 'A-1-1-1-2';

			// 기존 팔레트에 상품 추가
			await addProduct('QA001', { name: '기존 상품' }, existingPallet);

			// 컴포넌트 렌더링 (기존 팔레트)
			const statusIndicator = render(BatchStatusIndicator, {
				props: { palletId: existingPallet, autoRefresh: true, refreshInterval: 100 }
			});

			const productList = render(BatchProductList, {
				props: { palletId: existingPallet }
			});

			const saveButton = render(BatchSaveButton, {
				props: { palletId: existingPallet }
			});

			// 기존 팔레트 상태 확인
			await waitFor(() => {
				expect(screen.getByText(/1개 상품이 임시 저장됨/)).toBeInTheDocument();
				expect(screen.getByText('QA001')).toBeInTheDocument();
			});

			// 빈 팔레트로 전환
			statusIndicator.component.$set({ palletId: emptyPallet });
			productList.component.$set({ palletId: emptyPallet });
			saveButton.component.$set({ palletId: emptyPallet });

			// 빈 팔레트 상태 확인
			await waitFor(() => {
				expect(screen.getByText('저장된 상품이 없습니다')).toBeInTheDocument();
				expect(screen.getByText('저장할 상품 없음')).toBeInTheDocument();
				expect(screen.queryByText('QA001')).not.toBeInTheDocument();
			});

			// 컴포넌트 정리
			statusIndicator.unmount();
			productList.unmount();
			saveButton.unmount();
		});
	});

	describe('오류 상황에서의 UI 연동 테스트', () => {
		it('서비스 오류 시 모든 컴포넌트 안전 처리', async () => {
			const originalConsoleError = console.error;
			console.error = vi.fn();

			// 서비스 함수가 오류를 발생시키도록 모킹
			vi.doMock('../services/batchProductService', () => ({
				getAllProducts: vi.fn().mockImplementation(() => {
					throw new Error('서비스 오류');
				}),
				getProductCount: vi.fn().mockImplementation(() => {
					throw new Error('서비스 오류');
				}),
				submitAllProducts: vi.fn().mockImplementation(() => {
					throw new Error('서비스 오류');
				})
			}));

			// 컴포넌트 렌더링
			const statusIndicator = render(BatchStatusIndicator, {
				props: { palletId: testPalletId }
			});

			const productList = render(BatchProductList, {
				props: { palletId: testPalletId }
			});

			const saveButton = render(BatchSaveButton, {
				props: { palletId: testPalletId }
			});

			// 오류가 발생해도 컴포넌트가 정상적으로 렌더링되어야 함
			expect(screen.getByText('배치 상태')).toBeInTheDocument();
			expect(screen.getByText('상품 목록')).toBeInTheDocument();

			// 컴포넌트 정리
			statusIndicator.unmount();
			productList.unmount();
			saveButton.unmount();

			console.error = originalConsoleError;
		});

		it('네트워크 오류 시 UI 피드백', async () => {
			// 네트워크 오류 모킹
			mockSaveProducts.mockRejectedValueOnce(new Error('네트워크 연결 실패'));

			// 컴포넌트 렌더링
			const statusIndicator = render(BatchStatusIndicator, {
				props: { palletId: testPalletId, autoRefresh: true, refreshInterval: 100 }
			});

			const saveButton = render(BatchSaveButton, {
				props: { palletId: testPalletId }
			});

			const errorList = render(BatchErrorList, {
				props: { palletId: testPalletId, autoRefresh: true, refreshInterval: 100 }
			});

			// 상품 추가 및 저장 시도
			await addProduct('QA001', { name: '네트워크 테스트 상품' }, testPalletId);

			const saveButtonElement = screen.getByText('일괄 저장 (1개)');
			await fireEvent.click(saveButtonElement);

			// 네트워크 오류 피드백 확인
			await waitFor(
				() => {
					expect(screen.getByText('저장 실패')).toBeInTheDocument();
					expect(screen.getByText('네트워크 연결 실패')).toBeInTheDocument();
				},
				{ timeout: 2000 }
			);

			// 오류 목록에 표시 확인
			await waitFor(() => {
				expect(screen.getByText('QA001')).toBeInTheDocument();
				expect(screen.getByText('네트워크 연결 실패')).toBeInTheDocument();
			});

			// 컴포넌트 정리
			statusIndicator.unmount();
			saveButton.unmount();
			errorList.unmount();
		});
	});

	describe('성능 및 메모리 관리 테스트', () => {
		it('대량 데이터 처리 시 UI 성능', async () => {
			const productCount = 500;

			// 컴포넌트 렌더링
			const statusIndicator = render(BatchStatusIndicator, {
				props: { palletId: testPalletId, autoRefresh: true, refreshInterval: 200 }
			});

			const productList = render(BatchProductList, {
				props: { palletId: testPalletId, itemsPerPage: 50 }
			});

			// 대량 상품 추가 시간 측정
			const startTime = Date.now();

			for (let i = 1; i <= productCount; i++) {
				await addProduct(`QA${i.toString().padStart(3, '0')}`, { name: `상품 ${i}` }, testPalletId);
			}

			const addTime = Date.now() - startTime;

			// UI 업데이트 확인
			await waitFor(
				() => {
					expect(screen.getByText(`${productCount}개`)).toBeInTheDocument();
					expect(screen.getByText(/500개 상품이 임시 저장됨/)).toBeInTheDocument();
				},
				{ timeout: 5000 }
			);

			const totalTime = Date.now() - startTime;

			console.log(`${productCount}개 상품 추가 시간: ${addTime}ms`);
			console.log(`UI 업데이트 포함 총 시간: ${totalTime}ms`);

			// 성능 검증 (10초 이내)
			expect(totalTime).toBeLessThan(10000);

			// 페이지네이션으로 인해 실제로는 50개만 렌더링되어야 함
			const productElements = screen.getAllByText(/^QA\d{3}$/);
			expect(productElements.length).toBeLessThanOrEqual(50);

			// 컴포넌트 정리
			statusIndicator.unmount();
			productList.unmount();
		});

		it('컴포넌트 언마운트 시 메모리 정리', async () => {
			// 자동 새로고침이 활성화된 컴포넌트 렌더링
			const statusIndicator = render(BatchStatusIndicator, {
				props: { palletId: testPalletId, autoRefresh: true, refreshInterval: 50 }
			});

			const storageWarning = render(BatchStorageWarning, {
				props: { autoRefresh: true, refreshInterval: 50 }
			});

			// 상품 추가
			await addProduct('QA001', { name: '메모리 테스트 상품' }, testPalletId);

			// 잠시 대기하여 타이머가 실행되도록 함
			await new Promise((resolve) => setTimeout(resolve, 200));

			// 컴포넌트 언마운트
			statusIndicator.unmount();
			storageWarning.unmount();

			// 언마운트 후에도 오류가 발생하지 않아야 함
			await new Promise((resolve) => setTimeout(resolve, 200));

			// 메모리 누수 검사는 실제로는 더 정교한 도구가 필요하지만,
			// 여기서는 언마운트가 성공적으로 완료되었는지만 확인
			expect(true).toBe(true);
		});
	});

	describe('접근성 및 사용성 테스트', () => {
		it('키보드 네비게이션 전체 워크플로우', async () => {
			// 컴포넌트 렌더링
			const productList = render(BatchProductList, {
				props: { palletId: testPalletId, showActions: true }
			});

			const saveButton = render(BatchSaveButton, {
				props: { palletId: testPalletId }
			});

			// 상품 추가
			await addProduct('QA001', { name: '키보드 테스트 상품' }, testPalletId);

			// 저장 버튼에 포커스
			const saveButtonElement = screen.getByText('일괄 저장 (1개)');
			saveButtonElement.focus();
			expect(document.activeElement).toBe(saveButtonElement);

			// Enter 키로 저장 실행
			await fireEvent.keyDown(saveButtonElement, { key: 'Enter' });

			// 저장 완료 확인
			await waitFor(
				() => {
					expect(screen.getByText('저장 완료')).toBeInTheDocument();
				},
				{ timeout: 2000 }
			);

			// 컴포넌트 정리
			productList.unmount();
			saveButton.unmount();
		});

		it('스크린 리더 지원 확인', async () => {
			// 컴포넌트 렌더링
			const statusIndicator = render(BatchStatusIndicator, {
				props: { palletId: testPalletId }
			});

			const productList = render(BatchProductList, {
				props: { palletId: testPalletId }
			});

			// 상품 추가
			await addProduct('QA001', { name: '접근성 테스트 상품' }, testPalletId);

			// ARIA 레이블 및 역할 확인
			expect(screen.getByRole('heading', { name: /배치 상태/ })).toBeInTheDocument();
			expect(screen.getByRole('heading', { name: /상품 목록/ })).toBeInTheDocument();

			// 버튼에 적절한 title 속성 확인
			const refreshButton = screen.getByTitle('상태 새로고침');
			expect(refreshButton).toBeInTheDocument();

			// 컴포넌트 정리
			statusIndicator.unmount();
			productList.unmount();
		});
	});
});
