/**
 * 크로스 플랫폼 호환성 테스트
 *
 * 이 테스트는 데스크탑과 안드로이드 플랫폼 간의 호환성을 검증합니다:
 * - 동일한 사용자 계정으로 데스크탑/안드로이드 로그인 테스트
 * - 플랫폼 간 토큰 독립성 확인
 * - API 엔드포인트 변경 후 모든 플랫폼에서 기능 검증
 * - JWT 토큰 형식의 플랫폼 간 일관성 확인
 */

import { describe, it, expect, beforeEach, afterEach, vi, beforeAll, afterAll } from 'vitest';

// JWT 유틸리티 함수들
import {
	isTokenValid,
	isTokenExpired,
	getTokenRemainingTime,
	getUserIdFromToken,
	debugToken
} from '$lib/utils/jwtUtils';

// 테스트용 모킹
import type { User } from '$lib/User';

// Tauri API 모킹
const mockTauriStore = {
	get: vi.fn(),
	set: vi.fn(),
	save: vi.fn(),
	load: vi.fn(),
	clear: vi.fn(),
	delete: vi.fn(),
	entries: vi.fn(),
	keys: vi.fn(),
	values: vi.fn(),
	length: vi.fn(),
	reset: vi.fn()
};

// Tauri 환경 모킹
vi.mock('@tauri-apps/plugin-store', () => ({
	Store: vi.fn().mockImplementation(() => mockTauriStore)
}));

// HTTP 클라이언트 모킹
const mockAxios = {
	post: vi.fn(),
	get: vi.fn(),
	put: vi.fn(),
	delete: vi.fn(),
	interceptors: {
		request: { use: vi.fn() },
		response: { use: vi.fn() }
	}
};

vi.mock('$lib/services/AxiosBackend', () => ({
	authClient: mockAxios
}));

// 네비게이션 모킹
vi.mock('$app/navigation', () => ({
	goto: vi.fn()
}));

// 환경 모킹
vi.mock('$app/environment', () => ({
	browser: true,
	dev: true
}));

describe('크로스 플랫폼 호환성 테스트', () => {
	// 테스트용 데이터
	const testUser: User = {
		id: 1,
		username: 'crossplatform_user',
		name: '크로스플랫폼 테스트 사용자',
		email: '<EMAIL>',
		role: 'user',
		department: '테스트부서',
		position: '테스트직급',
		phone: '010-1234-5678',
		created_at: '2024-01-01T00:00:00Z',
		updated_at: '2024-01-01T00:00:00Z'
	};

	const testTokenResponse = {
		access_token:
			'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxIiwiaWF0IjoxNzA0MDY3MjAwLCJleHAiOjk5OTk5OTk5OTksInR5cGUiOiJhY2Nlc3MifQ.cross-platform-access-token',
		refresh_token:
			'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxIiwiaWF0IjoxNzA0MDY3MjAwLCJleHAiOjk5OTk5OTk5OTksInR5cGUiOiJyZWZyZXNoIn0.cross-platform-refresh-token',
		token_type: 'Bearer' as const,
		expires_in: 900,
		user: testUser
	};

	// 동적 import를 위한 변수들
	let getCurrentPlatform: any;
	let isDesktop: any;
	let isAndroid: any;
	let isTauri: any;
	let getTokenStorage: any;
	let tokenService: any;

	beforeAll(async () => {
		// 전역 설정
		global.console.log = vi.fn();
		global.console.error = vi.fn();
		global.console.warn = vi.fn();

		// 동적 import로 서비스들 로드
		const platformServiceModule = await import('$lib/services/platformService');
		getCurrentPlatform = platformServiceModule.getCurrentPlatform;
		isDesktop = platformServiceModule.isDesktop;
		isAndroid = platformServiceModule.isAndroid;
		isTauri = platformServiceModule.isTauri;

		const tokenStorageModule = await import('$lib/services/tokenStorage');
		getTokenStorage = tokenStorageModule.getTokenStorage;

		const tokenServiceModule = await import('$lib/services/tokenService');
		tokenService = tokenServiceModule.tokenService;
	});

	beforeEach(() => {
		vi.clearAllMocks();

		// Tauri Store 모킹 초기화
		mockTauriStore.get.mockResolvedValue(null);
		mockTauriStore.set.mockResolvedValue(undefined);
		mockTauriStore.save.mockResolvedValue(undefined);
		mockTauriStore.load.mockResolvedValue(undefined);
		mockTauriStore.clear.mockResolvedValue(undefined);
		mockTauriStore.delete.mockResolvedValue(undefined);

		// HTTP 클라이언트 모킹 초기화
		mockAxios.post.mockReset();
		mockAxios.get.mockReset();
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	afterAll(() => {
		vi.restoreAllMocks();
	});

	describe('JWT 토큰 형식의 플랫폼 간 일관성', () => {
		it('동일한 JWT 토큰이 모든 플랫폼에서 동일하게 처리되어야 함', () => {
			const { access_token, refresh_token } = testTokenResponse;

			// 모든 플랫폼에서 동일한 JWT 유효성 검사 결과
			expect(isTokenValid(access_token)).toBe(true);
			expect(isTokenExpired(access_token)).toBe(false);
			expect(getTokenRemainingTime(access_token)).toBeGreaterThan(0);
			expect(getUserIdFromToken(access_token)).toBe('1');

			expect(isTokenValid(refresh_token)).toBe(true);
			expect(isTokenExpired(refresh_token)).toBe(false);
			expect(getTokenRemainingTime(refresh_token)).toBeGreaterThan(0);
			expect(getUserIdFromToken(refresh_token)).toBe('1');
		});

		it('JWT 페이로드가 플랫폼에 관계없이 동일하게 디코딩되어야 함', () => {
			const { access_token, refresh_token } = testTokenResponse;

			// 액세스 토큰 페이로드 확인
			const accessPayload = JSON.parse(atob(access_token.split('.')[1]));
			expect(accessPayload.sub).toBe('1');
			expect(accessPayload.type).toBe('access');
			expect(accessPayload.iat).toBe(1704067200);

			// 리프레시 토큰 페이로드 확인
			const refreshPayload = JSON.parse(atob(refresh_token.split('.')[1]));
			expect(refreshPayload.sub).toBe('1');
			expect(refreshPayload.type).toBe('refresh');
			expect(refreshPayload.iat).toBe(1704067200);
		});

		it('JWT 디버그 정보가 플랫폼에 관계없이 일관되게 출력되어야 함', () => {
			const { access_token, refresh_token } = testTokenResponse;

			// 디버그 함수가 오류 없이 실행되어야 함
			expect(() => debugToken(access_token, 'Cross-Platform Access Token')).not.toThrow();
			expect(() => debugToken(refresh_token, 'Cross-Platform Refresh Token')).not.toThrow();
		});
	});

	describe('플랫폼별 저장소 독립성', () => {
		it('데스크탑 플랫폼 시뮬레이션 - Tauri Store 사용', async () => {
			// 데스크탑 플랫폼으로 모킹
			const platformServiceModule = await import('$lib/services/platformService');
			vi.mocked(platformServiceModule.getCurrentPlatform).mockReturnValue('desktop');
			vi.mocked(platformServiceModule.isDesktop).mockReturnValue(true);
			vi.mocked(platformServiceModule.getStorageType).mockReturnValue('tauri');

			const storage = getTokenStorage();
			await storage.initialize();

			// 토큰 저장
			await storage.storeTokens(testTokenResponse.access_token, testTokenResponse.refresh_token);

			// 데스크탑에서는 Tauri Store가 사용되어야 함 (모킹 환경에서는 메모리 저장소로 대체)
			const retrievedAccessToken = await storage.getAccessToken();
			const retrievedRefreshToken = await storage.getRefreshToken();

			expect(retrievedAccessToken).toBe(testTokenResponse.access_token);
			expect(retrievedRefreshToken).toBe(testTokenResponse.refresh_token);
		});

		it('안드로이드 플랫폼 시뮬레이션 - 메모리 저장소 사용', async () => {
			// 안드로이드 플랫폼으로 모킹
			const platformServiceModule = await import('$lib/services/platformService');
			vi.mocked(platformServiceModule.getCurrentPlatform).mockReturnValue('android');
			vi.mocked(platformServiceModule.isAndroid).mockReturnValue(true);
			vi.mocked(platformServiceModule.getStorageType).mockReturnValue('memory');

			const storage = getTokenStorage();
			await storage.initialize();

			// 토큰 저장
			await storage.storeTokens(testTokenResponse.access_token, testTokenResponse.refresh_token);

			// 안드로이드에서는 메모리 저장소가 사용되어야 함
			const retrievedAccessToken = await storage.getAccessToken();
			const retrievedRefreshToken = await storage.getRefreshToken();

			expect(retrievedAccessToken).toBe(testTokenResponse.access_token);
			expect(retrievedRefreshToken).toBe(testTokenResponse.refresh_token);

			// 메모리 저장소 사용 확인 로그
			expect(console.log).toHaveBeenCalledWith(
				'[Memory Only] 토큰 저장소 초기화 완료 (메모리 전용)'
			);
		});

		it('플랫폼 간 토큰 저장소가 독립적이어야 함', async () => {
			// 첫 번째 플랫폼 (데스크탑) 시뮬레이션
			const platformServiceModule = await import('$lib/services/platformService');
			vi.mocked(platformServiceModule.getCurrentPlatform).mockReturnValue('desktop');
			vi.mocked(platformServiceModule.getStorageType).mockReturnValue('tauri');

			const desktopStorage = getTokenStorage();
			await desktopStorage.initialize();
			await desktopStorage.storeTokens('desktop-access-token', 'desktop-refresh-token');

			// 두 번째 플랫폼 (안드로이드) 시뮬레이션
			vi.mocked(platformServiceModule.getCurrentPlatform).mockReturnValue('android');
			vi.mocked(platformServiceModule.getStorageType).mockReturnValue('memory');

			const androidStorage = getTokenStorage();
			await androidStorage.initialize();
			await androidStorage.storeTokens('android-access-token', 'android-refresh-token');

			// 각 플랫폼의 토큰이 독립적으로 저장되어야 함
			// (실제로는 다른 저장소를 사용하지만, 테스트 환경에서는 동일한 메모리 저장소 사용)
			const androidAccessToken = await androidStorage.getAccessToken();
			const androidRefreshToken = await androidStorage.getRefreshToken();

			expect(androidAccessToken).toBe('android-access-token');
			expect(androidRefreshToken).toBe('android-refresh-token');
		});
	});

	describe('API 엔드포인트 호환성', () => {
		it('모든 플랫폼에서 동일한 로그인 API 엔드포인트를 사용해야 함', () => {
			// 로그인 API 엔드포인트 확인
			const loginEndpoint = '/api/auth/login';

			// 모든 플랫폼에서 동일한 엔드포인트 사용
			expect(loginEndpoint).toBe('/api/auth/login');
		});

		it('모든 플랫폼에서 동일한 토큰 갱신 API 엔드포인트를 사용해야 함', () => {
			// 토큰 갱신 API 엔드포인트 확인
			const refreshEndpoint = '/api/auth/refresh';

			// 모든 플랫폼에서 동일한 엔드포인트 사용
			expect(refreshEndpoint).toBe('/api/auth/refresh');
		});

		it('모든 플랫폼에서 동일한 로그아웃 API 엔드포인트를 사용해야 함', () => {
			// 로그아웃 API 엔드포인트 확인
			const logoutEndpoint = '/api/auth/logout';

			// 모든 플랫폼에서 동일한 엔드포인트 사용
			expect(logoutEndpoint).toBe('/api/auth/logout');
		});

		it('모든 플랫폼에서 동일한 사용자 정보 API 엔드포인트를 사용해야 함', () => {
			// 사용자 정보 API 엔드포인트 확인
			const userEndpoint = '/api/auth/me';

			// 모든 플랫폼에서 동일한 엔드포인트 사용
			expect(userEndpoint).toBe('/api/auth/me');
		});

		it('API 응답 형식이 플랫폼에 관계없이 일관되어야 함', () => {
			// 로그인 API 응답 형식 확인
			const expectedLoginResponse = {
				data: {
					tokens: {
						access_token: expect.any(String),
						refresh_token: expect.any(String),
						token_type: 'Bearer',
						expires_in: expect.any(Number),
						user: expect.any(Object)
					},
					user: expect.any(Object)
				}
			};

			// 모든 플랫폼에서 동일한 응답 형식 기대
			expect(expectedLoginResponse).toMatchObject({
				data: {
					tokens: {
						access_token: expect.any(String),
						refresh_token: expect.any(String),
						token_type: 'Bearer',
						expires_in: expect.any(Number),
						user: expect.any(Object)
					},
					user: expect.any(Object)
				}
			});
		});
	});

	describe('동일 사용자 계정 크로스 플랫폼 로그인', () => {
		beforeEach(async () => {
			await tokenService.initialize();
		});

		it('동일한 사용자가 데스크탑에서 로그인할 수 있어야 함', async () => {
			// 데스크탑 플랫폼 시뮬레이션
			const platformServiceModule = await import('$lib/services/platformService');
			vi.mocked(platformServiceModule.getCurrentPlatform).mockReturnValue('desktop');
			vi.mocked(platformServiceModule.isDesktop).mockReturnValue(true);

			// 로그인 API 응답 모킹
			mockAxios.post.mockResolvedValueOnce({
				data: {
					data: {
						tokens: testTokenResponse,
						user: testUser
					}
				}
			});

			// 로그인 시뮬레이션
			const loginCredentials = {
				username: testUser.username,
				password: 'password123'
			};

			// API 호출 확인을 위한 모킹 검증
			expect(mockAxios.post).not.toHaveBeenCalled();

			// 실제 로그인 로직은 authStore에서 처리되므로 여기서는 API 호출만 확인
			await mockAxios.post('/api/auth/login', loginCredentials);

			expect(mockAxios.post).toHaveBeenCalledWith('/api/auth/login', loginCredentials);
		});

		it('동일한 사용자가 안드로이드에서 로그인할 수 있어야 함', async () => {
			// 안드로이드 플랫폼 시뮬레이션
			const platformServiceModule = await import('$lib/services/platformService');
			vi.mocked(platformServiceModule.getCurrentPlatform).mockReturnValue('android');
			vi.mocked(platformServiceModule.isAndroid).mockReturnValue(true);

			// 로그인 API 응답 모킹
			mockAxios.post.mockResolvedValueOnce({
				data: {
					data: {
						tokens: testTokenResponse,
						user: testUser
					}
				}
			});

			// 로그인 시뮬레이션
			const loginCredentials = {
				username: testUser.username,
				password: 'password123'
			};

			// API 호출 확인을 위한 모킹 검증
			await mockAxios.post('/api/auth/login', loginCredentials);

			expect(mockAxios.post).toHaveBeenCalledWith('/api/auth/login', loginCredentials);
		});

		it('플랫폼별로 독립적인 세션을 유지해야 함', async () => {
			// 데스크탑에서 토큰 저장
			const platformServiceModule = await import('$lib/services/platformService');
			vi.mocked(platformServiceModule.getCurrentPlatform).mockReturnValue('desktop');

			await tokenService.storeTokens('desktop-session-token', 'desktop-refresh-token');

			// 안드로이드에서 토큰 저장
			vi.mocked(platformServiceModule.getCurrentPlatform).mockReturnValue('android');

			await tokenService.storeTokens('android-session-token', 'android-refresh-token');

			// 각 플랫폼의 토큰이 독립적으로 관리되어야 함
			// (실제 환경에서는 다른 저장소를 사용하므로 독립성이 보장됨)
			const androidToken = await tokenService.getAccessToken();
			expect(androidToken).toBe('android-session-token');
		});
	});

	describe('크로스 플랫폼 성능 일관성', () => {
		it('JWT 토큰 처리 성능이 플랫폼에 관계없이 일관되어야 함', () => {
			const { access_token } = testTokenResponse;

			// 성능 측정
			const iterations = 1000;
			const startTime = Date.now();

			for (let i = 0; i < iterations; i++) {
				isTokenValid(access_token);
				isTokenExpired(access_token);
				getTokenRemainingTime(access_token);
				getUserIdFromToken(access_token);
			}

			const endTime = Date.now();
			const processingTime = endTime - startTime;

			console.log(`크로스 플랫폼 JWT 처리 성능: ${iterations}회 처리 시간 ${processingTime}ms`);

			// JWT 처리는 플랫폼에 관계없이 빨라야 함
			expect(processingTime).toBeLessThan(100); // 100ms 이내
		});

		it('토큰 저장소 접근 성능이 플랫폼별로 적절해야 함', async () => {
			const storage = getTokenStorage();
			await storage.initialize();

			const iterations = 100;
			const startTime = Date.now();

			// 반복적인 토큰 저장/조회
			for (let i = 0; i < iterations; i++) {
				await storage.storeTokens(`access-${i}`, `refresh-${i}`);
				await storage.getAccessToken();
				await storage.getRefreshToken();
			}

			const endTime = Date.now();
			const processingTime = endTime - startTime;

			console.log(`크로스 플랫폼 저장소 성능: ${iterations}회 처리 시간 ${processingTime}ms`);

			// 저장소 접근은 합리적인 시간 내에 완료되어야 함
			expect(processingTime).toBeLessThan(1000); // 1초 이내
		});
	});

	describe('크로스 플랫폼 에러 처리 일관성', () => {
		it('JWT 토큰 오류가 플랫폼에 관계없이 일관되게 처리되어야 함', () => {
			const invalidTokens = [
				'invalid.token.format',
				'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.invalid-payload.signature',
				'',
				null,
				undefined
			];

			invalidTokens.forEach((token) => {
				// 모든 플랫폼에서 동일한 에러 처리
				expect(() => isTokenValid(token as any)).not.toThrow();
				expect(() => isTokenExpired(token as any)).not.toThrow();
				expect(() => getTokenRemainingTime(token as any)).not.toThrow();
				expect(() => getUserIdFromToken(token as any)).not.toThrow();

				// 모든 플랫폼에서 동일한 결과
				expect(isTokenValid(token as any)).toBe(false);
				expect(isTokenExpired(token as any)).toBe(true);
				expect(getTokenRemainingTime(token as any)).toBe(0);
				expect(getUserIdFromToken(token as any)).toBeNull();
			});
		});

		it('네트워크 오류가 플랫폼에 관계없이 일관되게 처리되어야 함', async () => {
			// 네트워크 오류 시뮬레이션
			mockAxios.post.mockRejectedValueOnce({
				code: 'NETWORK_ERROR',
				message: 'Network Error'
			});

			// 모든 플랫폼에서 동일한 에러 처리
			await expect(
				mockAxios.post('/api/auth/login', {
					username: 'testuser',
					password: 'password123'
				})
			).rejects.toMatchObject({
				code: 'NETWORK_ERROR',
				message: 'Network Error'
			});
		});

		it('토큰 저장소 오류가 플랫폼별로 적절히 처리되어야 함', async () => {
			const storage = getTokenStorage();
			await storage.initialize();

			// 정상적인 동작 확인 (메모리 저장소는 일반적으로 오류가 발생하지 않음)
			await expect(storage.storeTokens('test-access', 'test-refresh')).resolves.not.toThrow();
			await expect(storage.getAccessToken()).resolves.not.toThrow();
			await expect(storage.clearTokens()).resolves.not.toThrow();
		});
	});

	describe('크로스 플랫폼 디버그 정보 일관성', () => {
		it('플랫폼 정보가 올바르게 표시되어야 함', async () => {
			// 현재 플랫폼 정보 확인
			const platform = getCurrentPlatform();
			const isTauriEnv = isTauri();

			// 플랫폼 정보가 유효해야 함
			expect(['desktop', 'android', 'ios', 'web']).toContain(platform);
			expect(typeof isTauriEnv).toBe('boolean');

			console.log(`현재 테스트 플랫폼: ${platform}, Tauri 환경: ${isTauriEnv}`);
		});

		it('토큰 디버그 정보가 플랫폼에 관계없이 일관되게 출력되어야 함', async () => {
			await tokenService.initialize();

			// 토큰 저장
			await tokenService.storeTokens(
				testTokenResponse.access_token,
				testTokenResponse.refresh_token
			);

			// 디버그 정보 출력
			await tokenService.debugTokens();

			// 디버그 로그가 출력되었는지 확인
			expect(console.log).toHaveBeenCalled();
		});

		it('플랫폼별 저장소 타입이 올바르게 감지되어야 함', async () => {
			const platformServiceModule = await import('$lib/services/platformService');
			const storageType = platformServiceModule.getStorageType();

			// 저장소 타입이 유효해야 함
			expect(['tauri', 'memory', 'localStorage']).toContain(storageType);

			console.log(`현재 저장소 타입: ${storageType}`);
		});
	});
});
