/**
 * 데스크탑 환경 JWT 인증 시스템 간단 테스트
 *
 * SvelteKit 의존성을 최소화하여 핵심 JWT 기능만 테스트합니다.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';

// JWT 유틸리티 함수들 (SvelteKit 의존성 없음)
import {
	isTokenValid,
	isTokenExpired,
	getTokenRemainingTime,
	getUserIdFromToken,
	debugToken
} from '$lib/utils/jwtUtils';

// 플랫폼 서비스 (모킹 가능)
import { getCurrentPlatform, isDesktop, isTauri } from '$lib/services/platformService';

// Tauri Store 모킹
const mockTauriStore = {
	get: vi.fn(),
	set: vi.fn(),
	save: vi.fn(),
	load: vi.fn(),
	clear: vi.fn(),
	delete: vi.fn(),
	entries: vi.fn(),
	keys: vi.fn(),
	values: vi.fn(),
	length: vi.fn(),
	reset: vi.fn()
};

// Tauri 환경 모킹
vi.mock('@tauri-apps/plugin-store', () => ({
	Store: vi.fn().mockImplementation(() => mockTauriStore)
}));

// 플랫폼 서비스 모킹 (데스크탑 환경으로 설정)
vi.mock('$lib/services/platformService', async (importOriginal) => {
	const actual = await importOriginal();
	return {
		...actual,
		getCurrentPlatform: vi.fn().mockReturnValue('desktop'),
		isDesktop: vi.fn().mockReturnValue(true),
		isTauri: vi.fn().mockReturnValue(true),
		isAndroid: vi.fn().mockReturnValue(false),
		isIOS: vi.fn().mockReturnValue(false),
		getStorageType: vi.fn().mockReturnValue('tauri'),
		debugPlatform: vi.fn()
	};
});

describe('데스크탑 환경 JWT 인증 시스템 간단 테스트', () => {
	// 테스트용 JWT 토큰들
	const validAccessToken =
		'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxIiwiaWF0IjoxNzA0MDY3MjAwLCJleHAiOjk5OTk5OTk5OTksInR5cGUiOiJhY2Nlc3MifQ.valid-access-token';
	const expiredAccessToken =
		'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxIiwiaWF0IjoxNzA0MDY3MjAwLCJleHAiOjE3MDQwNjcyMDEsInR5cGUiOiJhY2Nlc3MifQ.expired-access-token';
	const validRefreshToken =
		'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxIiwiaWF0IjoxNzA0MDY3MjAwLCJleHAiOjk5OTk5OTk5OTksInR5cGUiOiJyZWZyZXNoIn0.valid-refresh-token';

	beforeEach(() => {
		vi.clearAllMocks();

		// Tauri Store 모킹 초기화
		mockTauriStore.get.mockResolvedValue(null);
		mockTauriStore.set.mockResolvedValue(undefined);
		mockTauriStore.save.mockResolvedValue(undefined);
		mockTauriStore.load.mockResolvedValue(undefined);
		mockTauriStore.clear.mockResolvedValue(undefined);
		mockTauriStore.delete.mockResolvedValue(undefined);
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	describe('플랫폼 감지', () => {
		it('데스크탑 플랫폼을 올바르게 감지해야 함', () => {
			expect(getCurrentPlatform()).toBe('desktop');
			expect(isDesktop()).toBe(true);
			expect(isTauri()).toBe(true);
		});
	});

	describe('JWT 토큰 유틸리티 함수', () => {
		it('유효한 토큰을 올바르게 검증해야 함', () => {
			expect(isTokenValid(validAccessToken)).toBe(true);
			expect(isTokenExpired(validAccessToken)).toBe(false);
			expect(getTokenRemainingTime(validAccessToken)).toBeGreaterThan(0);
			expect(getUserIdFromToken(validAccessToken)).toBe('1');
		});

		it('만료된 토큰을 올바르게 검증해야 함', () => {
			expect(isTokenValid(expiredAccessToken)).toBe(false);
			expect(isTokenExpired(expiredAccessToken)).toBe(true);
			expect(getTokenRemainingTime(expiredAccessToken)).toBeLessThanOrEqual(0);
			expect(getUserIdFromToken(expiredAccessToken)).toBe('1'); // 만료되어도 사용자 ID는 추출 가능
		});

		it('잘못된 형식의 토큰을 처리해야 함', () => {
			const invalidToken = 'invalid.token.format';

			expect(isTokenValid(invalidToken)).toBe(false);
			expect(isTokenExpired(invalidToken)).toBe(true);
			expect(getTokenRemainingTime(invalidToken)).toBe(0);
			expect(getUserIdFromToken(invalidToken)).toBeNull();
		});

		it('빈 토큰을 처리해야 함', () => {
			expect(isTokenValid('')).toBe(false);
			expect(isTokenValid(null as any)).toBe(false);
			expect(isTokenValid(undefined as any)).toBe(false);
		});

		it('디버그 토큰 함수가 오류 없이 실행되어야 함', () => {
			// 개발 환경에서만 실행되므로 오류가 발생하지 않아야 함
			expect(() => debugToken(validAccessToken, 'Test Token')).not.toThrow();
			expect(() => debugToken(expiredAccessToken, 'Expired Token')).not.toThrow();
			expect(() => debugToken('invalid-token', 'Invalid Token')).not.toThrow();
		});
	});

	describe('Tauri Store API 기본 동작', () => {
		let TauriStore: any;

		beforeEach(async () => {
			// 동적으로 Tauri Store 모듈 import
			const storeModule = await import('@tauri-apps/plugin-store');
			TauriStore = storeModule.Store;
		});

		it('Tauri Store 인스턴스를 생성할 수 있어야 함', () => {
			const store = new TauriStore('test.dat');
			expect(store).toBeDefined();
			expect(TauriStore).toHaveBeenCalledWith('test.dat');
		});

		it('토큰 저장 메서드들이 호출되어야 함', async () => {
			const store = new TauriStore('auth.dat');

			await store.set('access_token', validAccessToken);
			await store.set('refresh_token', validRefreshToken);
			await store.save();

			expect(mockTauriStore.set).toHaveBeenCalledWith('access_token', validAccessToken);
			expect(mockTauriStore.set).toHaveBeenCalledWith('refresh_token', validRefreshToken);
			expect(mockTauriStore.save).toHaveBeenCalled();
		});

		it('토큰 조회 메서드들이 호출되어야 함', async () => {
			const store = new TauriStore('auth.dat');

			mockTauriStore.get.mockImplementation((key: string) => {
				if (key === 'access_token') return Promise.resolve(validAccessToken);
				if (key === 'refresh_token') return Promise.resolve(validRefreshToken);
				return Promise.resolve(null);
			});

			const accessToken = await store.get('access_token');
			const refreshToken = await store.get('refresh_token');

			expect(accessToken).toBe(validAccessToken);
			expect(refreshToken).toBe(validRefreshToken);
			expect(mockTauriStore.get).toHaveBeenCalledWith('access_token');
			expect(mockTauriStore.get).toHaveBeenCalledWith('refresh_token');
		});

		it('토큰 삭제 메서드들이 호출되어야 함', async () => {
			const store = new TauriStore('auth.dat');

			await store.delete('access_token');
			await store.delete('refresh_token');
			await store.save();

			expect(mockTauriStore.delete).toHaveBeenCalledWith('access_token');
			expect(mockTauriStore.delete).toHaveBeenCalledWith('refresh_token');
			expect(mockTauriStore.save).toHaveBeenCalled();
		});

		it('Store 오류를 적절히 처리해야 함', async () => {
			const store = new TauriStore('auth.dat');

			// Store 접근 오류 모킹
			mockTauriStore.get.mockRejectedValue(new Error('Store access denied'));
			mockTauriStore.set.mockRejectedValue(new Error('Store write denied'));

			// 오류가 발생해도 예외가 전파되어야 함
			await expect(store.get('access_token')).rejects.toThrow('Store access denied');
			await expect(store.set('access_token', 'test')).rejects.toThrow('Store write denied');
		});
	});

	describe('토큰 저장소 통합 테스트', () => {
		let tokenStorage: any;

		beforeEach(async () => {
			// 동적으로 토큰 저장소 모듈 import
			const storageModule = await import('$lib/services/tokenStorage');
			tokenStorage = storageModule.getTokenStorage();
			await tokenStorage.initialize();
		});

		it('토큰 저장소가 초기화되어야 함', () => {
			expect(tokenStorage).toBeDefined();
			expect(typeof tokenStorage.storeTokens).toBe('function');
			expect(typeof tokenStorage.getAccessToken).toBe('function');
			expect(typeof tokenStorage.getRefreshToken).toBe('function');
			expect(typeof tokenStorage.clearTokens).toBe('function');
		});

		it('토큰을 저장하고 조회할 수 있어야 함', async () => {
			// 토큰 저장
			await tokenStorage.storeTokens(validAccessToken, validRefreshToken);

			// Tauri Store 호출 확인
			expect(mockTauriStore.set).toHaveBeenCalledWith('access_token', validAccessToken);
			expect(mockTauriStore.set).toHaveBeenCalledWith('refresh_token', validRefreshToken);
			expect(mockTauriStore.save).toHaveBeenCalled();

			// 토큰 조회를 위한 모킹 설정
			mockTauriStore.get.mockImplementation((key: string) => {
				if (key === 'access_token') return Promise.resolve(validAccessToken);
				if (key === 'refresh_token') return Promise.resolve(validRefreshToken);
				return Promise.resolve(null);
			});

			// 토큰 조회
			const retrievedAccessToken = await tokenStorage.getAccessToken();
			const retrievedRefreshToken = await tokenStorage.getRefreshToken();

			expect(retrievedAccessToken).toBe(validAccessToken);
			expect(retrievedRefreshToken).toBe(validRefreshToken);
		});

		it('만료된 토큰을 자동으로 제거해야 함', async () => {
			// 만료된 토큰 반환 모킹
			mockTauriStore.get.mockResolvedValue(expiredAccessToken);

			// 만료된 토큰 조회 시 null 반환되고 자동 삭제되어야 함
			const retrievedToken = await tokenStorage.getAccessToken();
			expect(retrievedToken).toBeNull();

			// 만료된 토큰 삭제 확인
			expect(mockTauriStore.set).toHaveBeenCalledWith('access_token', '');
		});

		it('토큰을 완전히 삭제할 수 있어야 함', async () => {
			await tokenStorage.clearTokens();

			expect(mockTauriStore.delete).toHaveBeenCalledWith('access_token');
			expect(mockTauriStore.delete).toHaveBeenCalledWith('refresh_token');
			expect(mockTauriStore.save).toHaveBeenCalled();
		});
	});

	describe('성능 테스트', () => {
		let tokenStorage: any;

		beforeEach(async () => {
			const storageModule = await import('$lib/services/tokenStorage');
			tokenStorage = storageModule.getTokenStorage();
			await tokenStorage.initialize();
		});

		it('대량 토큰 저장/조회가 효율적이어야 함', async () => {
			const startTime = Date.now();

			// 100회 토큰 저장/조회 테스트
			for (let i = 0; i < 100; i++) {
				await tokenStorage.storeTokens(`access-${i}`, `refresh-${i}`);

				// 조회를 위한 모킹
				mockTauriStore.get.mockImplementation((key: string) => {
					if (key === 'access_token') return Promise.resolve(`access-${i}`);
					if (key === 'refresh_token') return Promise.resolve(`refresh-${i}`);
					return Promise.resolve(null);
				});

				await tokenStorage.getAccessToken();
				await tokenStorage.getRefreshToken();
			}

			const endTime = Date.now();
			const processingTime = endTime - startTime;

			console.log(`100회 토큰 저장/조회 시간: ${processingTime}ms`);

			// 처리 시간이 합리적인 범위 내에 있어야 함 (모킹이므로 매우 빨라야 함)
			expect(processingTime).toBeLessThan(1000); // 1초 이내
		});

		it('JWT 토큰 유효성 검사가 효율적이어야 함', () => {
			const startTime = Date.now();

			// 1000회 토큰 유효성 검사
			for (let i = 0; i < 1000; i++) {
				isTokenValid(validAccessToken);
				isTokenExpired(validAccessToken);
				getTokenRemainingTime(validAccessToken);
				getUserIdFromToken(validAccessToken);
			}

			const endTime = Date.now();
			const processingTime = endTime - startTime;

			console.log(`1000회 JWT 유효성 검사 시간: ${processingTime}ms`);

			// JWT 파싱은 매우 빨라야 함
			expect(processingTime).toBeLessThan(100); // 100ms 이내
		});
	});

	describe('에러 처리', () => {
		let tokenStorage: any;

		beforeEach(async () => {
			const storageModule = await import('$lib/services/tokenStorage');
			tokenStorage = storageModule.getTokenStorage();
			await tokenStorage.initialize();
		});

		it('Store 접근 오류를 안전하게 처리해야 함', async () => {
			// Store 접근 오류 모킹
			mockTauriStore.get.mockRejectedValue(new Error('Store access denied'));
			mockTauriStore.set.mockRejectedValue(new Error('Store write denied'));

			// 토큰 조회 시 오류가 발생해도 null을 반환해야 함
			const accessToken = await tokenStorage.getAccessToken();
			const refreshToken = await tokenStorage.getRefreshToken();

			expect(accessToken).toBeNull();
			expect(refreshToken).toBeNull();

			// 토큰 저장 시 오류가 발생하면 예외가 전파되어야 함
			await expect(tokenStorage.storeTokens('test', 'test')).rejects.toThrow();
		});

		it('잘못된 JWT 형식을 안전하게 처리해야 함', () => {
			const malformedTokens = [
				'not.a.jwt',
				'invalid-token',
				'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.invalid-payload.signature',
				'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..signature', // 빈 페이로드
				'..', // 완전히 빈 토큰
				null,
				undefined,
				''
			];

			malformedTokens.forEach((token) => {
				expect(() => isTokenValid(token as any)).not.toThrow();
				expect(() => isTokenExpired(token as any)).not.toThrow();
				expect(() => getTokenRemainingTime(token as any)).not.toThrow();
				expect(() => getUserIdFromToken(token as any)).not.toThrow();

				expect(isTokenValid(token as any)).toBe(false);
				expect(isTokenExpired(token as any)).toBe(true);
				expect(getTokenRemainingTime(token as any)).toBe(0);
				expect(getUserIdFromToken(token as any)).toBeNull();
			});
		});
	});

	describe('데스크탑 전용 기능', () => {
		it('데스크탑 환경에서만 실행되는 기능들이 정상 동작해야 함', () => {
			// 플랫폼 감지 확인
			expect(getCurrentPlatform()).toBe('desktop');
			expect(isDesktop()).toBe(true);
			expect(isTauri()).toBe(true);

			// 데스크탑 전용 플러그인들이 사용 가능한 환경인지 확인
			// (실제 플러그인 테스트는 E2E에서 수행)
			expect(true).toBe(true);
		});

		it('Tauri Store가 데스크탑 환경에서 사용 가능해야 함', async () => {
			// Tauri Store 모듈이 정상적으로 로드되는지 확인
			const storeModule = await import('@tauri-apps/plugin-store');
			expect(storeModule.Store).toBeDefined();

			// Store 인스턴스 생성 확인
			const store = new storeModule.Store('test.dat');
			expect(store).toBeDefined();
		});
	});
});
