/**
 * 데스크탑 환경 JWT 인증 시스템 통합 테스트
 *
 * 이 테스트는 데스크탑 환경에서 JWT 인증 플로우 전체를 검증합니다:
 * - Tauri Store API 기반 토큰 저장소 동작 확인
 * - JWT 인증 플로우 (로그인, 토큰 갱신, 로그아웃) 테스트
 * - 데스크탑 전용 플러그인 정상 동작 확인
 * - 플랫폼 감지 및 저장소 선택 로직 테스트
 */

import { describe, it, expect, beforeEach, afterEach, vi, beforeAll, afterAll } from 'vitest';
import { get } from 'svelte/store';

// 테스트용 모킹
import type { User } from '$lib/User';

// Tauri API 모킹
const mockTauriStore = {
	get: vi.fn(),
	set: vi.fn(),
	save: vi.fn(),
	load: vi.fn(),
	clear: vi.fn(),
	delete: vi.fn(),
	entries: vi.fn(),
	keys: vi.fn(),
	values: vi.fn(),
	length: vi.fn(),
	reset: vi.fn()
};

// Tauri 환경 모킹
vi.mock('@tauri-apps/plugin-store', () => ({
	Store: vi.fn().mockImplementation(() => mockTauriStore)
}));

// HTTP 클라이언트 모킹
const mockAxios = {
	post: vi.fn(),
	get: vi.fn(),
	put: vi.fn(),
	delete: vi.fn(),
	interceptors: {
		request: { use: vi.fn() },
		response: { use: vi.fn() }
	}
};

vi.mock('$lib/services/AxiosBackend', () => ({
	authClient: mockAxios
}));

// 플랫폼 서비스 모킹 (데스크탑 환경으로 설정)
vi.mock('$lib/services/platformService', async () => {
	const actual = await vi.importActual('$lib/services/platformService');
	return {
		...actual,
		getCurrentPlatform: vi.fn().mockReturnValue('desktop'),
		isDesktop: vi.fn().mockReturnValue(true),
		isTauri: vi.fn().mockReturnValue(true),
		isAndroid: vi.fn().mockReturnValue(false),
		isIOS: vi.fn().mockReturnValue(false),
		debugPlatform: vi.fn()
	};
});

// 네비게이션 모킹
vi.mock('$app/navigation', () => ({
	goto: vi.fn()
}));

// 환경 모킹
vi.mock('$app/environment', () => ({
	browser: true,
	dev: true
}));

// JWT 인증 관련 서비스들 (동적 import로 변경)
let tokenService: any;
let authActions: any;
let authState: any;
let isAuthenticated: any;
let currentUser: any;
let getCurrentPlatform: any;
let isDesktop: any;
let isTauri: any;
let getTokenStorage: any;

describe('데스크탑 환경 JWT 인증 시스템 통합 테스트', () => {
	// 테스트용 데이터
	const testUser: User = {
		id: 1,
		username: 'testuser',
		name: '테스트 사용자',
		email: '<EMAIL>',
		role: 'user',
		department: '테스트부서',
		position: '테스트직급',
		phone: '010-1234-5678',
		created_at: '2024-01-01T00:00:00Z',
		updated_at: '2024-01-01T00:00:00Z'
	};

	const testTokenResponse: TokenResponse = {
		access_token:
			'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxIiwiaWF0IjoxNzA0MDY3MjAwLCJleHAiOjE3MDQwNjgxMDAsInR5cGUiOiJhY2Nlc3MifQ.test-access-token',
		refresh_token:
			'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxIiwiaWF0IjoxNzA0MDY3MjAwLCJleHAiOjE3MDQxNTM2MDAsInR5cGUiOiJyZWZyZXNoIn0.test-refresh-token',
		token_type: 'Bearer',
		expires_in: 900, // 15분
		user: testUser
	};

	beforeAll(async () => {
		// 전역 설정
		global.console.log = vi.fn();
		global.console.error = vi.fn();
		global.console.warn = vi.fn();

		// 동적 import로 서비스들 로드
		const tokenServiceModule = await import('$lib/services/tokenService');
		tokenService = tokenServiceModule.tokenService;

		const authStoreModule = await import('$lib/stores/authStore');
		authActions = authStoreModule.authActions;
		authState = authStoreModule.authState;
		isAuthenticated = authStoreModule.isAuthenticated;
		currentUser = authStoreModule.currentUser;

		const platformServiceModule = await import('$lib/services/platformService');
		getCurrentPlatform = platformServiceModule.getCurrentPlatform;
		isDesktop = platformServiceModule.isDesktop;
		isTauri = platformServiceModule.isTauri;

		const tokenStorageModule = await import('$lib/services/tokenStorage');
		getTokenStorage = tokenStorageModule.getTokenStorage;
	});

	beforeEach(async () => {
		// 모든 모킹 초기화
		vi.clearAllMocks();

		// Tauri Store 모킹 초기화
		mockTauriStore.get.mockResolvedValue(null);
		mockTauriStore.set.mockResolvedValue(undefined);
		mockTauriStore.save.mockResolvedValue(undefined);
		mockTauriStore.load.mockResolvedValue(undefined);
		mockTauriStore.clear.mockResolvedValue(undefined);
		mockTauriStore.delete.mockResolvedValue(undefined);

		// HTTP 클라이언트 모킹 초기화
		mockAxios.post.mockReset();
		mockAxios.get.mockReset();

		// 인증 상태 초기화
		authState.set({
			isAuthenticated: false,
			isInitialized: false,
			isLoading: false,
			user: null,
			accessToken: null,
			refreshToken: null,
			tokenExpiresAt: null,
			error: null,
			debugInfo: {
				lastTokenRefresh: null,
				tokenRefreshCount: 0,
				platform: 'desktop'
			}
		});
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	afterAll(() => {
		vi.restoreAllMocks();
	});

	describe('플랫폼 감지 및 저장소 초기화', () => {
		it('데스크탑 플랫폼을 올바르게 감지해야 함', () => {
			expect(getCurrentPlatform()).toBe('desktop');
			expect(isDesktop()).toBe(true);
			expect(isTauri()).toBe(true);
		});

		it('Tauri Store API 기반 토큰 저장소를 초기화해야 함', async () => {
			const storage = getTokenStorage();
			await storage.initialize();

			// Tauri Store가 생성되었는지 확인
			expect(storage).toBeDefined();
			expect(typeof storage.storeTokens).toBe('function');
			expect(typeof storage.getAccessToken).toBe('function');
			expect(typeof storage.getRefreshToken).toBe('function');
		});

		it('토큰 서비스가 올바르게 초기화되어야 함', async () => {
			await tokenService.initialize();

			expect(tokenService.storage).toBeDefined();

			// 개발 환경에서 디버그 로그가 출력되는지 확인
			expect(console.log).toHaveBeenCalledWith('[Token Service] 초기화 완료');
		});
	});

	describe('Tauri Store API 기반 토큰 저장소 동작', () => {
		beforeEach(async () => {
			await tokenService.initialize();
		});

		it('토큰을 Tauri Store에 저장할 수 있어야 함', async () => {
			const accessToken = testTokenResponse.access_token;
			const refreshToken = testTokenResponse.refresh_token;

			await tokenService.storeTokens(accessToken, refreshToken);

			// Tauri Store의 set 메서드가 호출되었는지 확인
			expect(mockTauriStore.set).toHaveBeenCalledWith('access_token', accessToken);
			expect(mockTauriStore.set).toHaveBeenCalledWith('refresh_token', refreshToken);
			expect(mockTauriStore.save).toHaveBeenCalled();
		});

		it('Tauri Store에서 토큰을 조회할 수 있어야 함', async () => {
			const accessToken = testTokenResponse.access_token;
			const refreshToken = testTokenResponse.refresh_token;

			// Tauri Store에서 토큰을 반환하도록 모킹
			mockTauriStore.get.mockImplementation((key: string) => {
				if (key === 'access_token') return Promise.resolve(accessToken);
				if (key === 'refresh_token') return Promise.resolve(refreshToken);
				return Promise.resolve(null);
			});

			const retrievedAccessToken = await tokenService.getAccessToken();
			const retrievedRefreshToken = await tokenService.getRefreshToken();

			expect(retrievedAccessToken).toBe(accessToken);
			expect(retrievedRefreshToken).toBe(refreshToken);
			expect(mockTauriStore.get).toHaveBeenCalledWith('access_token');
			expect(mockTauriStore.get).toHaveBeenCalledWith('refresh_token');
		});

		it('Tauri Store에서 토큰을 삭제할 수 있어야 함', async () => {
			await tokenService.clearTokens();

			expect(mockTauriStore.delete).toHaveBeenCalledWith('access_token');
			expect(mockTauriStore.delete).toHaveBeenCalledWith('refresh_token');
			expect(mockTauriStore.save).toHaveBeenCalled();
		});

		it('토큰 유효성 검사가 올바르게 동작해야 함', async () => {
			// 유효한 토큰 (미래 만료 시간)
			const validToken =
				'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxIiwiaWF0IjoxNzA0MDY3MjAwLCJleHAiOjk5OTk5OTk5OTksInR5cGUiOiJhY2Nlc3MifQ.valid-token';

			// 만료된 토큰 (과거 만료 시간)
			const expiredToken =
				'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxIiwiaWF0IjoxNzA0MDY3MjAwLCJleHAiOjE3MDQwNjcyMDEsInR5cGUiOiJhY2Nlc3MifQ.expired-token';

			// 유효한 토큰 테스트
			mockTauriStore.get.mockResolvedValue(validToken);
			const validResult = await tokenService.getAccessToken();
			expect(validResult).toBe(validToken);

			// 만료된 토큰 테스트 (자동 삭제되어야 함)
			mockTauriStore.get.mockResolvedValue(expiredToken);
			const expiredResult = await tokenService.getAccessToken();
			expect(expiredResult).toBeNull();
			expect(mockTauriStore.set).toHaveBeenCalledWith('access_token', '');
		});

		it('토큰 상태 정보를 올바르게 제공해야 함', async () => {
			const accessToken =
				'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxIiwiaWF0IjoxNzA0MDY3MjAwLCJleHAiOjk5OTk5OTk5OTksInR5cGUiOiJhY2Nlc3MifQ.valid-token';
			const refreshToken = 'valid-refresh-token';

			mockTauriStore.get.mockImplementation((key: string) => {
				if (key === 'access_token') return Promise.resolve(accessToken);
				if (key === 'refresh_token') return Promise.resolve(refreshToken);
				return Promise.resolve(null);
			});

			const status = await tokenService.getTokenStatus();

			expect(status.hasAccessToken).toBe(true);
			expect(status.hasRefreshToken).toBe(true);
			expect(status.isAccessTokenValid).toBe(true);
			expect(status.isRefreshTokenValid).toBe(true);
			expect(status.userId).toBe('1');
			expect(status.accessTokenRemainingTime).toBeGreaterThan(0);
		});
	});

	describe('JWT 인증 플로우 통합 테스트', () => {
		beforeEach(async () => {
			await authActions.initialize();
		});

		it('로그인 플로우가 올바르게 동작해야 함', async () => {
			// 로그인 API 응답 모킹
			mockAxios.post.mockResolvedValueOnce({
				data: {
					data: {
						tokens: testTokenResponse,
						user: testUser
					}
				}
			});

			// 로그인 실행
			await authActions.login({
				username: 'testuser',
				password: 'password123'
			});

			// API 호출 확인
			expect(mockAxios.post).toHaveBeenCalledWith('/api/auth/login', {
				username: 'testuser',
				password: 'password123'
			});

			// 토큰 저장 확인
			expect(mockTauriStore.set).toHaveBeenCalledWith(
				'access_token',
				testTokenResponse.access_token
			);
			expect(mockTauriStore.set).toHaveBeenCalledWith(
				'refresh_token',
				testTokenResponse.refresh_token
			);

			// 인증 상태 확인
			const state = get(authState);
			expect(state.isAuthenticated).toBe(true);
			expect(state.user).toEqual(testUser);
			expect(state.accessToken).toBe(testTokenResponse.access_token);
			expect(state.refreshToken).toBe(testTokenResponse.refresh_token);
		});

		it('토큰 갱신 플로우가 올바르게 동작해야 함', async () => {
			// 기존 리프레시 토큰 설정
			mockTauriStore.get.mockImplementation((key: string) => {
				if (key === 'refresh_token') return Promise.resolve('valid-refresh-token');
				return Promise.resolve(null);
			});

			// 토큰 갱신 API 응답 모킹
			const newTokenResponse = {
				...testTokenResponse,
				access_token: 'new-access-token',
				refresh_token: 'new-refresh-token'
			};

			mockAxios.post.mockResolvedValueOnce({
				data: newTokenResponse
			});

			// 토큰 갱신 실행
			const result = await authActions.refreshAuth();

			expect(result).toBe(true);
			expect(mockAxios.post).toHaveBeenCalledWith('/api/auth/refresh', {
				refresh_token: 'valid-refresh-token'
			});

			// 새 토큰 저장 확인
			expect(mockTauriStore.set).toHaveBeenCalledWith('access_token', 'new-access-token');
			expect(mockTauriStore.set).toHaveBeenCalledWith('refresh_token', 'new-refresh-token');

			// 디버그 정보 업데이트 확인
			const state = get(authState);
			expect(state.debugInfo?.tokenRefreshCount).toBeGreaterThan(0);
			expect(state.debugInfo?.lastTokenRefresh).toBeInstanceOf(Date);
		});

		it('로그아웃 플로우가 올바르게 동작해야 함', async () => {
			// 초기 인증 상태 설정
			authState.update((state) => ({
				...state,
				isAuthenticated: true,
				user: testUser,
				accessToken: testTokenResponse.access_token,
				refreshToken: testTokenResponse.refresh_token
			}));

			// 로그아웃 API 응답 모킹
			mockAxios.post.mockResolvedValueOnce({ data: { message: '로그아웃 성공' } });

			// 로그아웃 실행
			await authActions.logout();

			// 서버 로그아웃 API 호출 확인
			expect(mockAxios.post).toHaveBeenCalledWith('/api/auth/logout');

			// 토큰 삭제 확인
			expect(mockTauriStore.delete).toHaveBeenCalledWith('access_token');
			expect(mockTauriStore.delete).toHaveBeenCalledWith('refresh_token');

			// 인증 상태 초기화 확인
			const state = get(authState);
			expect(state.isAuthenticated).toBe(false);
			expect(state.user).toBeNull();
			expect(state.accessToken).toBeNull();
			expect(state.refreshToken).toBeNull();
		});

		it('네트워크 오류 시 적절히 처리해야 함', async () => {
			// 네트워크 오류 모킹
			mockAxios.post.mockRejectedValueOnce({
				code: 'NETWORK_ERROR',
				message: 'Network Error'
			});

			// 로그인 시도
			await expect(
				authActions.login({
					username: 'testuser',
					password: 'password123'
				})
			).rejects.toThrow();

			// 에러 상태 확인
			const state = get(authState);
			expect(state.error).toBeDefined();
			expect(state.error?.type).toBe('NETWORK_ERROR');
			expect(state.isAuthenticated).toBe(false);
		});

		it('401 인증 오류 시 적절히 처리해야 함', async () => {
			// 401 오류 모킹
			mockAxios.post.mockRejectedValueOnce({
				response: {
					status: 401,
					data: { message: 'Invalid credentials' }
				}
			});

			// 로그인 시도
			await expect(
				authActions.login({
					username: 'wronguser',
					password: 'wrongpassword'
				})
			).rejects.toThrow();

			// 에러 상태 확인
			const state = get(authState);
			expect(state.error).toBeDefined();
			expect(state.error?.type).toBe('INVALID_CREDENTIALS');
			expect(state.error?.statusCode).toBe(401);
		});
	});

	describe('앱 재시작 후 인증 상태 복원', () => {
		it('유효한 토큰이 있을 때 자동으로 인증 상태를 복원해야 함', async () => {
			// 저장된 유효한 토큰 모킹
			const validAccessToken =
				'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxIiwiaWF0IjoxNzA0MDY3MjAwLCJleHAiOjk5OTk5OTk5OTksInR5cGUiOiJhY2Nlc3MifQ.valid-token';
			const validRefreshToken = 'valid-refresh-token';

			mockTauriStore.get.mockImplementation((key: string) => {
				if (key === 'access_token') return Promise.resolve(validAccessToken);
				if (key === 'refresh_token') return Promise.resolve(validRefreshToken);
				return Promise.resolve(null);
			});

			// 사용자 정보 API 응답 모킹
			mockAxios.get.mockResolvedValueOnce({
				data: {
					data: {
						user: testUser
					}
				}
			});

			// 인증 시스템 초기화
			await authActions.initialize();

			// 인증 상태 확인
			const state = get(authState);
			expect(state.isAuthenticated).toBe(true);
			expect(state.isInitialized).toBe(true);
			expect(state.user).toEqual(testUser);
			expect(state.accessToken).toBe(validAccessToken);
			expect(state.refreshToken).toBe(validRefreshToken);

			// 사용자 정보 로드 API 호출 확인
			expect(mockAxios.get).toHaveBeenCalledWith('/api/auth/me');
		});

		it('만료된 토큰이 있을 때 자동 갱신을 시도해야 함', async () => {
			// 만료된 액세스 토큰과 유효한 리프레시 토큰
			const expiredAccessToken =
				'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxIiwiaWF0IjoxNzA0MDY3MjAwLCJleHAiOjE3MDQwNjcyMDEsInR5cGUiOiJhY2Nlc3MifQ.expired-token';
			const validRefreshToken = 'valid-refresh-token';

			mockTauriStore.get.mockImplementation((key: string) => {
				if (key === 'access_token') return Promise.resolve(expiredAccessToken);
				if (key === 'refresh_token') return Promise.resolve(validRefreshToken);
				return Promise.resolve(null);
			});

			// 사용자 정보 API에서 401 오류 (만료된 토큰)
			mockAxios.get.mockRejectedValueOnce({
				response: { status: 401 }
			});

			// 토큰 갱신 API 응답
			mockAxios.post.mockResolvedValueOnce({
				data: testTokenResponse
			});

			// 갱신 후 사용자 정보 API 응답
			mockAxios.get.mockResolvedValueOnce({
				data: {
					data: {
						user: testUser
					}
				}
			});

			// 인증 시스템 초기화
			await authActions.initialize();

			// 토큰 갱신이 시도되었는지 확인
			expect(mockAxios.post).toHaveBeenCalledWith('/api/auth/refresh', {
				refresh_token: validRefreshToken
			});

			// 최종 인증 상태 확인
			const state = get(authState);
			expect(state.isAuthenticated).toBe(true);
			expect(state.user).toEqual(testUser);
		});

		it('토큰이 없을 때 비인증 상태로 초기화해야 함', async () => {
			// 저장된 토큰이 없음
			mockTauriStore.get.mockResolvedValue(null);

			// 인증 시스템 초기화
			await authActions.initialize();

			// 비인증 상태 확인
			const state = get(authState);
			expect(state.isAuthenticated).toBe(false);
			expect(state.isInitialized).toBe(true);
			expect(state.user).toBeNull();
			expect(state.accessToken).toBeNull();
			expect(state.refreshToken).toBeNull();

			// 사용자 정보 API가 호출되지 않았는지 확인
			expect(mockAxios.get).not.toHaveBeenCalled();
		});
	});

	describe('데스크탑 전용 플러그인 호환성', () => {
		it('데스크탑 환경에서 플러그인이 사용 가능해야 함', () => {
			// 플랫폼 감지 확인
			expect(getCurrentPlatform()).toBe('desktop');
			expect(isDesktop()).toBe(true);
			expect(isTauri()).toBe(true);

			// 데스크탑 전용 플러그인들이 정상적으로 로드되어야 함
			// (실제 플러그인 테스트는 E2E 테스트에서 수행)
			expect(true).toBe(true); // 플레이스홀더
		});

		it('Tauri Store API가 정상 동작해야 함', async () => {
			const storage = getTokenStorage();
			await storage.initialize();

			// Store 인스턴스가 생성되었는지 확인
			expect(storage).toBeDefined();

			// 기본 Store 메서드들이 사용 가능한지 확인
			await expect(storage.storeTokens('test-access', 'test-refresh')).resolves.not.toThrow();
			await expect(storage.getAccessToken()).resolves.not.toThrow();
			await expect(storage.clearTokens()).resolves.not.toThrow();
		});
	});

	describe('성능 및 메모리 관리', () => {
		it('토큰 저장소 접근이 효율적이어야 함', async () => {
			await tokenService.initialize();

			const startTime = Date.now();

			// 100회 토큰 저장/조회 테스트
			for (let i = 0; i < 100; i++) {
				await tokenService.storeTokens(`access-${i}`, `refresh-${i}`);
				await tokenService.getAccessToken();
				await tokenService.getRefreshToken();
			}

			const endTime = Date.now();
			const processingTime = endTime - startTime;

			console.log(`100회 토큰 저장/조회 시간: ${processingTime}ms`);

			// 처리 시간이 합리적인 범위 내에 있어야 함
			expect(processingTime).toBeLessThan(5000); // 5초 이내
		});

		it('메모리 누수가 없어야 함', async () => {
			await tokenService.initialize();

			// 반복적인 토큰 저장/삭제
			for (let i = 0; i < 50; i++) {
				await tokenService.storeTokens(`access-${i}`, `refresh-${i}`);
				await tokenService.clearTokens();
			}

			// 최종 상태 확인
			const accessToken = await tokenService.getAccessToken();
			const refreshToken = await tokenService.getRefreshToken();

			expect(accessToken).toBeNull();
			expect(refreshToken).toBeNull();
		});
	});

	describe('에러 처리 및 복구', () => {
		it('Tauri Store 접근 오류 시 안전하게 처리해야 함', async () => {
			// Store 접근 오류 모킹
			mockTauriStore.get.mockRejectedValue(new Error('Store access denied'));
			mockTauriStore.set.mockRejectedValue(new Error('Store write denied'));

			await tokenService.initialize();

			// 오류가 발생해도 null을 반환해야 함
			const accessToken = await tokenService.getAccessToken();
			const refreshToken = await tokenService.getRefreshToken();

			expect(accessToken).toBeNull();
			expect(refreshToken).toBeNull();
		});

		it('토큰 서비스 초기화 실패 시 적절히 처리해야 함', async () => {
			// 초기화 오류 모킹
			const originalInitialize = tokenService.initialize;
			tokenService.initialize = vi.fn().mockRejectedValue(new Error('Initialization failed'));

			// 인증 시스템 초기화 시도
			await authActions.initialize();

			// 에러 상태 확인
			const state = get(authState);
			expect(state.error).toBeDefined();
			expect(state.error?.type).toBe('UNKNOWN_ERROR');
			expect(state.isInitialized).toBe(true); // 실패해도 초기화 완료로 표시

			// 원래 함수 복원
			tokenService.initialize = originalInitialize;
		});
	});

	describe('디버그 및 개발 도구', () => {
		it('개발 환경에서 디버그 정보를 제공해야 함', async () => {
			await authActions.initialize();

			// 디버그 정보 출력
			await authActions.debugAuth();
			await tokenService.debugTokens();

			// 콘솔 로그가 출력되었는지 확인
			expect(console.log).toHaveBeenCalledWith(
				expect.stringContaining('[Auth Store Debug]'),
				expect.any(Object)
			);
		});

		it('토큰 상태 정보를 상세히 제공해야 함', async () => {
			const validToken =
				'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxIiwiaWF0IjoxNzA0MDY3MjAwLCJleHAiOjk5OTk5OTk5OTksInR5cGUiOiJhY2Nlc3MifQ.valid-token';

			mockTauriStore.get.mockImplementation((key: string) => {
				if (key === 'access_token') return Promise.resolve(validToken);
				if (key === 'refresh_token') return Promise.resolve('refresh-token');
				return Promise.resolve(null);
			});

			await tokenService.initialize();
			const status = await tokenService.getTokenStatus();

			expect(status).toMatchObject({
				hasAccessToken: true,
				hasRefreshToken: true,
				isAccessTokenValid: true,
				isRefreshTokenValid: true,
				userId: '1',
				accessTokenRemainingTime: expect.any(Number),
				refreshTokenRemainingTime: expect.any(Number)
			});

			expect(status.accessTokenRemainingTime).toBeGreaterThan(0);
		});
	});
});
