/**
 * 로컬스토리지 저장 및 로드 통합 테스트
 * 로컬스토리지와 관련된 모든 기능의 통합을 테스트합니다.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
	initBatchStorage,
	getBatchState,
	saveBatchState,
	resetBatchStorage,
	checkStorageCapacity,
	cleanupStorage,
	checkAutoSaveNeeded,
	createBackup,
	restoreFromBackup,
	getBackupList,
	clearBackups
} from '../utils/batchStorageUtils';
import {
	addProduct,
	updateProduct,
	removeProduct,
	getAllProducts,
	clearAllProducts,
	submitAllProducts,
	setCurrentPalletId,
	getCurrentPalletId,
	loadPalletData
} from '../services/batchProductService';
import { updateProductStatus } from '../services/batchProductStatusService';
import type { BatchStorageState, BatchProductData } from '../types/batchTypes';

// 로컬스토리지 모킹
const mockLocalStorage = (() => {
	let store: Record<string, string> = {};
	return {
		getItem: (key: string) => store[key] || null,
		setItem: (key: string, value: string) => {
			store[key] = value;
		},
		removeItem: (key: string) => {
			delete store[key];
		},
		clear: () => {
			store = {};
		},
		get length() {
			return Object.keys(store).length;
		},
		key: (index: number) => Object.keys(store)[index] || null,
		// 테스트용 헬퍼 메서드
		_getStore: () => ({ ...store }),
		_setStore: (newStore: Record<string, string>) => {
			store = { ...newStore };
		}
	};
})();

Object.defineProperty(window, 'localStorage', { value: mockLocalStorage });

// palletApiService 모킹
vi.mock('../services/palletApiService', () => ({
	saveProducts: vi.fn().mockResolvedValue({
		success: true,
		message: '저장 성공',
		successIds: ['1', '2', '3'],
		failedItems: []
	})
}));

describe('로컬스토리지 통합 테스트', () => {
	const testPalletId = 'A-1-1-1-1';

	beforeEach(() => {
		mockLocalStorage.clear();
		initBatchStorage(true);
		vi.clearAllMocks();
	});

	afterEach(() => {
		mockLocalStorage.clear();
		vi.restoreAllMocks();
	});

	describe('기본 스토리지 작업', () => {
		it('초기화 시 기본 구조가 생성되어야 함', () => {
			const state = getBatchState();

			expect(state).toMatchObject({
				pallets: {},
				totalCount: 0,
				pendingCount: 0,
				successCount: 0,
				failedCount: 0,
				submittingCount: 0,
				lastUpdated: expect.any(Number),
				currentPalletId: null
			});

			// 로컬스토리지에 저장되었는지 확인
			const stored = mockLocalStorage.getItem('batch_pallet_storage_state');
			expect(stored).not.toBeNull();
			expect(JSON.parse(stored!)).toMatchObject(state);
		});

		it('상태 저장 및 로드가 올바르게 동작해야 함', () => {
			const testState: BatchStorageState = {
				pallets: {
					[testPalletId]: {
						products: [
							{
								id: 'test-1',
								qaid: 'QA001',
								status: 'pending',
								timestamp: Date.now(),
								palletId: testPalletId,
								productInfo: { name: '테스트 상품' }
							}
						],
						palletInfo: { id: testPalletId, name: '테스트 팔레트' }
					}
				},
				totalCount: 1,
				pendingCount: 1,
				successCount: 0,
				failedCount: 0,
				submittingCount: 0,
				lastUpdated: Date.now(),
				currentPalletId: testPalletId
			};

			// 상태 저장
			saveBatchState(testState);

			// 로컬스토리지에서 직접 확인
			const stored = mockLocalStorage.getItem('batch_pallet_storage_state');
			expect(stored).not.toBeNull();

			const parsedStored = JSON.parse(stored!);
			expect(parsedStored.totalCount).toBe(1);
			expect(parsedStored.pallets[testPalletId].products).toHaveLength(1);

			// getBatchState로 로드 확인
			const loadedState = getBatchState();
			expect(loadedState.totalCount).toBe(1);
			expect(loadedState.pallets[testPalletId].products).toHaveLength(1);
		});

		it('스토리지 리셋이 올바르게 동작해야 함', async () => {
			// 데이터 추가
			await addProduct('QA001', { name: '테스트 상품' }, testPalletId);

			let state = getBatchState();
			expect(state.totalCount).toBe(1);

			// 리셋 실행
			resetBatchStorage();

			// 상태 확인
			state = getBatchState();
			expect(state.totalCount).toBe(0);
			expect(state.pallets).toEqual({});

			// 로컬스토리지 확인
			const stored = mockLocalStorage.getItem('batch_pallet_storage_state');
			const parsedStored = JSON.parse(stored!);
			expect(parsedStored.totalCount).toBe(0);
		});
	});

	describe('상품 데이터 영속성', () => {
		it('상품 추가 시 로컬스토리지에 즉시 저장되어야 함', async () => {
			await addProduct('QA001', { name: '테스트 상품 1' }, testPalletId);
			await addProduct('QA002', { name: '테스트 상품 2' }, testPalletId);

			// 로컬스토리지에서 직접 확인
			const stored = mockLocalStorage.getItem('batch_pallet_storage_state');
			const parsedStored = JSON.parse(stored!);

			expect(parsedStored.totalCount).toBe(2);
			expect(parsedStored.pendingCount).toBe(2);
			expect(parsedStored.pallets[testPalletId].products).toHaveLength(2);

			const products = parsedStored.pallets[testPalletId].products;
			expect(products.find((p: any) => p.qaid === 'QA001')).toBeDefined();
			expect(products.find((p: any) => p.qaid === 'QA002')).toBeDefined();
		});

		it('상품 수정 시 로컬스토리지가 업데이트되어야 함', async () => {
			await addProduct('QA001', { name: '원본 상품' }, testPalletId);

			const products = getAllProducts({ palletId: testPalletId });
			const productId = products[0].id;

			await updateProduct(productId, {
				qaid: 'QA001-UPDATED',
				productInfo: { name: '수정된 상품' }
			});

			// 로컬스토리지 확인
			const stored = mockLocalStorage.getItem('batch_pallet_storage_state');
			const parsedStored = JSON.parse(stored!);

			const updatedProduct = parsedStored.pallets[testPalletId].products[0];
			expect(updatedProduct.qaid).toBe('QA001-UPDATED');
			expect(updatedProduct.productInfo.name).toBe('수정된 상품');
		});

		it('상품 삭제 시 로컬스토리지에서 제거되어야 함', async () => {
			await addProduct('QA001', { name: '삭제될 상품' }, testPalletId);
			await addProduct('QA002', { name: '유지될 상품' }, testPalletId);

			const products = getAllProducts({ palletId: testPalletId });
			const productToDelete = products.find((p) => p.qaid === 'QA001')!;

			await removeProduct(productToDelete.id);

			// 로컬스토리지 확인
			const stored = mockLocalStorage.getItem('batch_pallet_storage_state');
			const parsedStored = JSON.parse(stored!);

			expect(parsedStored.totalCount).toBe(1);
			expect(parsedStored.pallets[testPalletId].products).toHaveLength(1);
			expect(parsedStored.pallets[testPalletId].products[0].qaid).toBe('QA002');
		});

		it('상품 상태 변경 시 로컬스토리지가 업데이트되어야 함', async () => {
			await addProduct('QA001', { name: '테스트 상품' }, testPalletId);

			const products = getAllProducts({ palletId: testPalletId });
			const productId = products[0].id;

			await updateProductStatus(productId, 'success');

			// 로컬스토리지 확인
			const stored = mockLocalStorage.getItem('batch_pallet_storage_state');
			const parsedStored = JSON.parse(stored!);

			expect(parsedStored.pendingCount).toBe(0);
			expect(parsedStored.successCount).toBe(1);
			expect(parsedStored.pallets[testPalletId].products[0].status).toBe('success');
		});
	});

	describe('팔레트별 데이터 관리', () => {
		it('여러 팔레트의 데이터가 독립적으로 저장되어야 함', async () => {
			const pallet1 = 'A-1-1-1-1';
			const pallet2 = 'A-1-1-1-2';

			await addProduct('QA001', { name: '팔레트1 상품1' }, pallet1);
			await addProduct('QA002', { name: '팔레트1 상품2' }, pallet1);
			await addProduct('QA003', { name: '팔레트2 상품1' }, pallet2);

			// 로컬스토리지 확인
			const stored = mockLocalStorage.getItem('batch_pallet_storage_state');
			const parsedStored = JSON.parse(stored!);

			expect(parsedStored.totalCount).toBe(3);
			expect(parsedStored.pallets[pallet1].products).toHaveLength(2);
			expect(parsedStored.pallets[pallet2].products).toHaveLength(1);

			// 각 팔레트의 상품이 올바른 팔레트 ID를 가져야 함
			expect(parsedStored.pallets[pallet1].products[0].palletId).toBe(pallet1);
			expect(parsedStored.pallets[pallet2].products[0].palletId).toBe(pallet2);
		});

		it('현재 팔레트 ID가 로컬스토리지에 저장되어야 함', async () => {
			await setCurrentPalletId(testPalletId);

			// 로컬스토리지 확인
			const stored = mockLocalStorage.getItem('batch_pallet_storage_state');
			const parsedStored = JSON.parse(stored!);

			expect(parsedStored.currentPalletId).toBe(testPalletId);

			// getCurrentPalletId로도 확인
			const currentPallet = await getCurrentPalletId();
			expect(currentPallet).toBe(testPalletId);
		});

		it('팔레트 데이터 로드가 올바르게 동작해야 함', async () => {
			// 여러 팔레트에 데이터 추가
			await addProduct('QA001', { name: '팔레트1 상품' }, 'A-1-1-1-1');
			await addProduct('QA002', { name: '팔레트2 상품' }, 'A-1-1-1-2');

			// 팔레트1 데이터 로드
			const pallet1Data = await loadPalletData('A-1-1-1-1');
			expect(pallet1Data.success).toBe(true);
			expect(pallet1Data.hasExistingData).toBe(true);
			expect(pallet1Data.products).toHaveLength(1);
			expect(pallet1Data.products[0].qaid).toBe('QA001');

			// 팔레트2 데이터 로드
			const pallet2Data = await loadPalletData('A-1-1-1-2');
			expect(pallet2Data.success).toBe(true);
			expect(pallet2Data.hasExistingData).toBe(true);
			expect(pallet2Data.products).toHaveLength(1);
			expect(pallet2Data.products[0].qaid).toBe('QA002');

			// 존재하지 않는 팔레트 로드
			const emptyPalletData = await loadPalletData('A-1-1-1-3');
			expect(emptyPalletData.success).toBe(true);
			expect(emptyPalletData.hasExistingData).toBe(false);
			expect(emptyPalletData.products).toHaveLength(0);
		});
	});

	describe('스토리지 용량 관리', () => {
		it('스토리지 용량을 정확히 계산해야 함', async () => {
			// 초기 용량 확인
			const initialCapacity = checkStorageCapacity();
			expect(initialCapacity.used).toBeGreaterThan(0);
			expect(initialCapacity.total).toBeGreaterThan(0);
			expect(initialCapacity.percentUsed).toBeGreaterThanOrEqual(0);

			// 데이터 추가 후 용량 변화 확인
			for (let i = 1; i <= 10; i++) {
				await addProduct(
					`QA${i.toString().padStart(3, '0')}`,
					{
						name: `상품 ${i}`,
						description: 'x'.repeat(100) // 더 큰 데이터
					},
					testPalletId
				);
			}

			const afterCapacity = checkStorageCapacity();
			expect(afterCapacity.used).toBeGreaterThan(initialCapacity.used);
			expect(afterCapacity.batchUsed).toBeGreaterThan(0);
		});

		it('자동 저장 필요 여부를 올바르게 판단해야 함', async () => {
			// 적은 수의 상품 - 자동 저장 불필요
			await addProduct('QA001', { name: '상품1' }, testPalletId);
			await addProduct('QA002', { name: '상품2' }, testPalletId);

			let autoSaveCheck = checkAutoSaveNeeded();
			expect(autoSaveCheck.needed).toBe(false);

			// 많은 수의 상품 추가 - 자동 저장 필요
			for (let i = 3; i <= 120; i++) {
				await addProduct(`QA${i.toString().padStart(3, '0')}`, { name: `상품${i}` }, testPalletId);
			}

			autoSaveCheck = checkAutoSaveNeeded();
			expect(autoSaveCheck.needed).toBe(true);
			expect(autoSaveCheck.reason).toContain('개의 상품이 임시 저장');
		});

		it('스토리지 정리가 올바르게 동작해야 함', async () => {
			// 다양한 상태의 상품 추가
			await addProduct('QA001', { name: '성공 상품1' }, testPalletId);
			await addProduct('QA002', { name: '성공 상품2' }, testPalletId);
			await addProduct('QA003', { name: '실패 상품' }, testPalletId);
			await addProduct('QA004', { name: '대기 상품' }, testPalletId);

			// 상태 변경
			const products = getAllProducts({ palletId: testPalletId });
			await updateProductStatus(products[0].id, 'success');
			await updateProductStatus(products[1].id, 'success');
			await updateProductStatus(products[2].id, 'failed', '테스트 오류');

			// 성공한 상품만 정리
			const cleanupResult = cleanupStorage({ removeSuccess: true });
			expect(cleanupResult.success).toBe(true);

			// 로컬스토리지 확인
			const stored = mockLocalStorage.getItem('batch_pallet_storage_state');
			const parsedStored = JSON.parse(stored!);

			expect(parsedStored.successCount).toBe(0);
			expect(parsedStored.failedCount).toBe(1);
			expect(parsedStored.pendingCount).toBe(1);
			expect(parsedStored.totalCount).toBe(2);
		});
	});

	describe('백업 및 복원 기능', () => {
		it('백업 생성 및 목록 조회가 동작해야 함', async () => {
			// 테스트 데이터 추가
			await addProduct('QA001', { name: '백업 테스트 상품' }, testPalletId);

			// 백업 생성
			createBackup();

			// 백업 목록 확인
			const backupList = getBackupList();
			expect(backupList.length).toBeGreaterThan(0);

			// 백업 키 형식 확인 (timestamp 기반)
			const backupKey = backupList[0];
			expect(backupKey).toMatch(/^batch_backup_\d+$/);

			// 로컬스토리지에 백업이 저장되었는지 확인
			const backupData = mockLocalStorage.getItem(backupKey);
			expect(backupData).not.toBeNull();

			const parsedBackup = JSON.parse(backupData!);
			expect(parsedBackup.totalCount).toBe(1);
		});

		it('백업에서 복원이 올바르게 동작해야 함', async () => {
			// 초기 데이터
			await addProduct('QA001', { name: '백업 상품1' }, testPalletId);
			await addProduct('QA002', { name: '백업 상품2' }, testPalletId);

			// 백업 생성
			createBackup();
			const backupList = getBackupList();
			const backupKey = backupList[0];

			// 데이터 변경
			await addProduct('QA003', { name: '추가 상품' }, testPalletId);
			clearAllProducts();

			let state = getBatchState();
			expect(state.totalCount).toBe(0);

			// 백업에서 복원
			const restoreResult = restoreFromBackup(backupKey);
			expect(restoreResult).toBe(true);

			// 복원된 상태 확인
			state = getBatchState();
			expect(state.totalCount).toBe(2);

			const products = getAllProducts({ palletId: testPalletId });
			expect(products).toHaveLength(2);
			expect(products.find((p) => p.qaid === 'QA001')).toBeDefined();
			expect(products.find((p) => p.qaid === 'QA002')).toBeDefined();
		});

		it('백업 정리가 동작해야 함', async () => {
			// 여러 백업 생성
			await addProduct('QA001', { name: '상품1' }, testPalletId);
			createBackup();

			await addProduct('QA002', { name: '상품2' }, testPalletId);
			createBackup();

			let backupList = getBackupList();
			expect(backupList.length).toBe(2);

			// 백업 정리
			clearBackups();

			backupList = getBackupList();
			expect(backupList.length).toBe(0);
		});
	});

	describe('데이터 무결성 및 복구', () => {
		it('손상된 데이터를 자동으로 복구해야 함', () => {
			// 손상된 데이터 직접 삽입
			const corruptedData = {
				pallets: {
					[testPalletId]: {
						products: [
							{
								id: 'valid-1',
								qaid: 'QA001',
								status: 'pending',
								timestamp: Date.now(),
								palletId: testPalletId
							},
							// 손상된 상품 데이터
							{
								id: null,
								qaid: null,
								status: 'invalid'
							}
						],
						palletInfo: {}
					}
				},
				// 잘못된 카운트
				totalCount: 'invalid',
				pendingCount: 'invalid',
				successCount: 0,
				failedCount: 0,
				submittingCount: 0,
				lastUpdated: Date.now(),
				currentPalletId: testPalletId
			};

			mockLocalStorage.setItem('batch_pallet_storage_state', JSON.stringify(corruptedData));

			// 상태 조회 시 자동 복구 확인
			const state = getBatchState();
			expect(state.totalCount).toBe(1); // 유효한 상품만 카운트
			expect(state.pallets[testPalletId].products).toHaveLength(1);
			expect(state.pallets[testPalletId].products[0].qaid).toBe('QA001');
		});

		it('JSON 파싱 오류 시 기본값으로 초기화해야 함', () => {
			// 잘못된 JSON 데이터 삽입
			mockLocalStorage.setItem('batch_pallet_storage_state', 'invalid json data');

			// 상태 조회 시 기본값으로 초기화되어야 함
			const state = getBatchState();
			expect(state.totalCount).toBe(0);
			expect(state.pallets).toEqual({});
			expect(state.currentPalletId).toBeNull();
		});

		it('스토리지 접근 오류 시 안전하게 처리해야 함', () => {
			// localStorage.getItem이 오류를 발생시키도록 모킹
			const originalGetItem = mockLocalStorage.getItem;
			mockLocalStorage.getItem = vi.fn().mockImplementation(() => {
				throw new Error('Storage access denied');
			});

			// 오류가 발생해도 기본값을 반환해야 함
			const state = getBatchState();
			expect(state.totalCount).toBe(0);
			expect(state.pallets).toEqual({});

			mockLocalStorage.getItem = originalGetItem;
		});
	});

	describe('성능 및 메모리 관리', () => {
		it('대량 데이터 처리 시 적절한 성능을 보여야 함', async () => {
			const startTime = Date.now();

			// 대량 상품 추가
			for (let i = 1; i <= 1000; i++) {
				await addProduct(
					`QA${i.toString().padStart(4, '0')}`,
					{
						name: `상품 ${i}`,
						description: `상품 ${i}의 상세 설명`
					},
					testPalletId
				);
			}

			const endTime = Date.now();
			const processingTime = endTime - startTime;

			console.log(`1000개 상품 로컬스토리지 저장 시간: ${processingTime}ms`);

			// 로컬스토리지 상태 확인
			const state = getBatchState();
			expect(state.totalCount).toBe(1000);
			expect(state.pallets[testPalletId].products).toHaveLength(1000);

			// 스토리지 용량 확인
			const capacity = checkStorageCapacity();
			expect(capacity.batchUsed).toBeGreaterThan(0);

			// 처리 시간이 합리적인 범위 내에 있어야 함
			expect(processingTime).toBeLessThan(10000); // 10초 이내
		});

		it('메모리 사용량이 적절해야 함', async () => {
			// 대량 데이터 추가 및 삭제 반복
			for (let cycle = 0; cycle < 5; cycle++) {
				// 100개 상품 추가
				for (let i = 1; i <= 100; i++) {
					await addProduct(
						`QA${cycle}-${i.toString().padStart(3, '0')}`,
						{
							name: `사이클${cycle} 상품${i}`
						},
						testPalletId
					);
				}

				// 모든 상품 삭제
				clearAllProducts();

				// 상태 확인
				const state = getBatchState();
				expect(state.totalCount).toBe(0);
			}

			// 최종 스토리지 상태 확인
			const finalCapacity = checkStorageCapacity();
			expect(finalCapacity.batchUsed).toBe(0);
		});

		it('동시 접근 시 데이터 일관성을 유지해야 함', async () => {
			// 동시에 여러 상품 추가 시뮬레이션
			const promises = [];
			for (let i = 1; i <= 50; i++) {
				promises.push(
					addProduct(`QA${i.toString().padStart(3, '0')}`, { name: `상품${i}` }, testPalletId)
				);
			}

			await Promise.all(promises);

			// 모든 상품이 올바르게 저장되었는지 확인
			const state = getBatchState();
			expect(state.totalCount).toBe(50);
			expect(state.pallets[testPalletId].products).toHaveLength(50);

			// 중복 ID가 없는지 확인
			const products = state.pallets[testPalletId].products;
			const ids = products.map((p) => p.id);
			const uniqueIds = new Set(ids);
			expect(uniqueIds.size).toBe(products.length);
		});
	});

	describe('브라우저 호환성', () => {
		it('localStorage가 비활성화된 환경에서 안전하게 처리해야 함', () => {
			// localStorage를 null로 설정
			Object.defineProperty(window, 'localStorage', { value: null });

			// 오류 없이 기본값을 반환해야 함
			const state = getBatchState();
			expect(state.totalCount).toBe(0);
			expect(state.pallets).toEqual({});

			// localStorage 복원
			Object.defineProperty(window, 'localStorage', { value: mockLocalStorage });
		});

		it('스토리지 할당량 초과 시 적절히 처리해야 함', async () => {
			// setItem이 할당량 초과 오류를 발생시키도록 모킹
			const originalSetItem = mockLocalStorage.setItem;
			mockLocalStorage.setItem = vi.fn().mockImplementation(() => {
				throw new DOMException('QuotaExceededError', 'QuotaExceededError');
			});

			// 상품 추가 시도
			const result = await addProduct('QA001', { name: '테스트 상품' }, testPalletId);

			// 오류가 발생해도 안전하게 처리되어야 함
			expect(result).toBe(false);

			mockLocalStorage.setItem = originalSetItem;
		});
	});
});
