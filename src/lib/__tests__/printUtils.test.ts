import { describe, it, expect } from 'vitest';
import { buildPalletPrintParams } from '../utils/printUtils';

describe('buildPalletPrintParams', () => {
  it('선택 항목이 없으면 빈 값 반환', () => {
    const result = buildPalletPrintParams([], [] as any);
    expect(result.exportDateParam).toBeNull();
    expect(result.palletCodeList).toBe('');
  });

  it('선택된 항목의 코드들을 | 로 연결', () => {
    const items = [
      { id: 1, exported_at: '2024-01-01 10:00:00', pallet_info: { code: 'P-001' } },
      { id: 2, exported_at: '2024-01-02 10:00:00', pallet_info: { code: 'P-002' } },
      { id: 3, exported_at: null, pallet_info: { code: 'P-003' } },
    ];
    const result = buildPalletPrintParams([1, 3, 2], items as any);
    expect(result.palletCodeList).toBe('P-001|P-003|P-002');
  });

  it('첫 번째 유효한 export_date를 exportDateParam에 설정', () => {
    const items = [
      { id: 10, exported_at: null, pallet_info: { code: 'PX-10' } },
      { id: 11, exported_at: '2024-05-05 09:00:00', pallet_info: { code: 'PX-11' } },
      { id: 12, exported_at: '2024-05-06 09:00:00', pallet_info: { code: 'PX-12' } },
    ];
    const result = buildPalletPrintParams([10, 12, 11], items as any);
    expect(result.exportDateParam).toBe('2024-05-06 09:00:00');
  });
});


