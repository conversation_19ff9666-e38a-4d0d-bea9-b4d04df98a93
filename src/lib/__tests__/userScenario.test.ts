/**
 * 사용자 시나리오 테스트
 * 실제 사용자가 시스템을 사용하는 시나리오를 테스트합니다.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
	addProduct,
	getAllProducts,
	submitAllProducts,
	retryFailedProducts,
	clearAllProducts,
	getProductCount,
	hasProducts,
	hasPendingProducts,
	hasFailedProducts,
	setCurrentPalletId,
	loadPalletData
} from '$lib/services/batchProductService';
import {
	initBatchStorage,
	getBatchState,
	checkStorageCapacity,
	detectDataRestore,
	createBackup,
	attemptDataRecovery,
	cleanupStorage
} from '$lib/utils/batchStorageUtils';
import type { BatchProductData } from '$lib/types/batchTypes';

// 모의 fetch 설정
global.fetch = vi.fn();

describe('사용자 시나리오 테스트', () => {
	beforeEach(async () => {
		// 로컬스토리지 초기화
		localStorage.clear();
		initBatchStorage();

		// 테스트용 팔레트 ID 설정
		await setCurrentPalletId('TEST_PALLET_001');

		// 모의 함수 초기화
		vi.clearAllMocks();

		// 기본 fetch 모의 설정
		(global.fetch as any).mockResolvedValue({
			ok: true,
			json: () => Promise.resolve({ success: true, data: { id: 'server-id' } })
		});
	});

	afterEach(() => {
		localStorage.clear();
		vi.clearAllMocks();
	});

	describe('시나리오 1: 바코드 스캔 및 임시 저장', () => {
		it('QAID 바코드를 스캔하면 로컬스토리지에 임시 저장되어야 한다', async () => {
			// Given: 초기 상태
			expect(hasProducts()).toBe(false);
			expect(getProductCount().total).toBe(0);

			// When: QAID 바코드 스캔
			const qaid1 = 'QAID001';
			const qaid2 = 'QAID002';

			await addProduct(qaid1, { name: '상품1', price: 1000 });
			await addProduct(qaid2, { name: '상품2', price: 2000 });

			// Then: 로컬스토리지에 저장되고 상태가 업데이트됨
			expect(hasProducts()).toBe(true);
			expect(getProductCount().total).toBe(2);
			expect(getProductCount().pending).toBe(2);
			expect(getProductCount().success).toBe(0);
			expect(getProductCount().failed).toBe(0);

			const products = getAllProducts();
			expect(products).toHaveLength(2);
			expect(products[0].qaid).toBe(qaid1);
			expect(products[0].status).toBe('pending');
			expect(products[1].qaid).toBe(qaid2);
			expect(products[1].status).toBe('pending');
		});

		it('동일한 QAID를 다시 스캔하면 기존 상품이 업데이트되어야 한다', async () => {
			// Given: 이미 저장된 상품
			const qaid = 'QAID001';
			await addProduct(qaid, { name: '상품1', price: 1000 });

			expect(getProductCount().total).toBe(1);

			// When: 동일한 QAID 다시 스캔
			await addProduct(qaid, { name: '상품1 수정', price: 1500 });

			// Then: 새로운 상품이 추가되지 않고 기존 상품이 업데이트됨
			expect(getProductCount().total).toBe(1);

			const products = getAllProducts();
			expect(products[0].productInfo.name).toBe('상품1 수정');
			expect(products[0].productInfo.price).toBe(1500);
		});

		it('여러 상품을 연속으로 스캔해도 모두 임시 저장되어야 한다', async () => {
			// Given: 여러 QAID 준비
			const qaids = ['QAID001', 'QAID002', 'QAID003', 'QAID004', 'QAID005'];

			// When: 연속으로 스캔
			for (let i = 0; i < qaids.length; i++) {
				await addProduct(qaids[i], { name: `상품${i + 1}`, price: (i + 1) * 1000 });
			}

			// Then: 모든 상품이 저장됨
			expect(getProductCount().total).toBe(5);
			expect(getProductCount().pending).toBe(5);

			const products = getAllProducts();
			expect(products).toHaveLength(5);
			products.forEach((product, index) => {
				expect(product.qaid).toBe(qaids[index]);
				expect(product.status).toBe('pending');
			});
		});
	});

	describe('시나리오 2: 일괄 저장 및 성공 처리', () => {
		it('일괄 저장 시 모든 상품이 서버로 전송되고 성공 처리되어야 한다', async () => {
			// Given: 여러 상품이 임시 저장됨
			await addProduct('QAID001', { name: '상품1', price: 1000 });
			await addProduct('QAID002', { name: '상품2', price: 2000 });
			await addProduct('QAID003', { name: '상품3', price: 3000 });

			expect(getProductCount().pending).toBe(3);

			// When: 일괄 저장 실행
			const result = await submitAllProducts();

			// Then: 모든 상품이 성공 처리됨
			expect(result.success).toBe(true);
			expect(getProductCount().pending).toBe(0);
			expect(getProductCount().success).toBe(3);
			expect(getProductCount().failed).toBe(0);

			// 서버 API가 호출되었는지 확인
			expect(global.fetch).toHaveBeenCalledTimes(3);
		});

		it('일괄 저장 성공 후 로컬스토리지가 정리되어야 한다', async () => {
			// Given: 상품들이 임시 저장됨
			await addProduct('QAID001', { name: '상품1', price: 1000 });
			await addProduct('QAID002', { name: '상품2', price: 2000 });

			// When: 일괄 저장 성공
			await submitAllProducts();

			// Then: 성공한 상품들은 로컬스토리지에서 제거됨
			const state = getBatchState();
			const pendingProducts = state.products.filter((p) => p.status === 'pending');
			expect(pendingProducts).toHaveLength(0);
		});
	});

	describe('시나리오 3: 오류 처리 및 재시도', () => {
		it('서버 오류 발생 시 실패한 상품들이 표시되어야 한다', async () => {
			// Given: 상품들이 임시 저장됨
			await addProduct('QAID001', { name: '상품1', price: 1000 });
			await addProduct('QAID002', { name: '상품2', price: 2000 });

			// 첫 번째 요청은 성공, 두 번째 요청은 실패하도록 설정
			(global.fetch as any)
				.mockResolvedValueOnce({
					ok: true,
					json: () => Promise.resolve({ success: true, data: { id: 'server-id-1' } })
				})
				.mockRejectedValueOnce(new Error('네트워크 오류'));

			// When: 일괄 저장 실행
			const result = await submitAllProducts();

			// Then: 일부 성공, 일부 실패
			expect(result.success).toBe(false); // 전체적으로는 실패
			expect(getProductCount().success).toBe(1);
			expect(getProductCount().failed).toBe(1);
			expect(hasFailedProducts()).toBe(true);

			const products = getAllProducts();
			const failedProduct = products.find((p) => p.status === 'failed');
			expect(failedProduct).toBeDefined();
			expect(failedProduct?.errorMessage).toContain('네트워크 오류');
		});

		it('실패한 상품들만 재시도할 수 있어야 한다', async () => {
			// Given: 일부 상품이 실패한 상태
			await addProduct('QAID001', { name: '상품1', price: 1000 });
			await addProduct('QAID002', { name: '상품2', price: 2000 });

			// 첫 번째는 성공, 두 번째는 실패
			(global.fetch as any)
				.mockResolvedValueOnce({
					ok: true,
					json: () => Promise.resolve({ success: true, data: { id: 'server-id-1' } })
				})
				.mockRejectedValueOnce(new Error('서버 오류'));

			await submitAllProducts();
			expect(getProductCount().failed).toBe(1);

			// When: 실패한 상품들 재시도
			(global.fetch as any).mockResolvedValueOnce({
				ok: true,
				json: () => Promise.resolve({ success: true, data: { id: 'server-id-2' } })
			});

			const retryResult = await retryFailedProducts();

			// Then: 재시도 성공
			expect(retryResult.success).toBe(true);
			expect(getProductCount().failed).toBe(0);
			expect(getProductCount().success).toBe(2);
		});

		it('재시도에서도 실패하면 계속 실패 상태로 유지되어야 한다', async () => {
			// Given: 실패한 상품이 있음
			await addProduct('QAID001', { name: '상품1', price: 1000 });

			(global.fetch as any).mockRejectedValue(new Error('지속적인 서버 오류'));
			await submitAllProducts();
			expect(getProductCount().failed).toBe(1);

			// When: 재시도해도 계속 실패
			const retryResult = await retryFailedProducts();

			// Then: 여전히 실패 상태
			expect(retryResult.success).toBe(false);
			expect(getProductCount().failed).toBe(1);
			expect(hasFailedProducts()).toBe(true);
		});
	});

	describe('시나리오 4: 페이지 새로고침 및 데이터 복원', () => {
		it('페이지 새로고침 후에도 임시 저장된 데이터가 복원되어야 한다', async () => {
			// Given: 상품들이 임시 저장됨
			await addProduct('QAID001', { name: '상품1', price: 1000 });
			await addProduct('QAID002', { name: '상품2', price: 2000 });

			const beforeRefresh = getAllProducts();
			expect(beforeRefresh).toHaveLength(2);

			// When: 페이지 새로고침 시뮬레이션 (스토리지 재초기화)
			initBatchStorage();

			// Then: 데이터가 복원됨
			const afterRefresh = getAllProducts();
			expect(afterRefresh).toHaveLength(2);
			expect(afterRefresh[0].qaid).toBe('QAID001');
			expect(afterRefresh[1].qaid).toBe('QAID002');
			expect(getProductCount().pending).toBe(2);
		});

		it('실패한 상품들도 페이지 새로고침 후 복원되어야 한다', async () => {
			// Given: 일부 상품이 실패한 상태
			await addProduct('QAID001', { name: '상품1', price: 1000 });
			await addProduct('QAID002', { name: '상품2', price: 2000 });

			// 첫 번째는 성공, 두 번째는 실패
			(global.fetch as any)
				.mockResolvedValueOnce({
					ok: true,
					json: () => Promise.resolve({ success: true, data: { id: 'server-id-1' } })
				})
				.mockRejectedValueOnce(new Error('서버 오류'));

			await submitAllProducts();
			expect(getProductCount().failed).toBe(1);
			expect(getProductCount().success).toBe(1);

			// When: 페이지 새로고침 시뮬레이션
			initBatchStorage();

			// Then: 실패한 상품만 복원됨 (성공한 상품은 제거됨)
			expect(getProductCount().failed).toBe(1);
			expect(getProductCount().success).toBe(0);
			expect(hasFailedProducts()).toBe(true);

			const products = getAllProducts();
			const failedProduct = products.find((p) => p.status === 'failed');
			expect(failedProduct?.qaid).toBe('QAID002');
		});

		it('브라우저 재시작 시뮬레이션 - 로컬스토리지 데이터 유지', () => {
			// Given: 로컬스토리지에 데이터 직접 저장
			const testData = {
				products: [
					{
						id: 'test-id-1',
						qaid: 'QAID001',
						timestamp: Date.now(),
						status: 'pending' as const,
						productInfo: { name: '상품1', price: 1000 }
					},
					{
						id: 'test-id-2',
						qaid: 'QAID002',
						timestamp: Date.now(),
						status: 'failed' as const,
						errorMessage: '이전 오류',
						productInfo: { name: '상품2', price: 2000 }
					}
				],
				totalCount: 2,
				pendingCount: 1,
				failedCount: 1,
				lastUpdated: Date.now()
			};

			localStorage.setItem('batch_storage_state', JSON.stringify(testData));

			// When: 새로운 세션 시작 (초기화)
			initBatchStorage();

			// Then: 데이터가 복원됨
			expect(getProductCount().total).toBe(2);
			expect(getProductCount().pending).toBe(1);
			expect(getProductCount().failed).toBe(1);

			const products = getAllProducts();
			expect(products).toHaveLength(2);
			expect(products.find((p) => p.qaid === 'QAID001')?.status).toBe('pending');
			expect(products.find((p) => p.qaid === 'QAID002')?.status).toBe('failed');
		});
	});

	describe('시나리오 5: 복합 시나리오', () => {
		it('전체 워크플로우: 스캔 → 임시저장 → 일부실패 → 재시도 → 성공', async () => {
			// 1단계: 여러 상품 스캔 및 임시 저장
			await addProduct('QAID001', { name: '상품1', price: 1000 });
			await addProduct('QAID002', { name: '상품2', price: 2000 });
			await addProduct('QAID003', { name: '상품3', price: 3000 });

			expect(getProductCount().pending).toBe(3);
			expect(getProductCount().total).toBe(3);

			// 2단계: 일괄 저장 시도 (일부 실패)
			(global.fetch as any)
				.mockResolvedValueOnce({
					ok: true,
					json: () => Promise.resolve({ success: true, data: { id: 'server-id-1' } })
				})
				.mockRejectedValueOnce(new Error('네트워크 오류'))
				.mockResolvedValueOnce({
					ok: true,
					json: () => Promise.resolve({ success: true, data: { id: 'server-id-3' } })
				});

			const firstSubmit = await submitAllProducts();
			expect(firstSubmit.success).toBe(false);
			expect(getProductCount().success).toBe(2);
			expect(getProductCount().failed).toBe(1);

			// 3단계: 페이지 새로고침 시뮬레이션
			initBatchStorage();
			expect(getProductCount().failed).toBe(1);
			expect(getProductCount().success).toBe(0); // 성공한 것들은 제거됨

			// 4단계: 실패한 상품 재시도
			(global.fetch as any).mockResolvedValueOnce({
				ok: true,
				json: () => Promise.resolve({ success: true, data: { id: 'server-id-2' } })
			});

			const retryResult = await retryFailedProducts();
			expect(retryResult.success).toBe(true);
			expect(getProductCount().failed).toBe(0);
			expect(getProductCount().total).toBe(0); // 모든 상품이 성공적으로 처리됨
		});

		it('대량 데이터 처리 시나리오', async () => {
			// Given: 대량의 상품 데이터
			const productCount = 50;
			const products = Array.from({ length: productCount }, (_, i) => ({
				qaid: `QAID${String(i + 1).padStart(3, '0')}`,
				productInfo: { name: `상품${i + 1}`, price: (i + 1) * 100 }
			}));

			// When: 대량 스캔
			for (const product of products) {
				await addProduct(product.qaid, product.productInfo);
			}

			// Then: 모든 상품이 저장됨
			expect(getProductCount().total).toBe(productCount);
			expect(getProductCount().pending).toBe(productCount);

			// 스토리지 용량 확인
			const capacity = checkStorageCapacity();
			expect(capacity.used).toBeGreaterThan(0);
			expect(capacity.percentUsed).toBeGreaterThan(0);

			// 일괄 저장 (모두 성공)
			const result = await submitAllProducts();
			expect(result.success).toBe(true);
			expect(getProductCount().success).toBe(productCount);
			expect(getProductCount().pending).toBe(0);
		});
	});

	describe('시나리오 6: 에러 상황 처리', () => {
		it('로컬스토리지 데이터 손상 시 안전하게 초기화되어야 한다', () => {
			// Given: 손상된 데이터를 로컬스토리지에 저장
			localStorage.setItem('batch_pallet_storage_state', '잘못된 JSON 데이터');

			// When: 초기화 시도
			expect(() => initBatchStorage()).not.toThrow();

			// Then: 안전하게 초기화됨
			expect(getProductCount().total).toBe(0);
			expect(hasProducts()).toBe(false);
		});

		it('로컬스토리지 용량 부족 시나리오', async () => {
			// Given: 로컬스토리지 용량 확인
			const initialCapacity = checkStorageCapacity();
			expect(initialCapacity).toBeDefined();

			// When: 많은 데이터 저장 시도
			const largeProductInfo = {
				name: '매우 긴 상품명'.repeat(100),
				description: '매우 긴 설명'.repeat(100),
				price: 10000
			};

			// 용량 제한에 도달할 때까지 상품 추가
			let addedCount = 0;
			try {
				for (let i = 0; i < 100; i++) {
					await addProduct(`QAID${String(i).padStart(3, '0')}`, largeProductInfo);
					addedCount++;
				}
			} catch (error) {
				// 용량 부족 오류가 발생할 수 있음
			}

			// Then: 용량 상태 확인
			const finalCapacity = checkStorageCapacity();
			expect(finalCapacity.used).toBeGreaterThan(initialCapacity.used);
		});

		it('데이터 복구 기능 테스트', () => {
			// Given: 일부 손상된 데이터 구조
			const partiallyCorruptedData = {
				pallets: {
					TEST_PALLET_001: {
						products: [
							{
								id: 'valid-id-1',
								qaid: 'QAID001',
								timestamp: Date.now(),
								status: 'pending',
								palletId: 'TEST_PALLET_001',
								productInfo: { name: '정상 상품' }
							},
							{
								// 일부 필드 누락된 손상된 데이터
								qaid: 'QAID002',
								status: 'pending'
							}
						]
					}
				},
				// 일부 필드 누락
				totalCount: 2
			};

			localStorage.setItem('batch_pallet_storage_state', JSON.stringify(partiallyCorruptedData));

			// When: 데이터 복구 시도
			const recoveryResult = attemptDataRecovery();

			// Then: 복구 성공 및 유효한 데이터만 유지
			expect(recoveryResult).toBe(true);
			const state = getBatchState();
			expect(state.pallets['TEST_PALLET_001'].products).toHaveLength(1);
			expect(state.pallets['TEST_PALLET_001'].products[0].qaid).toBe('QAID001');
		});
	});

	describe('시나리오 7: 팔레트 관리 기능', () => {
		it('팔레트 변경 시 해당 팔레트 데이터가 로드되어야 한다', async () => {
			// Given: 여러 팔레트에 상품 저장
			await setCurrentPalletId('PALLET_A');
			await addProduct('QAID_A001', { name: '팔레트A 상품1' });
			await addProduct('QAID_A002', { name: '팔레트A 상품2' });

			await setCurrentPalletId('PALLET_B');
			await addProduct('QAID_B001', { name: '팔레트B 상품1' });

			// When: 팔레트 A로 변경
			const loadResult = await loadPalletData('PALLET_A');

			// Then: 팔레트 A의 데이터가 로드됨
			expect(loadResult.success).toBe(true);
			expect(loadResult.products).toHaveLength(2);
			expect(loadResult.counts.total).toBe(2);
			expect(loadResult.hasExistingData).toBe(true);
			expect(loadResult.products.find((p) => p.qaid === 'QAID_A001')).toBeDefined();
			expect(loadResult.products.find((p) => p.qaid === 'QAID_A002')).toBeDefined();
		});

		it('새로운 팔레트로 변경 시 빈 상태로 시작되어야 한다', async () => {
			// Given: 기존 팔레트에 상품이 있음
			await addProduct('QAID001', { name: '기존 상품' });
			expect(getProductCount().total).toBe(1);

			// When: 새로운 팔레트로 변경
			const loadResult = await loadPalletData('NEW_PALLET');

			// Then: 새 팔레트는 빈 상태
			expect(loadResult.success).toBe(true);
			expect(loadResult.products).toHaveLength(0);
			expect(loadResult.counts.total).toBe(0);
			expect(loadResult.hasExistingData).toBe(false);
		});

		it('팔레트별 상품 격리가 제대로 작동해야 한다', async () => {
			// Given: 두 개의 팔레트에 각각 상품 추가
			await setCurrentPalletId('PALLET_1');
			await addProduct('QAID001', { name: '팔레트1 상품' });

			await setCurrentPalletId('PALLET_2');
			await addProduct('QAID002', { name: '팔레트2 상품' });

			// When: 각 팔레트의 상품 수 확인
			const pallet1Count = getProductCount('PALLET_1');
			const pallet2Count = getProductCount('PALLET_2');

			// Then: 각 팔레트는 자신의 상품만 가지고 있음
			expect(pallet1Count.total).toBe(1);
			expect(pallet2Count.total).toBe(1);

			// 전체 상품 수는 2개
			const totalCount = getProductCount();
			expect(totalCount.total).toBe(2);
		});
	});

	describe('시나리오 8: 데이터 복원 및 알림', () => {
		it('페이지 로드 시 복원 가능한 데이터 감지', async () => {
			// Given: 이전 세션에서 저장된 데이터
			await addProduct('QAID001', { name: '이전 세션 상품1' });
			await addProduct('QAID002', { name: '이전 세션 상품2' });

			// 시간을 과거로 설정하여 복원 대상으로 만들기
			const state = getBatchState();
			const oldTimestamp = Date.now() - 10 * 60 * 1000; // 10분 전
			state.pallets['TEST_PALLET_001'].products.forEach((product) => {
				product.timestamp = oldTimestamp;
			});
			localStorage.setItem('batch_pallet_storage_state', JSON.stringify(state));

			// When: 데이터 복원 감지
			const restoreInfo = detectDataRestore('TEST_PALLET_001');

			// Then: 복원 가능한 데이터가 감지됨
			expect(restoreInfo.hasRestorableData).toBe(true);
			expect(restoreInfo.restoreInfo).toBeDefined();
			expect(restoreInfo.restoreInfo?.palletId).toBe('TEST_PALLET_001');
			expect(restoreInfo.restoreInfo?.productCount).toBe(2);
			expect(restoreInfo.restoreInfo?.pendingCount).toBe(2);
		});

		it('복원 알림이 적절한 조건에서만 표시되어야 한다', async () => {
			// Given: 최근에 저장된 데이터 (복원 알림 불필요)
			await addProduct('QAID001', { name: '최근 상품' });

			// When: 데이터 복원 감지
			const restoreInfo = detectDataRestore('TEST_PALLET_001');

			// Then: 복원 알림이 표시되지 않음
			expect(restoreInfo.hasRestorableData).toBe(false);
			expect(restoreInfo.restoreInfo).toBeNull();
		});

		it('여러 팔레트 중 복원 우선순위가 올바르게 결정되어야 한다', async () => {
			// Given: 여러 팔레트에 다른 시간의 데이터
			const oldTime = Date.now() - 10 * 60 * 1000;
			const veryOldTime = Date.now() - 30 * 60 * 1000;

			// 팔레트 A - 10분 전 데이터
			await setCurrentPalletId('PALLET_A');
			await addProduct('QAID_A001', { name: '팔레트A 상품' });

			// 팔레트 B - 30분 전 데이터 (더 오래됨)
			await setCurrentPalletId('PALLET_B');
			await addProduct('QAID_B001', { name: '팔레트B 상품' });

			// 시간 조작
			const state = getBatchState();
			state.pallets['PALLET_A'].products[0].timestamp = oldTime;
			state.pallets['PALLET_B'].products[0].timestamp = veryOldTime;
			localStorage.setItem('batch_pallet_storage_state', JSON.stringify(state));

			// When: 현재 팔레트를 A로 설정하고 복원 감지
			const restoreInfo = detectDataRestore('PALLET_A');

			// Then: 현재 팔레트(A)가 우선적으로 선택됨
			expect(restoreInfo.hasRestorableData).toBe(true);
			expect(restoreInfo.restoreInfo?.palletId).toBe('PALLET_A');
			expect(restoreInfo.restoreInfo?.isCurrentPallet).toBe(true);
		});
	});

	describe('시나리오 9: 스토리지 관리 및 최적화', () => {
		it('스토리지 정리 기능이 올바르게 작동해야 한다', async () => {
			// Given: 성공한 상품과 실패한 상품이 혼재
			await addProduct('QAID001', { name: '상품1' });
			await addProduct('QAID002', { name: '상품2' });
			await addProduct('QAID003', { name: '상품3' });

			// 일부 상품을 성공 상태로 변경
			const state = getBatchState();
			state.pallets['TEST_PALLET_001'].products[0].status = 'success';
			state.pallets['TEST_PALLET_001'].products[1].status = 'failed';
			state.pallets['TEST_PALLET_001'].products[1].errorMessage = '테스트 오류';
			localStorage.setItem('batch_pallet_storage_state', JSON.stringify(state));

			// When: 스토리지 정리 실행
			const cleanupResult = cleanupStorage({
				removeSuccess: true,
				removeOldBackups: true,
				compressData: true
			});

			// Then: 성공한 상품이 제거되고 실패한 상품은 유지됨
			expect(cleanupResult.success).toBe(true);
			const afterState = getBatchState();
			expect(afterState.pallets['TEST_PALLET_001'].products).toHaveLength(2); // 실패 + 대기 상품만 남음
			expect(
				afterState.pallets['TEST_PALLET_001'].products.find((p) => p.status === 'success')
			).toBeUndefined();
		});

		it('백업 생성 및 복원 기능이 작동해야 한다', async () => {
			// Given: 테스트 데이터
			await addProduct('QAID001', { name: '백업 테스트 상품' });
			const originalState = getBatchState();

			// When: 백업 생성
			createBackup();

			// 데이터 변경
			await addProduct('QAID002', { name: '새로운 상품' });
			expect(getProductCount().total).toBe(2);

			// Then: 백업이 생성되었는지 확인
			let backupFound = false;
			for (let i = 0; i < localStorage.length; i++) {
				const key = localStorage.key(i);
				if (key && key.includes('_backup_')) {
					backupFound = true;
					break;
				}
			}
			expect(backupFound).toBe(true);
		});

		it('용량 경고 임계값이 올바르게 작동해야 한다', () => {
			// Given: 테스트용 작은 쿼터 설정
			const testQuota = 100; // 100KB

			// When: 용량 확인 (테스트 쿼터 사용)
			const capacity = checkStorageCapacity(testQuota);

			// Then: 용량 정보가 올바르게 계산됨
			expect(capacity.total).toBe(testQuota);
			expect(capacity.used).toBeGreaterThanOrEqual(0);
			expect(capacity.available).toBeLessThanOrEqual(testQuota);
			expect(capacity.percentUsed).toBeGreaterThanOrEqual(0);
			expect(capacity.percentUsed).toBeLessThanOrEqual(100);
		});
	});

	describe('시나리오 10: 네트워크 연결 상태별 처리', () => {
		it('네트워크 연결 실패 시 모든 상품이 실패 상태로 표시되어야 한다', async () => {
			// Given: 여러 상품이 대기 중
			await addProduct('QAID001', { name: '상품1' });
			await addProduct('QAID002', { name: '상품2' });
			await addProduct('QAID003', { name: '상품3' });

			// 네트워크 오류 시뮬레이션
			(global.fetch as any).mockRejectedValue(new Error('네트워크 연결 실패'));

			// When: 일괄 저장 시도
			const result = await submitAllProducts();

			// Then: 모든 상품이 실패 상태
			expect(result.success).toBe(false);
			expect(getProductCount().failed).toBe(3);
			expect(getProductCount().pending).toBe(0);
		});

		it('부분적 네트워크 실패 시 성공/실패가 혼재되어야 한다', async () => {
			// Given: 여러 상품이 대기 중
			await addProduct('QAID001', { name: '상품1' });
			await addProduct('QAID002', { name: '상품2' });
			await addProduct('QAID003', { name: '상품3' });

			// 부분적 실패 시뮬레이션 (첫 번째와 세 번째는 성공, 두 번째는 실패)
			(global.fetch as any)
				.mockResolvedValueOnce({
					ok: true,
					json: () => Promise.resolve({ success: true, data: { id: 'server-id-1' } })
				})
				.mockRejectedValueOnce(new Error('서버 오류'))
				.mockResolvedValueOnce({
					ok: true,
					json: () => Promise.resolve({ success: true, data: { id: 'server-id-3' } })
				});

			// When: 일괄 저장 시도
			const result = await submitAllProducts();

			// Then: 부분적 성공/실패
			expect(result.success).toBe(false); // 전체적으로는 실패 (일부 실패 포함)
			expect(getProductCount().success).toBe(2);
			expect(getProductCount().failed).toBe(1);
		});

		it('네트워크 복구 후 재시도가 성공해야 한다', async () => {
			// Given: 네트워크 실패로 인한 실패 상품들
			await addProduct('QAID001', { name: '상품1' });
			await addProduct('QAID002', { name: '상품2' });

			(global.fetch as any).mockRejectedValue(new Error('네트워크 오류'));
			await submitAllProducts();
			expect(getProductCount().failed).toBe(2);

			// When: 네트워크 복구 후 재시도
			(global.fetch as any).mockResolvedValue({
				ok: true,
				json: () => Promise.resolve({ success: true, data: { id: 'server-id' } })
			});

			const retryResult = await retryFailedProducts();

			// Then: 재시도 성공
			expect(retryResult.success).toBe(true);
			expect(getProductCount().failed).toBe(0);
			expect(getProductCount().success).toBe(2);
		});
	});

	describe('시나리오 11: 동시성 및 경합 상태 처리', () => {
		it('동일한 QAID를 빠르게 연속 스캔해도 중복되지 않아야 한다', async () => {
			// Given: 동일한 QAID
			const qaid = 'QAID001';

			// When: 빠른 연속 스캔 시뮬레이션
			const promises = [
				addProduct(qaid, { name: '상품1', scan: 1 }),
				addProduct(qaid, { name: '상품1', scan: 2 }),
				addProduct(qaid, { name: '상품1', scan: 3 })
			];

			await Promise.all(promises);

			// Then: 하나의 상품만 존재 (마지막 스캔으로 업데이트)
			expect(getProductCount().total).toBe(1);
			const products = getAllProducts();
			expect(products[0].qaid).toBe(qaid);
		});

		it('여러 팔레트에서 동시 작업이 서로 영향을 주지 않아야 한다', async () => {
			// Given: 두 개의 팔레트에서 동시 작업
			const pallet1Operations = async () => {
				await setCurrentPalletId('PALLET_1');
				await addProduct('QAID_P1_001', { name: '팔레트1 상품1' });
				await addProduct('QAID_P1_002', { name: '팔레트1 상품2' });
			};

			const pallet2Operations = async () => {
				await setCurrentPalletId('PALLET_2');
				await addProduct('QAID_P2_001', { name: '팔레트2 상품1' });
				await addProduct('QAID_P2_002', { name: '팔레트2 상품2' });
			};

			// When: 동시 실행
			await Promise.all([pallet1Operations(), pallet2Operations()]);

			// Then: 각 팔레트가 독립적으로 관리됨
			const pallet1Count = getProductCount('PALLET_1');
			const pallet2Count = getProductCount('PALLET_2');

			expect(pallet1Count.total).toBe(2);
			expect(pallet2Count.total).toBe(2);

			// 전체 상품 수는 4개
			expect(getProductCount().total).toBe(4);
		});
	});

	describe('시나리오 12: 브라우저 재시작 및 세션 복원', () => {
		it('브라우저 재시작 후 데이터가 완전히 복원되어야 한다', async () => {
			// Given: 다양한 상태의 상품들
			await addProduct('QAID001', { name: '대기 상품' });
			await addProduct('QAID002', { name: '실패 상품' });

			// 상품 상태 변경
			const state = getBatchState();
			state.pallets['TEST_PALLET_001'].products[1].status = 'failed';
			state.pallets['TEST_PALLET_001'].products[1].errorMessage = '서버 오류';
			localStorage.setItem('batch_pallet_storage_state', JSON.stringify(state));

			// When: 브라우저 재시작 시뮬레이션 (새로운 초기화)
			const initResult = initBatchStorage();

			// Then: 기존 데이터가 복원됨
			expect(initResult.success).toBe(true);
			expect(initResult.isNew).toBe(false);
			expect(getProductCount().total).toBe(2);
			expect(getProductCount().pending).toBe(1);
			expect(getProductCount().failed).toBe(1);
		});

		it('장기간 미사용 후 데이터 복원 시 알림이 표시되어야 한다', async () => {
			// Given: 오래된 데이터
			await addProduct('QAID001', { name: '오래된 상품' });

			// 타임스탬프를 1시간 전으로 설정
			const state = getBatchState();
			const oldTimestamp = Date.now() - 60 * 60 * 1000; // 1시간 전
			state.pallets['TEST_PALLET_001'].products[0].timestamp = oldTimestamp;
			localStorage.setItem('batch_pallet_storage_state', JSON.stringify(state));

			// When: 데이터 복원 감지
			const restoreInfo = detectDataRestore('TEST_PALLET_001');

			// Then: 복원 알림이 필요함
			expect(restoreInfo.hasRestorableData).toBe(true);
			expect(restoreInfo.restoreInfo?.shouldNotify).toBe(true);
			expect(restoreInfo.restoreInfo?.timeSinceLastUpdate).toBeGreaterThan(60 * 60 * 1000);
		});
	});

	describe('시나리오 13: 성능 및 대용량 데이터 처리', () => {
		it('대량의 상품 처리 시 성능이 유지되어야 한다', async () => {
			// Given: 대량의 상품 데이터
			const productCount = 200;
			const startTime = Date.now();

			// When: 대량 상품 추가
			for (let i = 0; i < productCount; i++) {
				await addProduct(`QAID${String(i).padStart(4, '0')}`, {
					name: `상품${i + 1}`,
					price: (i + 1) * 100,
					category: `카테고리${Math.floor(i / 10) + 1}`
				});
			}

			const addTime = Date.now() - startTime;

			// Then: 모든 상품이 추가되고 성능이 합리적임
			expect(getProductCount().total).toBe(productCount);
			expect(addTime).toBeLessThan(5000); // 5초 이내

			// 조회 성능 테스트
			const queryStartTime = Date.now();
			const allProducts = getAllProducts();
			const queryTime = Date.now() - queryStartTime;

			expect(allProducts).toHaveLength(productCount);
			expect(queryTime).toBeLessThan(1000); // 1초 이내
		});

		it('메모리 사용량이 적절히 관리되어야 한다', async () => {
			// Given: 상품 추가 및 삭제 반복
			for (let cycle = 0; cycle < 5; cycle++) {
				// 상품 추가
				for (let i = 0; i < 50; i++) {
					await addProduct(`CYCLE${cycle}_QAID${i}`, { name: `사이클${cycle} 상품${i}` });
				}

				// 일부 상품 성공 처리 후 정리
				const state = getBatchState();
				state.pallets['TEST_PALLET_001'].products.forEach((product, index) => {
					if (index % 2 === 0) {
						product.status = 'success';
					}
				});
				localStorage.setItem('batch_pallet_storage_state', JSON.stringify(state));

				// 스토리지 정리
				cleanupStorage({ removeSuccess: true });
			}

			// Then: 메모리 사용량이 적절히 관리됨
			const finalCount = getProductCount();
			expect(finalCount.total).toBeLessThan(150); // 정리로 인해 상품 수가 제한됨
			expect(finalCount.success).toBe(0); // 성공한 상품은 정리됨
		});
	});
});
