<!--
  인증 디버그 패널 컴포넌트 (개발 환경 전용)
  
  JWT 인증 시스템의 상태와 디버그 정보를 실시간으로 표시합니다.
  개발 환경에서만 표시되며, 인증 관련 문제 해결에 도움을 줍니다.
-->

<script lang="ts">
	import { onMount } from 'svelte';
	import { browser } from '$app/environment';
	import { authState, authActions } from '$lib/stores/authStore';
	import { tokenService } from '$lib/services/tokenService';
	import { getCurrentPlatform } from '$lib/services/platformService';
	import { getErrorStats, resetErrorStats } from '$lib/utils/authErrors';
	import type { AuthState } from '$lib/stores/authStore';
	import type { TokenStatus } from '$lib/services/tokenService';

	// 개발 환경이 아니면 렌더링하지 않음
	if (!import.meta.env.DEV) {
		// 빈 컴포넌트 렌더링
	}

	// Props (Svelte 5 방식)
	let {
		position = 'bottom-right',
		collapsed = true,
		autoRefresh = true,
		refreshInterval = 2000 // 2초
	}: {
		position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
		collapsed?: boolean;
		autoRefresh?: boolean;
		refreshInterval?: number;
	} = $props();

	// 내부 상태
	let isCollapsed = $state(collapsed);
	let currentAuthState = $state<AuthState | null>(null);
	let tokenStatus = $state<TokenStatus | null>(null);
	let platform = $state<string>('');
	let errorStats = $state<Record<string, number>>({});
	let refreshTimer: ReturnType<typeof setInterval> | null = null;
	let lastRefresh = $state<Date | null>(null);

	// 파생된 상태들
	const hasAuthState = $derived(!!currentAuthState);
	const hasTokenStatus = $derived(!!tokenStatus);
	const isAuthenticated = $derived(currentAuthState?.isAuthenticated || false);
	const hasErrors = $derived(Object.keys(errorStats).length > 0);

	/**
	 * 디버그 정보를 새로고침합니다.
	 */
	async function refreshDebugInfo(): Promise<void> {
		if (!browser) return;

		try {
			// 토큰 상태 조회
			tokenStatus = await tokenService.getTokenStatus();

			// 플랫폼 정보 조회
			platform = getCurrentPlatform();

			// 에러 통계 조회
			errorStats = getErrorStats();

			lastRefresh = new Date();
		} catch (error) {
			console.error('[AuthDebugPanel] 디버그 정보 새로고침 실패:', error);
		}
	}

	/**
	 * 자동 새로고침 설정
	 */
	function setupAutoRefresh(): void {
		if (!autoRefresh) return;

		refreshTimer = setInterval(refreshDebugInfo, refreshInterval);
	}

	/**
	 * 자동 새로고침 정리
	 */
	function clearAutoRefresh(): void {
		if (refreshTimer) {
			clearInterval(refreshTimer);
			refreshTimer = null;
		}
	}

	/**
	 * 패널 토글
	 */
	function togglePanel(): void {
		isCollapsed = !isCollapsed;
		if (!isCollapsed) {
			refreshDebugInfo();
		}
	}

	/**
	 * 인증 상태 디버그 출력
	 */
	async function debugAuthState(): Promise<void> {
		await authActions.debugAuth();
	}

	/**
	 * 토큰 디버그 출력
	 */
	async function debugTokens(): Promise<void> {
		await tokenService.debugTokens();
	}

	/**
	 * 에러 통계 초기화
	 */
	function clearErrorStats(): void {
		resetErrorStats();
		errorStats = {};
	}

	/**
	 * 강제 토큰 갱신
	 */
	async function forceRefreshToken(): Promise<void> {
		try {
			const result = await authActions.refreshAuth();
			console.log('[AuthDebugPanel] 강제 토큰 갱신 결과:', result);
			await refreshDebugInfo();
		} catch (error) {
			console.error('[AuthDebugPanel] 강제 토큰 갱신 실패:', error);
		}
	}

	/**
	 * 강제 로그아웃
	 */
	async function forceLogout(): Promise<void> {
		try {
			await authActions.logout();
			console.log('[AuthDebugPanel] 강제 로그아웃 완료');
		} catch (error) {
			console.error('[AuthDebugPanel] 강제 로그아웃 실패:', error);
		}
	}

	/**
	 * 시간 포맷팅
	 */
	function formatTime(date: Date | null): string {
		if (!date) return 'N/A';
		return date.toLocaleTimeString();
	}

	/**
	 * 남은 시간 포맷팅 (초 단위)
	 */
	function formatRemainingTime(seconds: number): string {
		if (seconds <= 0) return '만료됨';

		const minutes = Math.floor(seconds / 60);
		const remainingSeconds = seconds % 60;

		if (minutes > 0) {
			return `${minutes}분 ${remainingSeconds}초`;
		}
		return `${remainingSeconds}초`;
	}

	// 인증 상태 구독
	$effect(() => {
		if (!browser) return;

		const unsubscribe = authState.subscribe((state) => {
			currentAuthState = state;
		});

		return unsubscribe;
	});

	// 컴포넌트 마운트 시 초기화
	onMount(() => {
		if (!import.meta.env.DEV || !browser) return;

		// 초기 데이터 로드
		refreshDebugInfo();

		// 자동 새로고침 설정
		setupAutoRefresh();

		// 정리 함수
		return () => {
			clearAutoRefresh();
		};
	});

	// 자동 새로고침 설정 변경 시 재설정
	$effect(() => {
		clearAutoRefresh();
		if (autoRefresh) {
			setupAutoRefresh();
		}
	});
</script>

{#if import.meta.env.DEV}
	<div class="auth-debug-panel {position}" class:collapsed={isCollapsed}>
		<!-- 헤더 -->
		<div class="debug-header" onclick={togglePanel}>
			<span class="debug-title">🔐 Auth Debug</span>
			<span class="debug-toggle">{isCollapsed ? '▲' : '▼'}</span>
		</div>

		<!-- 패널 내용 -->
		{#if !isCollapsed}
			<div class="debug-content">
				<!-- 기본 정보 -->
				<div class="debug-section">
					<div class="section-title">기본 정보</div>
					<div class="info-grid">
						<div class="info-item">
							<span class="info-label">플랫폼:</span>
							<span class="info-value">{platform}</span>
						</div>
						<div class="info-item">
							<span class="info-label">마지막 새로고침:</span>
							<span class="info-value">{formatTime(lastRefresh)}</span>
						</div>
						<div class="info-item">
							<span class="info-label">인증 상태:</span>
							<span class="info-value status" class:authenticated={isAuthenticated}>
								{isAuthenticated ? '✅ 인증됨' : '❌ 미인증'}
							</span>
						</div>
					</div>
				</div>

				<!-- 인증 상태 -->
				{#if hasAuthState && currentAuthState}
					<div class="debug-section">
						<div class="section-title">인증 상태</div>
						<div class="info-grid">
							<div class="info-item">
								<span class="info-label">초기화됨:</span>
								<span class="info-value">{currentAuthState.isInitialized ? '✅' : '❌'}</span>
							</div>
							<div class="info-item">
								<span class="info-label">로딩 중:</span>
								<span class="info-value">{currentAuthState.isLoading ? '⏳' : '✅'}</span>
							</div>
							<div class="info-item">
								<span class="info-label">사용자:</span>
								<span class="info-value">{currentAuthState.user?.name || 'N/A'}</span>
							</div>
							<div class="info-item">
								<span class="info-label">에러:</span>
								<span class="info-value">{currentAuthState.error?.type || 'None'}</span>
							</div>
						</div>
					</div>
				{/if}

				<!-- 토큰 상태 -->
				{#if hasTokenStatus && tokenStatus}
					<div class="debug-section">
						<div class="section-title">토큰 상태</div>
						<div class="info-grid">
							<div class="info-item">
								<span class="info-label">액세스 토큰:</span>
								<span class="info-value">{tokenStatus.hasAccessToken ? '✅' : '❌'}</span>
							</div>
							<div class="info-item">
								<span class="info-label">리프레시 토큰:</span>
								<span class="info-value">{tokenStatus.hasRefreshToken ? '✅' : '❌'}</span>
							</div>
							<div class="info-item">
								<span class="info-label">액세스 토큰 유효:</span>
								<span class="info-value">{tokenStatus.isAccessTokenValid ? '✅' : '❌'}</span>
							</div>
							<div class="info-item">
								<span class="info-label">리프레시 토큰 유효:</span>
								<span class="info-value">{tokenStatus.isRefreshTokenValid ? '✅' : '❌'}</span>
							</div>
							<div class="info-item">
								<span class="info-label">액세스 토큰 남은 시간:</span>
								<span class="info-value"
									>{formatRemainingTime(tokenStatus.accessTokenRemainingTime)}</span
								>
							</div>
							<div class="info-item">
								<span class="info-label">사용자 ID:</span>
								<span class="info-value">{tokenStatus.userId || 'N/A'}</span>
							</div>
						</div>
					</div>
				{/if}

				<!-- 에러 통계 -->
				{#if hasErrors}
					<div class="debug-section">
						<div class="section-title">
							에러 통계
							<button class="clear-button" onclick={clearErrorStats}>초기화</button>
						</div>
						<div class="error-stats">
							{#each Object.entries(errorStats) as [errorType, count]}
								<div class="error-stat-item">
									<span class="error-type">{errorType}</span>
									<span class="error-count">{count}</span>
								</div>
							{/each}
						</div>
					</div>
				{/if}

				<!-- 액션 버튼들 -->
				<div class="debug-section">
					<div class="section-title">액션</div>
					<div class="action-buttons">
						<button onclick={refreshDebugInfo}>🔄 새로고침</button>
						<button onclick={debugAuthState}>📊 인증 상태 로그</button>
						<button onclick={debugTokens}>🔑 토큰 로그</button>
						{#if isAuthenticated}
							<button onclick={forceRefreshToken}>🔄 토큰 갱신</button>
							<button onclick={forceLogout}>🚪 강제 로그아웃</button>
						{/if}
					</div>
				</div>
			</div>
		{/if}
	</div>
{/if}

<style>
	.auth-debug-panel {
		position: fixed;
		z-index: 10000;
		background: #1f2937;
		color: #f9fafb;
		border-radius: 8px;
		box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
		font-family: 'Courier New', monospace;
		font-size: 12px;
		max-width: 400px;
		max-height: 80vh;
		overflow-y: auto;
	}

	.auth-debug-panel.top-left {
		top: 20px;
		left: 20px;
	}

	.auth-debug-panel.top-right {
		top: 20px;
		right: 20px;
	}

	.auth-debug-panel.bottom-left {
		bottom: 20px;
		left: 20px;
	}

	.auth-debug-panel.bottom-right {
		bottom: 20px;
		right: 20px;
	}

	.auth-debug-panel.collapsed {
		max-height: none;
		overflow: visible;
	}

	.debug-header {
		background: #374151;
		padding: 8px 12px;
		border-radius: 8px 8px 0 0;
		cursor: pointer;
		display: flex;
		justify-content: space-between;
		align-items: center;
		user-select: none;
	}

	.collapsed .debug-header {
		border-radius: 8px;
	}

	.debug-title {
		font-weight: bold;
		font-size: 13px;
	}

	.debug-toggle {
		font-size: 10px;
		opacity: 0.7;
	}

	.debug-content {
		padding: 12px;
		max-height: 60vh;
		overflow-y: auto;
	}

	.debug-section {
		margin-bottom: 16px;
	}

	.debug-section:last-child {
		margin-bottom: 0;
	}

	.section-title {
		font-weight: bold;
		color: #60a5fa;
		margin-bottom: 8px;
		font-size: 13px;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.clear-button {
		background: #ef4444;
		color: white;
		border: none;
		border-radius: 4px;
		padding: 2px 6px;
		font-size: 10px;
		cursor: pointer;
	}

	.clear-button:hover {
		background: #dc2626;
	}

	.info-grid {
		display: grid;
		gap: 4px;
	}

	.info-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 2px 0;
	}

	.info-label {
		color: #9ca3af;
		font-size: 11px;
	}

	.info-value {
		color: #f3f4f6;
		font-weight: 500;
	}

	.info-value.status.authenticated {
		color: #10b981;
	}

	.error-stats {
		display: grid;
		gap: 4px;
	}

	.error-stat-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 2px 8px;
		background: #374151;
		border-radius: 4px;
	}

	.error-type {
		font-size: 10px;
		color: #fbbf24;
	}

	.error-count {
		background: #ef4444;
		color: white;
		border-radius: 50%;
		width: 18px;
		height: 18px;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 10px;
		font-weight: bold;
	}

	.action-buttons {
		display: grid;
		gap: 6px;
		grid-template-columns: 1fr 1fr;
	}

	.action-buttons button {
		background: #4b5563;
		color: #f9fafb;
		border: none;
		border-radius: 4px;
		padding: 6px 8px;
		font-size: 10px;
		cursor: pointer;
		transition: background 0.2s;
	}

	.action-buttons button:hover {
		background: #6b7280;
	}

	/* 스크롤바 스타일링 */
	.auth-debug-panel::-webkit-scrollbar,
	.debug-content::-webkit-scrollbar {
		width: 6px;
	}

	.auth-debug-panel::-webkit-scrollbar-track,
	.debug-content::-webkit-scrollbar-track {
		background: #374151;
	}

	.auth-debug-panel::-webkit-scrollbar-thumb,
	.debug-content::-webkit-scrollbar-thumb {
		background: #6b7280;
		border-radius: 3px;
	}

	.auth-debug-panel::-webkit-scrollbar-thumb:hover,
	.debug-content::-webkit-scrollbar-thumb:hover {
		background: #9ca3af;
	}
</style>
