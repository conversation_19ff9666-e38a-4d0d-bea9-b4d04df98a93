<!--
  인증 에러 표시 컴포넌트
  
  JWT 인증 시스템에서 발생하는 에러를 사용자 친화적으로 표시합니다.
  Svelte 5 호환 방식으로 구현되었습니다.
-->

<script lang="ts">
	import { onMount } from 'svelte';
	import type { AuthError } from '$lib/utils/authErrors';
	import {
		getErrorColor,
		getErrorIcon,
		getRecoveryStrategy,
		logAuthError,
		trackError
	} from '$lib/utils/authErrors';

	// Props (Svelte 5 방식)
	let {
		error = null,
		showDetails = false,
		autoHide = true,
		autoHideDelay = 5000, // 5초
		showRetryButton = true,
		showDismissButton = true,
		compact = false,
		// Callback props (Svelte 5 방식)
		onretry = undefined,
		ondismiss = undefined,
		ondetailstoggle = undefined
	}: {
		error?: AuthError | null;
		showDetails?: boolean;
		autoHide?: boolean;
		autoHideDelay?: number;
		showRetryButton?: boolean;
		showDismissButton?: boolean;
		compact?: boolean;
		onretry?: (() => void) | undefined;
		ondismiss?: (() => void) | undefined;
		ondetailstoggle?: ((show: boolean) => void) | undefined;
	} = $props();

	// 내부 상태
	let isVisible = $state(false);
	let isDetailsExpanded = $state(false);
	let autoHideTimer: ReturnType<typeof setTimeout> | null = null;

	// 파생된 상태들
	const hasError = $derived(!!error);
	const errorColor = $derived(error ? getErrorColor(error.severity) : '#6b7280');
	const errorIcon = $derived(error ? getErrorIcon(error.severity) : 'ℹ️');
	const recoveryStrategy = $derived(error ? getRecoveryStrategy(error) : null);
	const canRetry = $derived(error?.retryable && showRetryButton);
	const showDetailsButton = $derived(showDetails && error?.details);

	// 에러 변경 시 처리
	$effect(() => {
		if (error) {
			isVisible = true;
			isDetailsExpanded = false;

			// 에러 로깅 및 추적
			logAuthError(error, 'AuthErrorDisplay');
			trackError(error);

			// 자동 숨김 타이머 설정
			if (autoHide && error.severity !== 'critical') {
				setupAutoHideTimer();
			}
		} else {
			isVisible = false;
			clearAutoHideTimer();
		}
	});

	/**
	 * 자동 숨김 타이머 설정
	 */
	function setupAutoHideTimer(): void {
		clearAutoHideTimer();
		autoHideTimer = setTimeout(() => {
			handleDismiss();
		}, autoHideDelay);
	}

	/**
	 * 자동 숨김 타이머 정리
	 */
	function clearAutoHideTimer(): void {
		if (autoHideTimer) {
			clearTimeout(autoHideTimer);
			autoHideTimer = null;
		}
	}

	/**
	 * 재시도 버튼 클릭 처리
	 */
	function handleRetry(): void {
		clearAutoHideTimer();
		onretry?.();
	}

	/**
	 * 닫기 버튼 클릭 처리
	 */
	function handleDismiss(): void {
		clearAutoHideTimer();
		isVisible = false;
		ondismiss?.();
	}

	/**
	 * 상세 정보 토글 처리
	 */
	function handleDetailsToggle(): void {
		isDetailsExpanded = !isDetailsExpanded;
		ondetailstoggle?.(isDetailsExpanded);
	}

	/**
	 * 에러 복사 (개발 환경에서만)
	 */
	function copyErrorToClipboard(): void {
		if (!import.meta.env.DEV || !error) return;

		const errorInfo = {
			type: error.type,
			message: error.message,
			userMessage: error.userMessage,
			severity: error.severity,
			category: error.category,
			statusCode: error.statusCode,
			timestamp: error.timestamp,
			details: error.details,
			debugInfo: error.debugInfo
		};

		navigator.clipboard
			?.writeText(JSON.stringify(errorInfo, null, 2))
			.then(() => console.log('에러 정보가 클립보드에 복사되었습니다.'))
			.catch(console.error);
	}

	// 컴포넌트 언마운트 시 타이머 정리
	onMount(() => {
		return () => clearAutoHideTimer();
	});
</script>

{#if hasError && isVisible && error}
	<div
		class="auth-error-display"
		class:compact
		style="--error-color: {errorColor}"
		role="alert"
		aria-live="polite"
	>
		<!-- 메인 에러 메시지 -->
		<div class="error-main">
			<div class="error-icon">
				{errorIcon}
			</div>

			<div class="error-content">
				<div class="error-message">
					{error.userMessage}
				</div>

				{#if !compact && error.message !== error.userMessage}
					<div class="error-technical">
						{error.message}
					</div>
				{/if}

				{#if recoveryStrategy}
					<div class="error-recovery">
						{recoveryStrategy.description}
					</div>
				{/if}
			</div>

			<!-- 액션 버튼들 -->
			<div class="error-actions">
				{#if canRetry}
					<button class="retry-button" onclick={handleRetry} title="다시 시도"> 🔄 </button>
				{/if}

				{#if showDetailsButton}
					<button
						class="details-button"
						onclick={handleDetailsToggle}
						title="상세 정보"
						aria-expanded={isDetailsExpanded}
					>
						{isDetailsExpanded ? '▲' : '▼'}
					</button>
				{/if}

				{#if import.meta.env.DEV}
					<button
						class="copy-button"
						onclick={copyErrorToClipboard}
						title="에러 정보 복사 (개발용)"
					>
						📋
					</button>
				{/if}

				{#if showDismissButton}
					<button class="dismiss-button" onclick={handleDismiss} title="닫기"> ✕ </button>
				{/if}
			</div>
		</div>

		<!-- 상세 정보 (확장 시) -->
		{#if isDetailsExpanded && error.details}
			<div class="error-details">
				<div class="details-header">상세 정보</div>
				<div class="details-content">
					{#if typeof error.details === 'string'}
						<pre>{error.details}</pre>
					{:else}
						<pre>{JSON.stringify(error.details, null, 2)}</pre>
					{/if}
				</div>

				{#if import.meta.env.DEV && error.debugInfo}
					<div class="debug-info">
						<div class="debug-header">디버그 정보</div>
						<div class="debug-content">
							<div><strong>타입:</strong> {error.type}</div>
							<div><strong>심각도:</strong> {error.severity}</div>
							<div><strong>카테고리:</strong> {error.category}</div>
							<div><strong>시간:</strong> {error.timestamp.toLocaleString()}</div>
							{#if error.statusCode}
								<div><strong>상태 코드:</strong> {error.statusCode}</div>
							{/if}
							{#if error.debugInfo.platform}
								<div><strong>플랫폼:</strong> {error.debugInfo.platform}</div>
							{/if}
						</div>
					</div>
				{/if}
			</div>
		{/if}
	</div>
{/if}

<style>
	.auth-error-display {
		background: #fef2f2;
		border: 1px solid var(--error-color);
		border-radius: 8px;
		padding: 1rem;
		margin: 0.5rem 0;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
		animation: slideIn 0.3s ease-out;
	}

	.auth-error-display.compact {
		padding: 0.75rem;
		margin: 0.25rem 0;
	}

	@keyframes slideIn {
		from {
			opacity: 0;
			transform: translateY(-10px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.error-main {
		display: flex;
		align-items: flex-start;
		gap: 0.75rem;
	}

	.error-icon {
		font-size: 1.25rem;
		flex-shrink: 0;
		margin-top: 0.125rem;
	}

	.error-content {
		flex: 1;
		min-width: 0;
	}

	.error-message {
		font-weight: 600;
		color: #991b1b;
		margin-bottom: 0.25rem;
	}

	.error-technical {
		font-size: 0.875rem;
		color: #7f1d1d;
		margin-bottom: 0.25rem;
		font-family: monospace;
	}

	.error-recovery {
		font-size: 0.875rem;
		color: #6b7280;
		font-style: italic;
	}

	.error-actions {
		display: flex;
		gap: 0.5rem;
		flex-shrink: 0;
	}

	.error-actions button {
		background: none;
		border: 1px solid var(--error-color);
		border-radius: 4px;
		padding: 0.25rem 0.5rem;
		cursor: pointer;
		font-size: 0.875rem;
		transition: all 0.2s;
	}

	.error-actions button:hover {
		background: var(--error-color);
		color: white;
	}

	.retry-button {
		color: #059669;
		border-color: #059669;
	}

	.retry-button:hover {
		background: #059669;
	}

	.details-button {
		color: #3b82f6;
		border-color: #3b82f6;
	}

	.details-button:hover {
		background: #3b82f6;
	}

	.copy-button {
		color: #8b5cf6;
		border-color: #8b5cf6;
	}

	.copy-button:hover {
		background: #8b5cf6;
	}

	.dismiss-button {
		color: #6b7280;
		border-color: #6b7280;
	}

	.dismiss-button:hover {
		background: #6b7280;
	}

	.error-details {
		margin-top: 1rem;
		padding-top: 1rem;
		border-top: 1px solid #fecaca;
	}

	.details-header,
	.debug-header {
		font-weight: 600;
		color: #991b1b;
		margin-bottom: 0.5rem;
		font-size: 0.875rem;
	}

	.details-content,
	.debug-content {
		background: #fff;
		border: 1px solid #fecaca;
		border-radius: 4px;
		padding: 0.75rem;
		font-size: 0.875rem;
	}

	.details-content pre {
		margin: 0;
		white-space: pre-wrap;
		word-break: break-word;
		font-family: 'Courier New', monospace;
		color: #374151;
	}

	.debug-info {
		margin-top: 0.75rem;
	}

	.debug-content {
		background: #f9fafb;
		border-color: #d1d5db;
	}

	.debug-content div {
		margin-bottom: 0.25rem;
	}

	.debug-content strong {
		color: #374151;
		margin-right: 0.5rem;
	}

	/* 컴팩트 모드 스타일 */
	.compact .error-main {
		gap: 0.5rem;
	}

	.compact .error-icon {
		font-size: 1rem;
	}

	.compact .error-message {
		font-size: 0.875rem;
		margin-bottom: 0;
	}

	.compact .error-technical,
	.compact .error-recovery {
		display: none;
	}

	.compact .error-actions button {
		padding: 0.125rem 0.375rem;
		font-size: 0.75rem;
	}
</style>
