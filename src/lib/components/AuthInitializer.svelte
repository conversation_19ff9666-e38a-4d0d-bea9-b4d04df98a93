<!--
  JWT 인증 시스템 초기화 컴포넌트
  
  이 컴포넌트는 앱 시작 시 인증 상태를 초기화하고 관리합니다.
  루트 레이아웃에서 사용하여 전체 앱의 인증 상태를 관리합니다.
-->

<script lang="ts">
	import { onMount } from 'svelte';
	import { browser } from '$app/environment';
	import { goto } from '$app/navigation';
	import { authActions, authState, type AuthState } from '$lib/stores/authStore';
	import { tokenService } from '$lib/services/tokenService';

	// Props (Svelte 5 방식)
	let {
		showLoadingScreen = true,
		loadingMessage = '인증 상태를 확인하는 중...',
		autoRedirectToLogin = true,
		loginPath = '/login',
		defaultRedirectPath = '/',
		protectedPaths = [], // 보호된 경로 목록
		publicPaths = ['/login', '/register'], // 공개 경로 목록
		children
	}: {
		showLoadingScreen?: boolean;
		loadingMessage?: string;
		autoRedirectToLogin?: boolean;
		loginPath?: string;
		defaultRedirectPath?: string;
		protectedPaths?: string[];
		publicPaths?: string[];
		children?: import('svelte').Snippet;
	} = $props();

	// 내부 상태
	let isInitializing = $state(true);
	let initializationError = $state<string | null>(null);
	let currentAuthState = $state<AuthState | null>(null);
	let currentPath = $state('/');

	// 페이지 정보 업데이트
	$effect(() => {
		if (browser) {
			currentPath = window.location.pathname;

			// URL 변경 감지를 위한 이벤트 리스너
			const handleLocationChange = () => {
				currentPath = window.location.pathname;
			};

			window.addEventListener('popstate', handleLocationChange);

			return () => {
				window.removeEventListener('popstate', handleLocationChange);
			};
		}
	});

	// 인증 상태 구독
	$effect(() => {
		const unsubscribe = authState.subscribe((state) => {
			currentAuthState = state;
		});
		return unsubscribe;
	});

	// 초기화 완료 여부
	const isReady = $derived(() => {
		return currentAuthState?.isInitialized && !isInitializing;
	});

	// 현재 경로가 보호된 경로인지 확인
	const isProtectedPath = $derived(() => {
		if (!browser) return false;

		// 명시적으로 공개 경로로 지정된 경우
		if (publicPaths.some((path) => currentPath.startsWith(path))) {
			return false;
		}

		// 명시적으로 보호된 경로로 지정된 경우
		if (protectedPaths.length > 0) {
			return protectedPaths.some((path) => currentPath.startsWith(path));
		}

		// 기본적으로 로그인 페이지가 아니면 보호된 경로로 간주
		return !currentPath.startsWith(loginPath);
	});

	// 인증이 필요한지 확인
	const requiresAuth = $derived(() => {
		return isProtectedPath && !currentAuthState?.isAuthenticated;
	});

	// 로그인 페이지에서 이미 인증된 사용자 처리
	const shouldRedirectFromLogin = $derived(() => {
		if (!browser) return false;
		return currentPath.startsWith(loginPath) && currentAuthState?.isAuthenticated;
	});

	/**
	 * 인증 시스템을 초기화합니다.
	 */
	async function initializeAuth(): Promise<void> {
		try {
			isInitializing = true;
			initializationError = null;

			if (import.meta.env.DEV) {
				console.log('[AuthInitializer] 인증 초기화 시작');
			}

			// 인증 시스템 초기화
			await authActions.initialize();

			// 토큰 상태 확인
			const tokenStatus = await tokenService.getTokenStatus();

			if (import.meta.env.DEV) {
				console.log('[AuthInitializer] 토큰 상태:', tokenStatus);
			}

			// 토큰이 있지만 만료된 경우 갱신 시도
			if (tokenStatus.hasRefreshToken && !tokenStatus.isAccessTokenValid) {
				if (import.meta.env.DEV) {
					console.log('[AuthInitializer] 액세스 토큰 만료, 갱신 시도');
				}

				const refreshed = await authActions.refreshAuth();
				if (!refreshed) {
					if (import.meta.env.DEV) {
						console.log('[AuthInitializer] 토큰 갱신 실패, 로그아웃 처리');
					}
				}
			}

			if (import.meta.env.DEV) {
				console.log('[AuthInitializer] 인증 초기화 완료');
			}
		} catch (error) {
			console.error('[AuthInitializer] 인증 초기화 실패:', error);
			initializationError = '인증 시스템 초기화에 실패했습니다.';
		} finally {
			isInitializing = false;
		}
	}

	/**
	 * 경로 기반 리다이렉트를 처리합니다.
	 */
	async function handleRouteRedirect(): Promise<void> {
		if (!browser || !isReady) return;

		try {
			// 로그인 페이지에서 이미 인증된 사용자 리다이렉트
			if (shouldRedirectFromLogin) {
				const urlParams = new URLSearchParams(window.location.search);
				const redirectTo = urlParams.get('redirect') || defaultRedirectPath;
				if (import.meta.env.DEV) {
					console.log('[AuthInitializer] 인증된 사용자를 리다이렉트:', redirectTo);
				}
				await goto(redirectTo);
				return;
			}

			// 보호된 경로에서 인증되지 않은 사용자 리다이렉트
			if (requiresAuth && autoRedirectToLogin) {
				const currentPathWithSearch = window.location.pathname + window.location.search;
				const loginUrl = `${loginPath}?redirect=${encodeURIComponent(currentPathWithSearch)}`;

				if (import.meta.env.DEV) {
					console.log('[AuthInitializer] 인증되지 않은 사용자를 로그인 페이지로 리다이렉트');
				}

				await goto(loginUrl);
				return;
			}
		} catch (error) {
			console.error('[AuthInitializer] 리다이렉트 처리 실패:', error);
		}
	}

	/**
	 * 토큰 만료 모니터링을 설정합니다.
	 */
	function setupTokenMonitoring(): (() => void) | undefined {
		if (!browser) return;

		// 5분마다 토큰 상태 확인
		const interval = setInterval(
			async () => {
				if (!currentAuthState?.isAuthenticated) {
					clearInterval(interval);
					return;
				}

				try {
					const isExpiringSoon = await tokenService.isAccessTokenExpiringSoon(5);
					if (isExpiringSoon) {
						if (import.meta.env.DEV) {
							console.log('[AuthInitializer] 토큰 만료 임박, 자동 갱신 시도');
						}
						await authActions.refreshAuth();
					}
				} catch (error) {
					console.error('[AuthInitializer] 토큰 모니터링 오류:', error);
				}
			},
			5 * 60 * 1000
		); // 5분

		// 컴포넌트 언마운트 시 정리
		return () => clearInterval(interval);
	}

	/**
	 * 페이지 가시성 변경 시 토큰 상태 확인
	 */
	function setupVisibilityChangeHandler(): (() => void) | undefined {
		if (!browser) return;

		const handleVisibilityChange = async () => {
			if (document.visibilityState === 'visible' && currentAuthState?.isAuthenticated) {
				try {
					// 페이지가 다시 보일 때 토큰 상태 확인
					const tokenStatus = await tokenService.getTokenStatus();

					if (!tokenStatus.isAccessTokenValid && tokenStatus.hasRefreshToken) {
						if (import.meta.env.DEV) {
							console.log('[AuthInitializer] 페이지 복귀 시 토큰 갱신');
						}
						await authActions.refreshAuth();
					} else if (!tokenStatus.isAccessTokenValid && !tokenStatus.hasRefreshToken) {
						if (import.meta.env.DEV) {
							console.log('[AuthInitializer] 페이지 복귀 시 토큰 만료 확인, 로그아웃 처리');
						}
						await authActions.logout();
					}
				} catch (error) {
					console.error('[AuthInitializer] 페이지 가시성 변경 처리 오류:', error);
				}
			}
		};

		document.addEventListener('visibilitychange', handleVisibilityChange);

		// 컴포넌트 언마운트 시 정리
		return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
	}

	// 컴포넌트 마운트 시 초기화
	onMount(() => {
		if (!browser) return;

		let cleanupTokenMonitoring: (() => void) | undefined;
		let cleanupVisibilityHandler: (() => void) | undefined;

		// 비동기 초기화 함수
		const initialize = async () => {
			// 인증 시스템 초기화
			await initializeAuth();

			// 토큰 모니터링 설정
			cleanupTokenMonitoring = setupTokenMonitoring();

			// 페이지 가시성 변경 핸들러 설정
			cleanupVisibilityHandler = setupVisibilityChangeHandler();
		};

		// 초기화 실행
		initialize().catch(console.error);

		// 정리 함수 반환
		return () => {
			cleanupTokenMonitoring?.();
			cleanupVisibilityHandler?.();
		};
	});

	// 인증 상태나 경로 변경 시 리다이렉트 처리
	$effect(() => {
		handleRouteRedirect().catch(console.error);
	});

	// 개발 환경에서 상태 변경 로깅
	$effect(() => {
		if (import.meta.env.DEV && currentAuthState) {
			console.log('[AuthInitializer] 인증 상태 변경:', {
				isAuthenticated: currentAuthState.isAuthenticated,
				isInitialized: currentAuthState.isInitialized,
				isLoading: currentAuthState.isLoading,
				hasUser: !!currentAuthState.user,
				currentPath: browser ? currentPath : 'N/A'
			});
		}
	});
</script>

<!-- 초기화 중 로딩 화면 -->
{#if showLoadingScreen && (isInitializing || !isReady)}
	<div class="auth-initializer-loading">
		<div class="loading-content">
			<div class="loading-spinner"></div>
			<p class="loading-message">{loadingMessage}</p>

			{#if initializationError}
				<div class="error-message">
					<p>{initializationError}</p>
					<button onclick={() => window.location.reload()} class="retry-button"> 다시 시도 </button>
				</div>
			{/if}
		</div>
	</div>
{:else}
	<!-- 초기화 완료 후 슬롯 내용 표시 -->
	{#if children}
		{@render children()}
	{/if}
{/if}

<style>
	.auth-initializer-loading {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: rgba(255, 255, 255, 0.95);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
	}

	.loading-content {
		text-align: center;
		padding: 2rem;
	}

	.loading-spinner {
		width: 40px;
		height: 40px;
		border: 4px solid #f3f3f3;
		border-top: 4px solid #3498db;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin: 0 auto 1rem;
	}

	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}
		100% {
			transform: rotate(360deg);
		}
	}

	.loading-message {
		color: #666;
		font-size: 1rem;
		margin: 0;
	}

	.error-message {
		margin-top: 1rem;
		padding: 1rem;
		background: #fee;
		border: 1px solid #fcc;
		border-radius: 4px;
		color: #c33;
	}

	.retry-button {
		margin-top: 0.5rem;
		padding: 0.5rem 1rem;
		background: #3498db;
		color: white;
		border: none;
		border-radius: 4px;
		cursor: pointer;
		font-size: 0.9rem;
	}

	.retry-button:hover {
		background: #2980b9;
	}
</style>
