<!--
  배치 데이터 복원 알림 컴포넌트
  
  페이지 로드 시 로컬스토리지에서 복원된 데이터가 있을 때 사용자에게 알림을 표시합니다.
  요구사항 6.3: 로컬스토리지 데이터가 복원될 때 사용자에게 복원된 데이터가 있음을 알림
-->

<script lang="ts">
	import { onMount, createEventDispatcher } from 'svelte';
	import { fade, slide } from 'svelte/transition';
	import Icon from 'svelte-awesome';
	import { faInfoCircle } from '@fortawesome/free-solid-svg-icons/faInfoCircle';
	import { faCheckCircle } from '@fortawesome/free-solid-svg-icons/faCheckCircle';
	import { faTimes } from '@fortawesome/free-solid-svg-icons/faTimes';
	import { faHistory } from '@fortawesome/free-solid-svg-icons/faHistory';

	interface RestoreInfo {
		palletId: string;
		productCount: number;
		pendingCount: number;
		failedCount: number;
		lastUpdated: number;
		hasExistingData: boolean;
	}

	interface Props {
		/** 자동으로 복원 확인을 수행할지 여부 */
		autoCheck?: boolean;
		/** 알림 표시 시간 (밀리초, 0이면 수동으로 닫을 때까지 표시) */
		displayDuration?: number;
		/** 알림 위치 */
		position?: 'top' | 'bottom' | 'center';
		/** 컴팩트 모드 (작은 크기로 표시) */
		compact?: boolean;
	}

	let {
		autoCheck = true,
		displayDuration = 0,
		position = 'top',
		compact = false
	}: Props = $props();

	const dispatch = createEventDispatcher<{
		restore: { restoreInfo: RestoreInfo };
		dismiss: void;
		viewDetails: { restoreInfo: RestoreInfo };
	}>();

	let showNotification = $state(false);
	let restoreInfo: RestoreInfo | null = $state(null);
	let isLoading = $state(false);
	let autoHideTimer: number | null = null;

	/**
	 * 데이터 복원 확인
	 * 로컬스토리지에서 복원 가능한 데이터가 있는지 확인합니다.
	 */
	async function checkForRestorableData(): Promise<RestoreInfo | null> {
		try {
			isLoading = true;

			// 배치 스토리지 유틸리티에서 현재 상태 확인
			const { getBatchState, getCurrentPalletId } = await import('$lib/utils/batchStorageUtils');
			const state = getBatchState();
			const currentPalletId = getCurrentPalletId();

			// 현재 팔레트에 복원 가능한 데이터가 있는지 확인
			if (currentPalletId && state.pallets[currentPalletId]) {
				const pallet = state.pallets[currentPalletId];
				const products = pallet.products;

				if (products.length > 0) {
					// 마지막 업데이트 시간 계산
					const lastUpdated = Math.max(...products.map((p) => p.timestamp));

					// 현재 시간과 마지막 업데이트 시간의 차이 확인 (5분 이상 차이나면 복원으로 간주)
					const timeDiff = Date.now() - lastUpdated;
					const isRestored = timeDiff > 5 * 60 * 1000; // 5분

					if (isRestored) {
						return {
							palletId: currentPalletId,
							productCount: products.length,
							pendingCount: products.filter((p) => p.status === 'pending').length,
							failedCount: products.filter((p) => p.status === 'failed').length,
							lastUpdated,
							hasExistingData: true
						};
					}
				}
			}

			// 다른 팔레트들도 확인
			for (const palletId in state.pallets) {
				if (palletId !== currentPalletId) {
					const pallet = state.pallets[palletId];
					const products = pallet.products;

					if (products.length > 0) {
						const pendingCount = products.filter((p) => p.status === 'pending').length;
						const failedCount = products.filter((p) => p.status === 'failed').length;

						// 대기 중이거나 실패한 상품이 있으면 복원 대상
						if (pendingCount > 0 || failedCount > 0) {
							const lastUpdated = Math.max(...products.map((p) => p.timestamp));

							return {
								palletId,
								productCount: products.length,
								pendingCount,
								failedCount,
								lastUpdated,
								hasExistingData: true
							};
						}
					}
				}
			}

			return null;
		} catch (error) {
			console.error('데이터 복원 확인 중 오류 발생:', error);
			return null;
		} finally {
			isLoading = false;
		}
	}

	/**
	 * 알림 표시
	 */
	function showRestoreNotification(info: RestoreInfo) {
		restoreInfo = info;
		showNotification = true;

		// 자동 숨김 타이머 설정
		if (displayDuration > 0) {
			if (autoHideTimer) {
				clearTimeout(autoHideTimer);
			}
			autoHideTimer = setTimeout(() => {
				hideNotification();
			}, displayDuration);
		}
	}

	/**
	 * 알림 숨김
	 */
	function hideNotification() {
		showNotification = false;
		restoreInfo = null;

		if (autoHideTimer) {
			clearTimeout(autoHideTimer);
			autoHideTimer = null;
		}

		dispatch('dismiss');
	}

	/**
	 * 복원 확인 버튼 클릭 핸들러
	 */
	function handleRestoreConfirm() {
		if (restoreInfo) {
			dispatch('restore', { restoreInfo });
			hideNotification();
		}
	}

	/**
	 * 상세 보기 버튼 클릭 핸들러
	 */
	function handleViewDetails() {
		if (restoreInfo) {
			dispatch('viewDetails', { restoreInfo });
		}
	}

	/**
	 * 시간 포맷팅
	 */
	function formatTime(timestamp: number): string {
		const date = new Date(timestamp);
		const now = new Date();
		const diffMs = now.getTime() - date.getTime();
		const diffMinutes = Math.floor(diffMs / (1000 * 60));
		const diffHours = Math.floor(diffMinutes / 60);
		const diffDays = Math.floor(diffHours / 24);

		if (diffMinutes < 1) {
			return '방금 전';
		} else if (diffMinutes < 60) {
			return `${diffMinutes}분 전`;
		} else if (diffHours < 24) {
			return `${diffHours}시간 전`;
		} else if (diffDays < 7) {
			return `${diffDays}일 전`;
		} else {
			return date.toLocaleDateString('ko-KR', {
				year: 'numeric',
				month: 'short',
				day: 'numeric',
				hour: '2-digit',
				minute: '2-digit'
			});
		}
	}

	/**
	 * 컴포넌트 마운트 시 자동 확인
	 */
	onMount(async () => {
		if (autoCheck) {
			const info = await checkForRestorableData();
			if (info) {
				showRestoreNotification(info);
			}
		}
	});

	/**
	 * 외부에서 호출 가능한 함수들
	 */
	export function checkRestore() {
		return checkForRestorableData();
	}

	export function showRestore(info: RestoreInfo) {
		showRestoreNotification(info);
	}

	export function hide() {
		hideNotification();
	}
</script>

<!-- 알림 컨테이너 -->
{#if showNotification && restoreInfo}
	<div
		class="batch-restore-notification {position} {compact ? 'compact' : ''}"
		transition:slide={{ duration: 300 }}
	>
		<div class="notification-content" transition:fade={{ duration: 200 }}>
			<!-- 아이콘 및 제목 -->
			<div class="notification-header">
				<div class="icon-container">
					<Icon data={faHistory} class="restore-icon" />
				</div>
				<div class="title-container">
					<h3 class="notification-title">
						{#if compact}
							데이터 복원됨
						{:else}
							이전 작업 데이터가 복원되었습니다
						{/if}
					</h3>
					{#if !compact}
						<p class="notification-subtitle">
							팔레트 {restoreInfo.palletId}에서 {restoreInfo.productCount}개의 상품이
							발견되었습니다.
						</p>
					{/if}
				</div>
				<button class="close-button" onclick={hideNotification} aria-label="알림 닫기">
					<Icon data={faTimes} />
				</button>
			</div>

			<!-- 상세 정보 -->
			{#if !compact}
				<div class="notification-details">
					<div class="detail-grid">
						<div class="detail-item">
							<span class="detail-label">팔레트:</span>
							<span class="detail-value">{restoreInfo.palletId}</span>
						</div>
						<div class="detail-item">
							<span class="detail-label">전체 상품:</span>
							<span class="detail-value">{restoreInfo.productCount}개</span>
						</div>
						{#if restoreInfo.pendingCount > 0}
							<div class="detail-item">
								<span class="detail-label">대기 중:</span>
								<span class="detail-value pending">{restoreInfo.pendingCount}개</span>
							</div>
						{/if}
						{#if restoreInfo.failedCount > 0}
							<div class="detail-item">
								<span class="detail-label">실패:</span>
								<span class="detail-value failed">{restoreInfo.failedCount}개</span>
							</div>
						{/if}
						<div class="detail-item">
							<span class="detail-label">마지막 수정:</span>
							<span class="detail-value">{formatTime(restoreInfo.lastUpdated)}</span>
						</div>
					</div>
				</div>
			{/if}

			<!-- 액션 버튼들 -->
			<div class="notification-actions">
				{#if !compact}
					<button class="action-button secondary" onclick={handleViewDetails}>
						<Icon data={faInfoCircle} />
						상세 보기
					</button>
				{/if}
				<button class="action-button primary" onclick={handleRestoreConfirm}>
					<Icon data={faCheckCircle} />
					{compact ? '확인' : '계속 작업하기'}
				</button>
			</div>
		</div>

		<!-- 로딩 오버레이 -->
		{#if isLoading}
			<div class="loading-overlay" transition:fade={{ duration: 150 }}>
				<div class="loading-spinner"></div>
			</div>
		{/if}
	</div>
{/if}

<style>
	.batch-restore-notification {
		position: fixed;
		left: 50%;
		transform: translateX(-50%);
		z-index: 1000;
		max-width: 500px;
		width: 90%;
		background: white;
		border-radius: 12px;
		box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
		border: 1px solid #e5e7eb;
		overflow: hidden;
	}

	.batch-restore-notification.top {
		top: 20px;
	}

	.batch-restore-notification.bottom {
		bottom: 20px;
	}

	.batch-restore-notification.center {
		top: 50%;
		transform: translate(-50%, -50%);
	}

	.batch-restore-notification.compact {
		max-width: 400px;
	}

	.notification-content {
		position: relative;
		padding: 20px;
	}

	.compact .notification-content {
		padding: 16px;
	}

	.notification-header {
		display: flex;
		align-items: flex-start;
		gap: 12px;
		margin-bottom: 16px;
	}

	.compact .notification-header {
		margin-bottom: 12px;
	}

	.icon-container {
		flex-shrink: 0;
		width: 40px;
		height: 40px;
		background: linear-gradient(135deg, #3b82f6, #1d4ed8);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.compact .icon-container {
		width: 32px;
		height: 32px;
	}

	:global(.restore-icon) {
		color: white;
		font-size: 18px;
	}

	.compact :global(.restore-icon) {
		font-size: 14px;
	}

	.title-container {
		flex: 1;
		min-width: 0;
	}

	.notification-title {
		font-size: 18px;
		font-weight: 600;
		color: #111827;
		margin: 0 0 4px 0;
		line-height: 1.3;
	}

	.compact .notification-title {
		font-size: 16px;
		margin-bottom: 0;
	}

	.notification-subtitle {
		font-size: 14px;
		color: #6b7280;
		margin: 0;
		line-height: 1.4;
	}

	.close-button {
		flex-shrink: 0;
		width: 32px;
		height: 32px;
		border: none;
		background: none;
		color: #9ca3af;
		cursor: pointer;
		border-radius: 6px;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.2s ease;
	}

	.close-button:hover {
		background: #f3f4f6;
		color: #374151;
	}

	.notification-details {
		margin-bottom: 16px;
		padding: 12px;
		background: #f9fafb;
		border-radius: 8px;
		border: 1px solid #e5e7eb;
	}

	.detail-grid {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 8px;
	}

	.detail-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: 13px;
	}

	.detail-label {
		color: #6b7280;
		font-weight: 500;
	}

	.detail-value {
		color: #111827;
		font-weight: 600;
	}

	.detail-value.pending {
		color: #d97706;
	}

	.detail-value.failed {
		color: #dc2626;
	}

	.notification-actions {
		display: flex;
		gap: 8px;
		justify-content: flex-end;
	}

	.compact .notification-actions {
		justify-content: center;
	}

	.action-button {
		display: flex;
		align-items: center;
		gap: 6px;
		padding: 8px 16px;
		border: none;
		border-radius: 6px;
		font-size: 14px;
		font-weight: 500;
		cursor: pointer;
		transition: all 0.2s ease;
		min-width: 100px;
		justify-content: center;
	}

	.compact .action-button {
		min-width: 80px;
		padding: 6px 12px;
		font-size: 13px;
	}

	.action-button.primary {
		background: #3b82f6;
		color: white;
	}

	.action-button.primary:hover {
		background: #2563eb;
		transform: translateY(-1px);
	}

	.action-button.secondary {
		background: #f3f4f6;
		color: #374151;
		border: 1px solid #d1d5db;
	}

	.action-button.secondary:hover {
		background: #e5e7eb;
		border-color: #9ca3af;
	}

	.loading-overlay {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(255, 255, 255, 0.8);
		display: flex;
		align-items: center;
		justify-content: center;
		backdrop-filter: blur(2px);
	}

	.loading-spinner {
		width: 24px;
		height: 24px;
		border: 2px solid #e5e7eb;
		border-top: 2px solid #3b82f6;
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}
		100% {
			transform: rotate(360deg);
		}
	}

	/* 반응형 디자인 */
	@media (max-width: 640px) {
		.batch-restore-notification {
			width: 95%;
			max-width: none;
		}

		.notification-content {
			padding: 16px;
		}

		.notification-title {
			font-size: 16px;
		}

		.notification-subtitle {
			font-size: 13px;
		}

		.detail-grid {
			grid-template-columns: 1fr;
		}

		.action-button {
			flex: 1;
		}
	}

	/* 다크 모드 지원 */
	@media (prefers-color-scheme: dark) {
		.batch-restore-notification {
			background: #1f2937;
			border-color: #374151;
		}

		.notification-title {
			color: #f9fafb;
		}

		.notification-subtitle {
			color: #d1d5db;
		}

		.notification-details {
			background: #111827;
			border-color: #374151;
		}

		.detail-label {
			color: #9ca3af;
		}

		.detail-value {
			color: #f3f4f6;
		}

		.close-button {
			color: #6b7280;
		}

		.close-button:hover {
			background: #374151;
			color: #d1d5db;
		}

		.action-button.secondary {
			background: #374151;
			color: #d1d5db;
			border-color: #4b5563;
		}

		.action-button.secondary:hover {
			background: #4b5563;
			border-color: #6b7280;
		}

		.loading-overlay {
			background: rgba(31, 41, 55, 0.8);
		}
	}
</style>
