<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import Icon from 'svelte-awesome';
	import { faExclamationTriangle } from '@fortawesome/free-solid-svg-icons/faExclamationTriangle';
	import { faRedo } from '@fortawesome/free-solid-svg-icons/faRedo';
	import { faTrash } from '@fortawesome/free-solid-svg-icons/faTrash';
	import { faRefresh } from '@fortawesome/free-solid-svg-icons/faRefresh';
	import { faCheckCircle } from '@fortawesome/free-solid-svg-icons/faCheckCircle';
	import { faEdit } from '@fortawesome/free-solid-svg-icons/faEdit';
	import { faSave } from '@fortawesome/free-solid-svg-icons/faSave';
	import { faTimes } from '@fortawesome/free-solid-svg-icons/faTimes';
	import { faSpinner } from '@fortawesome/free-solid-svg-icons/faSpinner';

	import type { BatchProductData } from '$lib/types/batchTypes';
	import {
		getProductsByStatus,
		retryFailedProducts,
		removeProduct,
		updateProduct,
		submitSingleProduct
	} from '$lib/services/batchProductService';

	// 컴포넌트 props 정의
	let {
		palletId = '',
		showHeader = true,
		showActions = true,
		className = '',
		autoRefresh = true,
		refreshInterval = 2000,
		onProductUpdate = () => {},
		onProductDelete = () => {},
		onRetryComplete = () => {}
	}: {
		palletId?: string;
		showHeader?: boolean;
		showActions?: boolean;
		className?: string;
		autoRefresh?: boolean;
		refreshInterval?: number;
		onProductUpdate?: (product: BatchProductData) => void;
		onProductDelete?: (productId: string) => void;
		onRetryComplete?: (result: { success: boolean; message: string }) => void;
	} = $props();

	// 상태 변수들
	let isLoading = $state(false);
	let isRetrying = $state(false);
	let refreshTimer: NodeJS.Timeout | null = null;

	// 선택된 상품들 (일괄 재시도용)
	let selectedProducts = $state<Set<string>>(new Set());

	// 편집 상태 관리
	let editingProductId = $state<string | null>(null);
	let editedProduct = $state<Partial<BatchProductData>>({});

	// 개별 상품 재시도 상태
	let retryingProducts = $state<Set<string>>(new Set());

	// 반응형 실패 상품 목록
	const failedProducts = $derived.by(() => {
		try {
			return getProductsByStatus('failed', palletId || undefined);
		} catch (error) {
			console.error('실패 상품 목록 조회 중 오류 발생:', error);
			return [];
		}
	});

	// 자동 새로고침 시작
	function startAutoRefresh() {
		if (autoRefresh && refreshInterval > 0) {
			refreshTimer = setInterval(() => {
				// $derived가 자동으로 반응형 업데이트를 처리하므로 별도 작업 불필요
			}, refreshInterval);
		}
	}

	// 자동 새로고침 중지
	function stopAutoRefresh() {
		if (refreshTimer) {
			clearInterval(refreshTimer);
			refreshTimer = null;
		}
	}

	// 수동 새로고침
	function handleRefresh() {
		isLoading = true;
		setTimeout(() => {
			isLoading = false;
		}, 300);
	}

	// 시간 포맷팅 함수
	function formatTimestamp(timestamp: number): string {
		const date = new Date(timestamp);
		const now = new Date();
		const diffMs = now.getTime() - date.getTime();
		const diffMins = Math.floor(diffMs / (1000 * 60));
		const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
		const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

		if (diffMins < 1) return '방금 전';
		if (diffMins < 60) return `${diffMins}분 전`;
		if (diffHours < 24) return `${diffHours}시간 전`;
		if (diffDays < 7) return `${diffDays}일 전`;

		return date.toLocaleDateString('ko-KR', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		});
	}

	// 상품 정보 표시 함수
	function getProductInfoDisplay(productInfo: any): string {
		if (!productInfo || typeof productInfo !== 'object') return '';

		const entries = Object.entries(productInfo)
			.filter(([key, value]) => value !== null && value !== undefined && value !== '')
			.slice(0, 3);

		return entries.map(([key, value]) => `${key}: ${value}`).join(', ');
	}

	// 체크박스 변경 처리
	function handleProductSelect(productId: string, selected: boolean) {
		if (selected) {
			selectedProducts.add(productId);
		} else {
			selectedProducts.delete(productId);
		}
		selectedProducts = new Set(selectedProducts);
	}

	// 전체 선택/해제
	function handleSelectAll() {
		if (selectAll) {
			failedProducts.forEach((product) => {
				selectedProducts.add(product.id);
			});
		} else {
			selectedProducts.clear();
		}
		selectedProducts = new Set(selectedProducts);
	}

	// 전체 선택 상태
	let selectAll = $state(false);

	// 전체 선택 상태 자동 업데이트
	$effect(() => {
		const shouldSelectAll =
			failedProducts.length > 0 && failedProducts.every((p) => selectedProducts.has(p.id));
		if (selectAll !== shouldSelectAll) {
			selectAll = shouldSelectAll;
		}
	});

	// 모든 실패 상품 재시도
	async function handleRetryAll() {
		if (failedProducts.length === 0) return;

		const confirmed = confirm(`모든 실패 상품(${failedProducts.length}개)을 재시도하시겠습니까?`);
		if (!confirmed) return;

		try {
			isRetrying = true;
			const result = await retryFailedProducts(palletId || undefined);

			onRetryComplete(result);

			if (result.success) {
				selectedProducts.clear();
				selectedProducts = new Set();
			}
		} catch (error) {
			console.error('전체 재시도 중 오류 발생:', error);
			onRetryComplete({
				success: false,
				message: '재시도 중 오류가 발생했습니다.'
			});
		} finally {
			isRetrying = false;
		}
	}

	// 선택된 상품 재시도
	async function handleRetrySelected() {
		if (selectedProducts.size === 0) return;

		const confirmed = confirm(`선택된 ${selectedProducts.size}개 상품을 재시도하시겠습니까?`);
		if (!confirmed) return;

		try {
			isRetrying = true;
			const productIds = Array.from(selectedProducts);
			const result = await retryFailedProducts(palletId || undefined, productIds);

			onRetryComplete(result);

			if (result.success) {
				selectedProducts.clear();
				selectedProducts = new Set();
			}
		} catch (error) {
			console.error('선택 상품 재시도 중 오류 발생:', error);
			onRetryComplete({
				success: false,
				message: '재시도 중 오류가 발생했습니다.'
			});
		} finally {
			isRetrying = false;
		}
	}

	// 개별 상품 재시도
	async function handleSingleRetry(product: BatchProductData) {
		try {
			retryingProducts.add(product.id);
			retryingProducts = new Set(retryingProducts);

			const result = await submitSingleProduct(product.id);

			if (result.success) {
				const updatedProduct = {
					...product,
					status: 'success' as const,
					errorMessage: undefined,
					timestamp: Date.now()
				};
				onProductUpdate(updatedProduct);
			} else {
				const updatedProduct = {
					...product,
					status: 'failed' as const,
					errorMessage: result.error,
					timestamp: Date.now()
				};
				onProductUpdate(updatedProduct);
			}
		} catch (error) {
			console.error('개별 상품 재시도 중 오류 발생:', error);
		} finally {
			retryingProducts.delete(product.id);
			retryingProducts = new Set(retryingProducts);
		}
	}

	// 선택된 상품 삭제
	async function handleDeleteSelected() {
		if (selectedProducts.size === 0) return;

		const confirmed = confirm(`선택된 ${selectedProducts.size}개 상품을 삭제하시겠습니까?`);
		if (!confirmed) return;

		try {
			for (const productId of selectedProducts) {
				await removeProduct(productId);
				onProductDelete(productId);
			}

			selectedProducts.clear();
			selectedProducts = new Set();
		} catch (error) {
			console.error('선택된 상품 삭제 중 오류 발생:', error);
			alert('상품 삭제 중 오류가 발생했습니다.');
		}
	}

	// 개별 상품 삭제
	async function handleSingleDelete(product: BatchProductData) {
		const confirmed = confirm(`QAID "${product.qaid}" 상품을 삭제하시겠습니까?`);
		if (!confirmed) return;

		try {
			const success = await removeProduct(product.id);
			if (success) {
				selectedProducts.delete(product.id);
				selectedProducts = new Set(selectedProducts);
				onProductDelete(product.id);
			} else {
				alert('상품 삭제에 실패했습니다.');
			}
		} catch (error) {
			console.error('상품 삭제 중 오류 발생:', error);
			alert('상품 삭제 중 오류가 발생했습니다.');
		}
	}

	// 편집 시작
	function startEdit(product: BatchProductData) {
		editingProductId = product.id;
		editedProduct = {
			qaid: product.qaid,
			productInfo: { ...product.productInfo }
		};
	}

	// 편집 취소
	function cancelEdit() {
		editingProductId = null;
		editedProduct = {};
	}

	// 편집 저장
	async function saveEdit(product: BatchProductData) {
		try {
			if (!editedProduct.qaid?.trim()) {
				alert('QAID를 입력해주세요.');
				return;
			}

			const success = await updateProduct(product.id, {
				qaid: editedProduct.qaid?.trim() || '',
				productInfo: editedProduct.productInfo || {}
			});

			if (success) {
				editingProductId = null;
				editedProduct = {};

				const updatedProduct = {
					...product,
					qaid: editedProduct.qaid?.trim() || '',
					productInfo: editedProduct.productInfo || {},
					timestamp: Date.now()
				};
				onProductUpdate(updatedProduct);
			} else {
				alert('상품 정보 수정에 실패했습니다.');
			}
		} catch (error) {
			console.error('상품 수정 중 오류 발생:', error);
			alert('상품 정보 수정 중 오류가 발생했습니다.');
		}
	}

	// 컴포넌트 마운트 시 초기화
	onMount(() => {
		startAutoRefresh();
	});

	// 컴포넌트 언마운트 시 정리
	onDestroy(() => {
		stopAutoRefresh();
	});
</script>

<!-- 배치 오류 목록 컴포넌트 -->
<div class="batch-error-list {className}">
	<!-- 헤더 -->
	{#if showHeader}
		<div class="flex items-center justify-between mb-4">
			<div class="flex items-center gap-2">
				<Icon data={faExclamationTriangle} class="w-5 h-5 text-error" />
				<h3 class="text-lg font-semibold text-error">
					오류 목록
					{#if palletId}
						<span class="text-sm text-base-content/70">({palletId})</span>
					{/if}
				</h3>
				<div class="badge badge-error badge-sm">
					{failedProducts.length}개
				</div>
			</div>

			<div class="flex items-center gap-2">
				<!-- 새로고침 버튼 -->
				<button
					class="btn btn-ghost btn-sm btn-circle"
					onclick={handleRefresh}
					disabled={isLoading}
					title="목록 새로고침"
				>
					<Icon data={faRefresh} class="w-4 h-4 {isLoading ? 'animate-spin' : ''}" />
				</button>
			</div>
		</div>
	{/if}

	<!-- 일괄 작업 도구 -->
	{#if showActions && failedProducts.length > 0}
		<div class="card bg-base-100 shadow-sm border border-base-300 mb-4">
			<div class="card-body p-4">
				<div class="flex items-center justify-between">
					<div class="flex items-center gap-3">
						<label class="label cursor-pointer">
							<input
								type="checkbox"
								class="checkbox checkbox-sm"
								bind:checked={selectAll}
								onchange={handleSelectAll}
							/>
							<span class="label-text ml-2">전체 선택</span>
						</label>

						{#if selectedProducts.size > 0}
							<span class="text-sm text-base-content/70">
								{selectedProducts.size}개 선택됨
							</span>
						{/if}
					</div>

					<div class="flex items-center gap-2">
						<!-- 전체 재시도 -->
						<button
							class="btn btn-warning btn-sm"
							onclick={handleRetryAll}
							disabled={isRetrying || failedProducts.length === 0}
						>
							<Icon data={faRedo} class="w-3 h-3 {isRetrying ? 'animate-spin' : ''}" />
							전체 재시도
						</button>

						<!-- 선택 재시도 -->
						{#if selectedProducts.size > 0}
							<button
								class="btn btn-warning btn-sm"
								onclick={handleRetrySelected}
								disabled={isRetrying}
							>
								<Icon data={faRedo} class="w-3 h-3 {isRetrying ? 'animate-spin' : ''}" />
								선택 재시도
							</button>
						{/if}

						<!-- 선택 삭제 -->
						{#if selectedProducts.size > 0}
							<button class="btn btn-error btn-sm" onclick={handleDeleteSelected}>
								<Icon data={faTrash} class="w-3 h-3" />
								선택 삭제
							</button>
						{/if}
					</div>
				</div>
			</div>
		</div>
	{/if}

	<!-- 오류 상품 목록 -->
	<div class="space-y-3">
		{#if failedProducts.length === 0}
			<div class="card bg-base-100 shadow-sm border border-base-300">
				<div class="card-body text-center py-8">
					<Icon data={faCheckCircle} class="w-12 h-12 text-success mx-auto mb-2" />
					<p class="text-base-content/70">실패한 상품이 없습니다</p>
					<p class="text-sm text-base-content/50">모든 상품이 성공적으로 저장되었습니다</p>
				</div>
			</div>
		{:else}
			{#each failedProducts as product (product.id)}
				<div class="card bg-base-100 shadow-sm border-2 border-error bg-error/5">
					<div class="card-body p-4">
						<div class="flex items-start gap-3">
							<!-- 체크박스 -->
							{#if showActions}
								<div class="flex-shrink-0 pt-1">
									<input
										type="checkbox"
										class="checkbox checkbox-sm"
										checked={selectedProducts.has(product.id)}
										onchange={(e) => handleProductSelect(product.id, e.target.checked)}
									/>
								</div>
							{/if}

							<!-- 상품 정보 -->
							<div class="flex-1 min-w-0">
								<!-- 상태 및 QAID -->
								<div class="flex items-center gap-3 mb-2">
									<div class="flex items-center gap-2">
										<Icon data={faExclamationTriangle} class="w-4 h-4 text-error" />
										<span class="badge badge-error badge-sm">저장 실패</span>
									</div>

									{#if editingProductId === product.id}
										<input
											type="text"
											class="input input-bordered input-sm flex-1 max-w-xs"
											bind:value={editedProduct.qaid}
											placeholder="QAID 입력"
											autofocus
										/>
									{:else}
										<div class="font-mono text-sm font-medium">
											{product.qaid}
										</div>
									{/if}
								</div>

								<!-- 상품 상세 정보 -->
								{#if product.productInfo && Object.keys(product.productInfo).length > 0}
									<div class="text-sm text-base-content/70 mb-2">
										{#if editingProductId === product.id}
											<textarea
												class="textarea textarea-bordered textarea-sm w-full"
												bind:value={editedProduct.productInfo}
												placeholder="상품 정보 (JSON 형식)"
												rows="2"
											></textarea>
										{:else}
											<div class="truncate">
												{getProductInfoDisplay(product.productInfo)}
											</div>
										{/if}
									</div>
								{/if}

								<!-- 오류 메시지 -->
								{#if product.errorMessage}
									<div class="alert alert-error alert-sm mb-2">
										<Icon data={faExclamationTriangle} class="w-4 h-4" />
										<div class="flex-1">
											<div class="font-medium text-sm">오류 상세:</div>
											<div class="text-xs opacity-90">{product.errorMessage}</div>
										</div>
									</div>
								{/if}

								<!-- 시간 정보 -->
								<div class="text-xs text-base-content/50">
									실패 시간: {formatTimestamp(product.timestamp)}
								</div>
							</div>

							<!-- 액션 버튼들 -->
							{#if showActions}
								<div class="flex-shrink-0">
									<div class="flex items-center gap-1">
										{#if editingProductId === product.id}
											<!-- 편집 모드 버튼들 -->
											<button
												class="btn btn-success btn-xs"
												onclick={() => saveEdit(product)}
												title="저장"
											>
												<Icon data={faSave} class="w-3 h-3" />
											</button>
											<button class="btn btn-ghost btn-xs" onclick={cancelEdit} title="취소">
												<Icon data={faTimes} class="w-3 h-3" />
											</button>
										{:else}
											<!-- 일반 모드 버튼들 -->
											<button
												class="btn btn-warning btn-xs"
												onclick={() => handleSingleRetry(product)}
												disabled={retryingProducts.has(product.id)}
												title="재시도"
											>
												<Icon
													data={faRedo}
													class="w-3 h-3 {retryingProducts.has(product.id) ? 'animate-spin' : ''}"
												/>
											</button>

											<button
												class="btn btn-ghost btn-xs"
												onclick={() => startEdit(product)}
												title="수정"
											>
												<Icon data={faEdit} class="w-3 h-3" />
											</button>

											<button
												class="btn btn-error btn-xs"
												onclick={() => handleSingleDelete(product)}
												title="삭제"
											>
												<Icon data={faTrash} class="w-3 h-3" />
											</button>
										{/if}
									</div>
								</div>
							{/if}
						</div>
					</div>
				</div>
			{/each}
		{/if}
	</div>

	<!-- 재시도 진행 상태 -->
	{#if isRetrying}
		<div class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
			<div class="card bg-base-100 shadow-xl">
				<div class="card-body text-center">
					<Icon data={faSpinner} class="w-8 h-8 text-primary mx-auto mb-2 animate-spin" />
					<h3 class="font-semibold">재시도 진행 중...</h3>
					<p class="text-sm text-base-content/70">잠시만 기다려주세요</p>
				</div>
			</div>
		</div>
	{/if}
</div>

<style>
	.batch-error-list {
		width: 100%;
	}

	/* 애니메이션 효과 */
	.animate-spin {
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}

	/* 상태별 배지 스타일 */
	.badge-error {
		background-color: hsl(var(--er) / 0.2);
		color: hsl(var(--er));
		border-color: hsl(var(--er) / 0.3);
	}

	/* 체크박스 스타일 */
	.checkbox:checked {
		background-color: hsl(var(--p));
		border-color: hsl(var(--p));
	}

	/* 알림 스타일 */
	.alert-error {
		background-color: hsl(var(--er) / 0.1);
		border-color: hsl(var(--er) / 0.2);
		color: hsl(var(--er));
	}

	/* 카드 호버 효과 */
	.card:hover {
		box-shadow:
			0 4px 6px -1px rgb(0 0 0 / 0.1),
			0 2px 4px -2px rgb(0 0 0 / 0.1);
		transition: box-shadow 0.2s ease-in-out;
	}

	/* 버튼 호버 효과 */
	.btn:hover {
		transform: translateY(-1px);
		transition: transform 0.1s ease-in-out;
	}

	/* 입력 필드 포커스 효과 */
	.input:focus,
	.textarea:focus {
		box-shadow: 0 0 0 2px hsl(var(--p) / 0.5);
	}

	/* 텍스트 말줄임 */
	.truncate {
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}

	/* 모달 오버레이 */
	.fixed {
		position: fixed;
	}

	.inset-0 {
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
	}

	.z-50 {
		z-index: 50;
	}

	/* 반응형 디자인 */
	@media (max-width: 640px) {
		.batch-error-list .card-body {
			padding: 0.75rem;
		}

		.batch-error-list .btn {
			height: 1.5rem;
			min-height: 1.5rem;
			padding-left: 0.5rem;
			padding-right: 0.5rem;
			font-size: 0.75rem;
		}

		.batch-error-list .badge {
			height: 1rem;
			font-size: 0.625rem;
			padding-left: 0.25rem;
			padding-right: 0.25rem;
		}

		.batch-error-list .flex {
			flex-direction: column;
			gap: 0.5rem;
		}

		.batch-error-list .flex-shrink-0 {
			flex-shrink: 1;
		}

		.batch-error-list .alert {
			padding: 0.5rem;
		}
	}

	/* 다크 모드 지원 */
	@media (prefers-color-scheme: dark) {
		.card {
			background-color: hsl(var(--b2));
		}

		.alert-error {
			background-color: hsl(var(--er) / 0.2);
			border-color: hsl(var(--er) / 0.3);
		}

		.fixed.bg-black\/50 {
			background-color: rgb(0 0 0 / 0.7);
		}
	}
</style>
