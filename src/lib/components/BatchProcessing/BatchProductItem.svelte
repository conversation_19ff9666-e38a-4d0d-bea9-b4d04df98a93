<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import Icon from 'svelte-awesome';
	import { faBox } from '@fortawesome/free-solid-svg-icons/faBox';
	import { faCheckCircle } from '@fortawesome/free-solid-svg-icons/faCheckCircle';
	import { faExclamationTriangle } from '@fortawesome/free-solid-svg-icons/faExclamationTriangle';
	import { faSpinner } from '@fortawesome/free-solid-svg-icons/faSpinner';
	import { faClock } from '@fortawesome/free-solid-svg-icons/faClock';
	import { faEdit } from '@fortawesome/free-solid-svg-icons/faEdit';
	import { faTrash } from '@fortawesome/free-solid-svg-icons/faTrash';
	import { faSave } from '@fortawesome/free-solid-svg-icons/faSave';
	import { faTimes } from '@fortawesome/free-solid-svg-icons/faTimes';
	import { faRedo } from '@fortawesome/free-solid-svg-icons/faRedo';

	import type { BatchProductData } from '$lib/types/batchTypes';
	import {
		updateProduct,
		removeProduct,
		submitSingleProduct
	} from '$lib/services/batchProductService';

	// 컴포넌트 props 정의
	let {
		product,
		selected = false,
		showCheckbox = true,
		showActions = true,
		className = '',
		onSelect = () => {},
		onUpdate = () => {},
		onDelete = () => {}
	}: {
		product: BatchProductData;
		selected?: boolean;
		showCheckbox?: boolean;
		showActions?: boolean;
		className?: string;
		onSelect?: (selected: boolean) => void;
		onUpdate?: (product: BatchProductData) => void;
		onDelete?: () => void;
	} = $props();

	// 편집 상태
	let isEditing = $state(false);
	let isSubmitting = $state(false);
	let editedProduct = $state<Partial<BatchProductData>>({});

	// 상태별 스타일 클래스
	const statusClasses = {
		pending: 'border-warning bg-warning/5',
		submitting: 'border-info bg-info/5',
		success: 'border-success bg-success/5',
		failed: 'border-error bg-error/5'
	};

	// 상태별 아이콘
	const statusIcon = {
		pending: faClock,
		submitting: faSpinner,
		success: faCheckCircle,
		failed: faExclamationTriangle
	};

	// 상태별 색상 클래스
	const statusIconColor = {
		pending: 'text-warning',
		submitting: 'text-info',
		success: 'text-success',
		failed: 'text-error'
	};

	// 상태별 텍스트
	const statusText = {
		pending: '대기 중',
		submitting: '전송 중',
		success: '저장 완료',
		failed: '저장 실패'
	};

	// 시간 포맷팅 함수
	function formatTimestamp(timestamp: number): string {
		const date = new Date(timestamp);
		const now = new Date();
		const diffMs = now.getTime() - date.getTime();
		const diffMins = Math.floor(diffMs / (1000 * 60));
		const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
		const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

		if (diffMins < 1) return '방금 전';
		if (diffMins < 60) return `${diffMins}분 전`;
		if (diffHours < 24) return `${diffHours}시간 전`;
		if (diffDays < 7) return `${diffDays}일 전`;

		return date.toLocaleDateString('ko-KR', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		});
	}

	// 체크박스 변경 처리
	function handleCheckboxChange(event: Event) {
		const target = event.target as HTMLInputElement;
		onSelect(target.checked);
	}

	// 편집 시작
	function startEdit() {
		isEditing = true;
		editedProduct = {
			qaid: product.qaid,
			productInfo: { ...product.productInfo }
		};
	}

	// 편집 취소
	function cancelEdit() {
		isEditing = false;
		editedProduct = {};
	}

	// 편집 저장
	async function saveEdit() {
		try {
			if (!editedProduct.qaid?.trim()) {
				alert('QAID를 입력해주세요.');
				return;
			}

			const success = await updateProduct(product.id, {
				qaid: editedProduct.qaid?.trim() || '',
				productInfo: editedProduct.productInfo || {}
			});

			if (success) {
				isEditing = false;
				editedProduct = {};

				// 업데이트된 상품 정보로 onUpdate 호출
				const updatedProduct = {
					...product,
					qaid: editedProduct.qaid?.trim() || '',
					productInfo: editedProduct.productInfo || {},
					timestamp: Date.now()
				};
				onUpdate(updatedProduct);
			} else {
				alert('상품 정보 수정에 실패했습니다.');
			}
		} catch (error) {
			console.error('상품 수정 중 오류 발생:', error);
			alert('상품 정보 수정 중 오류가 발생했습니다.');
		}
	}

	// 상품 삭제
	async function handleDelete() {
		const confirmed = confirm(`QAID "${product.qaid}" 상품을 삭제하시겠습니까?`);
		if (!confirmed) return;

		try {
			const success = await removeProduct(product.id);
			if (success) {
				onDelete();
			} else {
				alert('상품 삭제에 실패했습니다.');
			}
		} catch (error) {
			console.error('상품 삭제 중 오류 발생:', error);
			alert('상품 삭제 중 오류가 발생했습니다.');
		}
	}

	// 개별 상품 재시도
	async function handleRetry() {
		if (product.status !== 'failed') return;

		try {
			isSubmitting = true;
			const result = await submitSingleProduct(product.id);

			if (result.success) {
				// 성공한 상품 정보로 onUpdate 호출
				const updatedProduct = {
					...product,
					status: 'success' as const,
					errorMessage: undefined,
					timestamp: Date.now()
				};
				onUpdate(updatedProduct);
			} else {
				// 실패한 상품 정보로 onUpdate 호출
				const updatedProduct = {
					...product,
					status: 'failed' as const,
					errorMessage: result.error,
					timestamp: Date.now()
				};
				onUpdate(updatedProduct);
			}
		} catch (error) {
			console.error('상품 재시도 중 오류 발생:', error);
			alert('상품 재시도 중 오류가 발생했습니다.');
		} finally {
			isSubmitting = false;
		}
	}

	// 상품 정보 표시 함수
	function getProductInfoDisplay(productInfo: any): string {
		if (!productInfo || typeof productInfo !== 'object') return '';

		const entries = Object.entries(productInfo)
			.filter(([key, value]) => value !== null && value !== undefined && value !== '')
			.slice(0, 3); // 최대 3개 항목만 표시

		return entries.map(([key, value]) => `${key}: ${value}`).join(', ');
	}
</script>

<!-- 배치 상품 항목 컴포넌트 -->
<div class="batch-product-item {className}">
	<div class="card bg-base-100 shadow-sm border-2 {statusClasses[product.status]}">
		<div class="card-body p-4">
			<div class="flex items-start gap-3">
				<!-- 체크박스 -->
				{#if showCheckbox}
					<div class="flex-shrink-0 pt-1">
						<input
							type="checkbox"
							class="checkbox checkbox-sm"
							checked={selected}
							onchange={handleCheckboxChange}
						/>
					</div>
				{/if}

				<!-- 상품 정보 -->
				<div class="flex-1 min-w-0">
					<!-- 상태 및 QAID -->
					<div class="flex items-center gap-3 mb-2">
						<div class="flex items-center gap-2">
							<Icon
								data={statusIcon[product.status]}
								class="w-4 h-4 {statusIconColor[product.status]} {product.status === 'submitting'
									? 'animate-spin'
									: ''}"
							/>
							<span
								class="badge badge-sm {product.status === 'pending'
									? 'badge-warning'
									: product.status === 'submitting'
										? 'badge-info'
										: product.status === 'success'
											? 'badge-success'
											: 'badge-error'}"
							>
								{statusText[product.status]}
							</span>
						</div>

						{#if isEditing}
							<input
								type="text"
								class="input input-bordered input-sm flex-1 max-w-xs"
								bind:value={editedProduct.qaid}
								placeholder="QAID 입력"
								autofocus
							/>
						{:else}
							<div class="font-mono text-sm font-medium">
								{product.qaid}
							</div>
						{/if}
					</div>

					<!-- 상품 상세 정보 -->
					{#if product.productInfo && Object.keys(product.productInfo).length > 0}
						<div class="text-sm text-base-content/70 mb-2">
							{#if isEditing}
								<textarea
									class="textarea textarea-bordered textarea-sm w-full"
									bind:value={editedProduct.productInfo}
									placeholder="상품 정보 (JSON 형식)"
									rows="2"
								></textarea>
							{:else}
								<div class="truncate">
									{getProductInfoDisplay(product.productInfo)}
								</div>
							{/if}
						</div>
					{/if}

					<!-- 오류 메시지 -->
					{#if product.status === 'failed' && product.errorMessage}
						<div class="alert alert-error alert-sm mt-2">
							<Icon data={faExclamationTriangle} class="w-4 h-4" />
							<span class="text-sm">{product.errorMessage}</span>
						</div>
					{/if}

					<!-- 시간 정보 -->
					<div class="text-xs text-base-content/50 mt-2">
						{formatTimestamp(product.timestamp)}
					</div>
				</div>

				<!-- 액션 버튼들 -->
				{#if showActions}
					<div class="flex-shrink-0">
						<div class="flex items-center gap-1">
							{#if isEditing}
								<!-- 편집 모드 버튼들 -->
								<button class="btn btn-success btn-xs" onclick={saveEdit} title="저장">
									<Icon data={faSave} class="w-3 h-3" />
								</button>
								<button class="btn btn-ghost btn-xs" onclick={cancelEdit} title="취소">
									<Icon data={faTimes} class="w-3 h-3" />
								</button>
							{:else}
								<!-- 일반 모드 버튼들 -->
								{#if product.status === 'failed'}
									<button
										class="btn btn-warning btn-xs"
										onclick={handleRetry}
										disabled={isSubmitting}
										title="재시도"
									>
										<Icon data={faRedo} class="w-3 h-3 {isSubmitting ? 'animate-spin' : ''}" />
									</button>
								{/if}

								{#if product.status === 'pending' || product.status === 'failed'}
									<button class="btn btn-ghost btn-xs" onclick={startEdit} title="수정">
										<Icon data={faEdit} class="w-3 h-3" />
									</button>
								{/if}

								<button class="btn btn-error btn-xs" onclick={handleDelete} title="삭제">
									<Icon data={faTrash} class="w-3 h-3" />
								</button>
							{/if}
						</div>
					</div>
				{/if}
			</div>
		</div>
	</div>
</div>

<style>
	.batch-product-item {
		width: 100%;
	}

	/* 애니메이션 효과 */
	.animate-spin {
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}

	/* 상태별 배지 스타일 */
	.badge-warning {
		background-color: hsl(var(--wa) / 0.2);
		color: hsl(var(--wa));
		border-color: hsl(var(--wa) / 0.3);
	}

	.badge-info {
		background-color: hsl(var(--in) / 0.2);
		color: hsl(var(--in));
		border-color: hsl(var(--in) / 0.3);
	}

	.badge-success {
		background-color: hsl(var(--su) / 0.2);
		color: hsl(var(--su));
		border-color: hsl(var(--su) / 0.3);
	}

	.badge-error {
		background-color: hsl(var(--er) / 0.2);
		color: hsl(var(--er));
		border-color: hsl(var(--er) / 0.3);
	}

	/* 체크박스 스타일 */
	.checkbox:checked {
		background-color: hsl(var(--p));
		border-color: hsl(var(--p));
	}

	/* 카드 호버 효과 */
	.card:hover {
		box-shadow:
			0 4px 6px -1px rgb(0 0 0 / 0.1),
			0 2px 4px -2px rgb(0 0 0 / 0.1);
		transition: box-shadow 0.2s ease-in-out;
	}

	/* 버튼 호버 효과 */
	.btn:hover {
		transform: translateY(-1px);
		transition: transform 0.1s ease-in-out;
	}

	/* 입력 필드 포커스 효과 */
	.input:focus,
	.textarea:focus {
		box-shadow: 0 0 0 2px hsl(var(--p) / 0.5);
	}

	/* 알림 스타일 */
	.alert-error {
		background-color: hsl(var(--er) / 0.1);
		border-color: hsl(var(--er) / 0.2);
		color: hsl(var(--er));
	}

	/* 텍스트 말줄임 */
	.truncate {
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}

	/* 반응형 디자인 */
	@media (max-width: 640px) {
		.batch-product-item .card-body {
			padding: 0.75rem;
		}

		.batch-product-item .btn {
			height: 1.5rem;
			min-height: 1.5rem;
			padding-left: 0.5rem;
			padding-right: 0.5rem;
			font-size: 0.75rem;
		}

		.batch-product-item .badge {
			height: 1rem;
			font-size: 0.625rem;
			padding-left: 0.25rem;
			padding-right: 0.25rem;
		}

		.batch-product-item .flex {
			flex-direction: column;
			gap: 0.5rem;
		}

		.batch-product-item .flex-shrink-0 {
			flex-shrink: 1;
		}
	}

	/* 다크 모드 지원 */
	@media (prefers-color-scheme: dark) {
		.card {
			background-color: hsl(var(--b2));
		}

		.alert-error {
			background-color: hsl(var(--er) / 0.2);
			border-color: hsl(var(--er) / 0.3);
		}
	}
</style>
