<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import Icon from 'svelte-awesome';
	import { faList } from '@fortawesome/free-solid-svg-icons/faList';
	import { faSearch } from '@fortawesome/free-solid-svg-icons/faSearch';
	import { faFilter } from '@fortawesome/free-solid-svg-icons/faFilter';
	import { faRefresh } from '@fortawesome/free-solid-svg-icons/faRefresh';
	import { faTrash } from '@fortawesome/free-solid-svg-icons/faTrash';
	import { faCheckCircle } from '@fortawesome/free-solid-svg-icons/faCheckCircle';

	import type { BatchProductData, BatchProductFilter } from '$lib/types/batchTypes';
	import { getAllProducts, clearAllProducts } from '$lib/services/batchProductService';
	import BatchProductItem from './BatchProductItem.svelte';

	// 컴포넌트 props 정의
	let {
		palletId = '',
		showHeader = true,
		showFilters = true,
		showActions = true,
		className = '',
		autoRefresh = true,
		refreshInterval = 2000,
		itemsPerPage = 10,
		onProductUpdate = () => {},
		onProductDelete = () => {}
	}: {
		palletId?: string;
		showHeader?: boolean;
		showFilters?: boolean;
		showActions?: boolean;
		className?: string;
		autoRefresh?: boolean;
		refreshInterval?: number;
		itemsPerPage?: number;
		onProductUpdate?: (product: BatchProductData) => void;
		onProductDelete?: (productId: string) => void;
	} = $props();

	// 상태 변수들
	let isLoading = $state(false);
	let refreshTimer: NodeJS.Timeout | null = null;

	// 필터 상태
	let searchText = $state('');
	let statusFilter = $state<'all' | 'pending' | 'submitting' | 'success' | 'failed'>('all');
	let sortBy = $state<'timestamp' | 'qaid' | 'status'>('timestamp');
	let sortOrder = $state<'asc' | 'desc'>('desc');

	// 페이지네이션 상태
	let currentPage = $state(1);

	// 선택된 상품들 (일괄 작업용)
	let selectedProducts = $state<Set<string>>(new Set());

	// 페이지별 상품 목록
	const paginatedProducts = $derived.by(() => {
		const startIndex = (currentPage - 1) * itemsPerPage;
		const endIndex = startIndex + itemsPerPage;
		return filteredAndSortedProducts.slice(startIndex, endIndex);
	});

	// 자동 새로고침 시작 (더 이상 필요하지 않음 - $derived가 자동으로 반응형 업데이트)
	function startAutoRefresh() {
		// $derived가 자동으로 반응형 업데이트를 처리하므로 별도의 새로고침이 불필요
	}

	// 자동 새로고침 중지
	function stopAutoRefresh() {
		if (refreshTimer) {
			clearInterval(refreshTimer);
			refreshTimer = null;
		}
	}

	// 수동 새로고침 (시각적 피드백용)
	function handleRefresh() {
		isLoading = true;
		// $derived가 자동으로 반응형 업데이트를 처리하므로 로딩 상태만 관리
		setTimeout(() => {
			isLoading = false;
		}, 300);
	}

	// 정렬 변경 처리
	function handleSortChange(newSortBy: typeof sortBy) {
		if (sortBy === newSortBy) {
			sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
		} else {
			sortBy = newSortBy;
			sortOrder = 'desc';
		}
		// $derived가 자동으로 반응형 업데이트를 처리하므로 별도의 함수 호출 불필요
	}

	// 페이지 변경 처리
	function handlePageChange(page: number) {
		if (page >= 1 && page <= totalPages) {
			currentPage = page;
		}
	}

	// 개별 선택/해제
	function handleProductSelect(productId: string, selected: boolean) {
		if (selected) {
			selectedProducts.add(productId);
		} else {
			selectedProducts.delete(productId);
		}
		selectedProducts = new Set(selectedProducts);

		// 전체 선택 상태는 $effect에서 자동으로 업데이트됩니다
	}

	// 선택된 상품 삭제
	async function handleDeleteSelected() {
		if (selectedProducts.size === 0) return;

		const confirmed = confirm(`선택된 ${selectedProducts.size}개 상품을 삭제하시겠습니까?`);
		if (!confirmed) return;

		try {
			const { removeProduct } = await import('$lib/services/batchProductService');

			for (const productId of selectedProducts) {
				await removeProduct(productId);
				onProductDelete(productId);
			}

			selectedProducts = new Set();
			selectAll = false;
		} catch (error) {
			console.error('선택된 상품 삭제 중 오류 발생:', error);
			alert('상품 삭제 중 오류가 발생했습니다.');
		}
	}

	// 모든 상품 삭제
	async function handleClearAll() {
		if (filteredAndSortedProducts.length === 0) return;

		const confirmed = confirm(
			`모든 상품(${filteredAndSortedProducts.length}개)을 삭제하시겠습니까?`
		);
		if (!confirmed) return;

		try {
			clearAllProducts(palletId || undefined);
			selectedProducts = new Set();
			selectAll = false;
		} catch (error) {
			console.error('모든 상품 삭제 중 오류 발생:', error);
			alert('상품 삭제 중 오류가 발생했습니다.');
		}
	}

	// 전체 선택/해제 처리
	function handleSelectAll() {
		if (selectAll) {
			// 전체 선택
			paginatedProducts.forEach((product) => {
				selectedProducts.add(product.id);
			});
		} else {
			// 전체 해제
			paginatedProducts.forEach((product) => {
				selectedProducts.delete(product.id);
			});
		}
		selectedProducts = new Set(selectedProducts);
	}

	// 상품 업데이트 이벤트 처리
	function handleProductUpdate(product: BatchProductData) {
		onProductUpdate?.(product);
	}

	// 상품 삭제 이벤트 처리
	function handleProductDelete(productId: string) {
		selectedProducts.delete(productId);
		selectedProducts = new Set(selectedProducts);
		onProductDelete?.(productId);
	}

	// 컴포넌트 마운트 시 초기화
	onMount(() => {
		startAutoRefresh();
	});

	// 컴포넌트 언마운트 시 정리
	onDestroy(() => {
		stopAutoRefresh();
	});

	// 반응형 상품 목록 - 검색어나 필터 변경 시 자동 업데이트
	const filteredAndSortedProducts = $derived.by(() => {
		// 필터 적용
		const filter: BatchProductFilter = {
			palletId: palletId || undefined,
			status: statusFilter === 'all' ? undefined : statusFilter,
			searchText: searchText.trim() || undefined
		};

		let result: BatchProductData[] = [];
		try {
			result = getAllProducts(filter);
		} catch (error) {
			console.error('상품 목록 업데이트 중 오류 발생:', error);
			return [];
		}

		// 정렬 적용
		result.sort((a, b) => {
			let comparison = 0;

			switch (sortBy) {
				case 'timestamp':
					comparison = a.timestamp - b.timestamp;
					break;
				case 'qaid':
					comparison = a.qaid.localeCompare(b.qaid);
					break;
				case 'status':
					comparison = a.status.localeCompare(b.status);
					break;
			}

			return sortOrder === 'asc' ? comparison : -comparison;
		});

		return result;
	});

	// 페이지네이션 계산
	const totalPages = $derived(Math.ceil(filteredAndSortedProducts.length / itemsPerPage));

	// 현재 페이지가 총 페이지 수를 초과하지 않도록 조정
	$effect(() => {
		if (currentPage > totalPages && totalPages > 0) {
			currentPage = Math.max(1, totalPages);
		}
	});

	// 검색어나 필터가 변경되면 첫 페이지로 이동
	$effect(() => {
		// 의존성: searchText, statusFilter
		currentPage = 1;
	});

	// 전체 선택 상태
	let selectAll = $state(false);

	// 전체 선택 상태 자동 업데이트
	$effect(() => {
		const shouldSelectAll =
			paginatedProducts.length > 0 && paginatedProducts.every((p) => selectedProducts.has(p.id));
		if (selectAll !== shouldSelectAll) {
			selectAll = shouldSelectAll;
		}
	});
</script>

<!-- 배치 상품 목록 컴포넌트 -->
<div class="batch-product-list {className}">
	<!-- 헤더 -->
	{#if showHeader}
		<div class="flex items-center justify-between mb-4">
			<div class="flex items-center gap-2">
				<Icon data={faList} class="w-5 h-5 text-primary" />
				<h3 class="text-lg font-semibold">
					상품 목록
					{#if palletId}
						<span class="text-sm text-base-content/70">({palletId})</span>
					{/if}
				</h3>
				<div class="badge badge-neutral badge-sm">
					{filteredAndSortedProducts.length}개
				</div>
			</div>

			<div class="flex items-center gap-2">
				<!-- 새로고침 버튼 -->
				<button
					class="btn btn-ghost btn-sm btn-circle"
					onclick={handleRefresh}
					disabled={isLoading}
					title="목록 새로고침"
				>
					<Icon data={faRefresh} class="w-4 h-4 {isLoading ? 'animate-spin' : ''}" />
				</button>
			</div>
		</div>
	{/if}

	<!-- 필터 및 검색 -->
	{#if showFilters}
		<div class="card bg-base-100 shadow-sm border border-base-300 mb-4">
			<div class="card-body p-4">
				<div class="flex flex-col lg:flex-row gap-4">
					<!-- 검색 -->
					<div class="flex-1">
						<div class="form-control">
							<div class="input-group">
								<span class="bg-base-200">
									<Icon data={faSearch} class="w-4 h-4" />
								</span>
								<input
									type="text"
									placeholder="QAID 또는 상품 정보 검색..."
									class="input input-bordered flex-1"
									bind:value={searchText}
								/>
							</div>
						</div>
					</div>

					<!-- 상태 필터 -->
					<div class="form-control">
						<select class="select select-bordered" bind:value={statusFilter}>
							<option value="all">전체 상태</option>
							<option value="pending">대기 중</option>
							<option value="submitting">전송 중</option>
							<option value="success">성공</option>
							<option value="failed">실패</option>
						</select>
					</div>

					<!-- 정렬 -->
					<div class="form-control">
						<select class="select select-bordered" bind:value={sortBy}>
							<option value="timestamp">시간순</option>
							<option value="qaid">QAID순</option>
							<option value="status">상태순</option>
						</select>
					</div>
				</div>
			</div>
		</div>
	{/if}

	<!-- 일괄 작업 도구 -->
	{#if showActions && filteredAndSortedProducts.length > 0}
		<div class="flex items-center justify-between mb-4 p-3 bg-base-200 rounded-lg">
			<div class="flex items-center gap-3">
				<label class="label cursor-pointer">
					<input
						type="checkbox"
						class="checkbox checkbox-sm"
						bind:checked={selectAll}
						onchange={handleSelectAll}
					/>
					<span class="label-text ml-2">전체 선택</span>
				</label>

				{#if selectedProducts.size > 0}
					<span class="text-sm text-base-content/70">
						{selectedProducts.size}개 선택됨
					</span>
				{/if}
			</div>

			<div class="flex items-center gap-2">
				{#if selectedProducts.size > 0}
					<button class="btn btn-error btn-sm" onclick={handleDeleteSelected}>
						<Icon data={faTrash} class="w-3 h-3" />
						선택 삭제
					</button>
				{/if}

				<button
					class="btn btn-ghost btn-sm"
					onclick={handleClearAll}
					disabled={filteredAndSortedProducts.length === 0}
				>
					<Icon data={faTrash} class="w-3 h-3" />
					전체 삭제
				</button>
			</div>
		</div>
	{/if}

	<!-- 상품 목록 -->
	<div class="space-y-2">
		{#if paginatedProducts.length === 0}
			<div class="card bg-base-100 shadow-sm border border-base-300">
				<div class="card-body text-center py-8">
					<Icon data={faList} class="w-12 h-12 text-base-content/30 mx-auto mb-2" />
					<p class="text-base-content/70">
						{filteredAndSortedProducts.length === 0
							? '저장된 상품이 없습니다'
							: '검색 결과가 없습니다'}
					</p>
					{#if searchText || statusFilter !== 'all'}
						<button
							class="btn btn-ghost btn-sm mt-2"
							onclick={() => {
								searchText = '';
								statusFilter = 'all';
							}}
						>
							필터 초기화
						</button>
					{/if}
				</div>
			</div>
		{:else}
			{#each paginatedProducts as product (product.id)}
				<BatchProductItem
					{product}
					selected={selectedProducts.has(product.id)}
					onSelect={(selected) => handleProductSelect(product.id, selected)}
					onUpdate={handleProductUpdate}
					onDelete={() => handleProductDelete(product.id)}
					showCheckbox={showActions}
				/>
			{/each}
		{/if}
	</div>

	<!-- 페이지네이션 -->
	{#if totalPages > 1}
		<div class="flex justify-center mt-6">
			<div class="btn-group">
				<button
					class="btn btn-sm"
					class:btn-disabled={currentPage === 1}
					onclick={() => handlePageChange(currentPage - 1)}
				>
					이전
				</button>

				{#each Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
					const startPage = Math.max(1, currentPage - 2);
					return startPage + i;
				}).filter((page) => page <= totalPages) as page}
					<button
						class="btn btn-sm"
						class:btn-active={page === currentPage}
						onclick={() => handlePageChange(page)}
					>
						{page}
					</button>
				{/each}

				<button
					class="btn btn-sm"
					class:btn-disabled={currentPage === totalPages}
					onclick={() => handlePageChange(currentPage + 1)}
				>
					다음
				</button>
			</div>
		</div>

		<div class="text-center text-sm text-base-content/70 mt-2">
			{totalPages}페이지 중 {currentPage}페이지 (전체 {filteredAndSortedProducts.length}개)
		</div>
	{/if}
</div>

<style>
	.batch-product-list {
		width: 100%;
	}

	/* 애니메이션 효과 */
	.animate-spin {
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}

	/* 입력 그룹 스타일 */
	.input-group span {
		display: flex;
		align-items: center;
		justify-content: center;
		padding-left: 0.75rem;
		padding-right: 0.75rem;
	}

	/* 버튼 그룹 스타일 */
	.btn-group .btn {
		border-right-width: 0;
	}

	.btn-group .btn:last-child {
		border-right-width: 1px;
	}

	/* 체크박스 스타일 */
	.checkbox:checked {
		background-color: hsl(var(--p));
		border-color: hsl(var(--p));
	}

	/* 카드 호버 효과 */
	.card:hover {
		box-shadow:
			0 4px 6px -1px rgb(0 0 0 / 0.1),
			0 2px 4px -2px rgb(0 0 0 / 0.1);
		transition: box-shadow 0.2s ease-in-out;
	}

	/* 반응형 디자인 */
	@media (max-width: 768px) {
		.batch-product-list .card-body {
			padding: 0.75rem;
		}

		.batch-product-list .btn-group {
			flex-wrap: wrap;
		}

		.batch-product-list .input-group {
			flex-direction: column;
		}

		.batch-product-list .input-group span {
			width: 100%;
			justify-content: flex-start;
			padding-left: 1rem;
			padding-right: 1rem;
			padding-top: 0.5rem;
			padding-bottom: 0.5rem;
			border-bottom-width: 1px;
		}
	}
</style>
