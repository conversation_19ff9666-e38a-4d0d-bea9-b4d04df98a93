<!--
  BatchSaveButton.svelte
  일괄 저장 버튼 컴포넌트
  
  기능:
  - 로컬스토리지의 모든 상품을 서버로 일괄 전송
  - 저장 진행 상태 표시 (프로그레스 바)
  - 저장 완료/실패 피드백 제공
  - 요구사항: 3.2, 5.3
-->

<script lang="ts">
	import { onMount } from 'svelte';
	import { submitAllProducts, getProductCount } from '$lib/services/batchProductService';
	import type { BatchSaveResult } from '$lib/types/batchTypes';
	import Icon from 'svelte-awesome';
	import { save, spinner, check, exclamationTriangle } from 'svelte-awesome/icons';

	// Props
	let {
		palletId = undefined,
		disabled = false,
		size = 'md',
		variant = 'primary',
		onsavestart = undefined,
		onsavecomplete = undefined,
		onsaveerror = undefined
	}: {
		palletId?: string | undefined;
		disabled?: boolean;
		size?: 'sm' | 'md' | 'lg';
		variant?: 'primary' | 'secondary' | 'success' | 'warning';
		onsavestart?: ((event: { palletId?: string }) => void) | undefined;
		onsavecomplete?: ((event: { result: BatchSaveResult }) => void) | undefined;
		onsaveerror?: ((event: { error: string }) => void) | undefined;
	} = $props();

	// 상태 변수 (Svelte 5 방식)
	let isSubmitting = $state(false); // 전송 중 여부
	let progress = $state(0); // 진행률 (0-100)
	let lastResult = $state<BatchSaveResult | null>(null); // 마지막 전송 결과
	let showResult = $state(false); // 결과 표시 여부
	let resultTimeout: NodeJS.Timeout | null = null; // 결과 표시 타이머

	// 상품 수 정보
	let productCounts = $state({ total: 0, pending: 0, submitting: 0, success: 0, failed: 0 });

	// 버튼 텍스트 및 아이콘 계산 (Svelte 5 방식)
	const buttonText = $derived(() => {
		if (isSubmitting) {
			return `저장 중... (${progress}%)`;
		}

		if (showResult && lastResult) {
			if (lastResult.success) {
				return '저장 완료';
			} else {
				return '저장 실패';
			}
		}

		if (productCounts.pending === 0) {
			return '저장할 상품 없음';
		}

		return `일괄 저장 (${productCounts.pending}개)`;
	});

	const buttonIcon = $derived(() => {
		if (isSubmitting) {
			return spinner;
		}

		if (showResult && lastResult) {
			if (lastResult.success) {
				return check;
			} else {
				return exclamationTriangle;
			}
		}

		return save;
	});

	const buttonClass = $derived(() => {
		const baseClass = 'btn';
		const sizeClass = size === 'sm' ? 'btn-sm' : size === 'lg' ? 'btn-lg' : '';

		let variantClass = '';
		if (showResult && lastResult) {
			variantClass = lastResult.success ? 'btn-success' : 'btn-error';
		} else if (isSubmitting) {
			variantClass = 'btn-info';
		} else if (!canSave) {
			variantClass = 'btn-disabled';
		} else {
			switch (variant) {
				case 'primary':
					variantClass = 'btn-primary';
					break;
				case 'secondary':
					variantClass = 'btn-secondary';
					break;
				case 'success':
					variantClass = 'btn-success';
					break;
				case 'warning':
					variantClass = 'btn-warning';
					break;
			}
		}

		return [baseClass, sizeClass, variantClass].filter(Boolean).join(' ');
	});

	const canSave = $derived(() => !disabled && !isSubmitting && productCounts.pending > 0);

	/**
	 * 컴포넌트 마운트 시 초기화
	 */
	onMount(() => {
		updateProductCounts();

		// 주기적으로 상품 수 업데이트 (5초마다)
		const interval = setInterval(updateProductCounts, 5000);

		return () => {
			clearInterval(interval);
			if (resultTimeout) {
				clearTimeout(resultTimeout);
			}
		};
	});

	/**
	 * 상품 수 정보 업데이트
	 */
	function updateProductCounts() {
		productCounts = getProductCount(palletId);
	}

	/**
	 * 일괄 저장 실행
	 */
	async function handleSave() {
		if (!canSave) return;

		try {
			// 저장 시작 콜백 호출
			onsavestart?.({ palletId });

			// 상태 초기화
			isSubmitting = true;
			progress = 0;
			showResult = false;
			lastResult = null;

			// 진행률 시뮬레이션 (실제 API 진행률이 없으므로)
			const progressInterval = setInterval(() => {
				if (progress < 90) {
					progress += Math.random() * 10;
					if (progress > 90) progress = 90;
				}
			}, 200);

			// 서버 전송 실행
			const result = await submitAllProducts(palletId);

			// 진행률 완료
			clearInterval(progressInterval);
			progress = 100;

			// 잠시 대기 후 결과 표시
			setTimeout(() => {
				isSubmitting = false;
				lastResult = result;
				showResult = true;

				// 상품 수 업데이트
				updateProductCounts();

				// 저장 완료 콜백 호출
				onsavecomplete?.({ result });

				// 3초 후 결과 숨기기
				resultTimeout = setTimeout(() => {
					showResult = false;
					lastResult = null;
				}, 3000);
			}, 500);
		} catch (error) {
			console.error('일괄 저장 중 오류 발생:', error);

			isSubmitting = false;
			progress = 0;

			const errorMessage =
				error instanceof Error ? error.message : '알 수 없는 오류가 발생했습니다.';

			// 오류 콜백 호출
			onsaveerror?.({ error: errorMessage });

			// 오류 결과 표시
			lastResult = {
				success: false,
				message: errorMessage,
				successIds: [],
				failedItems: []
			};
			showResult = true;

			// 3초 후 결과 숨기기
			resultTimeout = setTimeout(() => {
				showResult = false;
				lastResult = null;
			}, 3000);
		}
	}

	/**
	 * 외부에서 상품 수 업데이트 트리거
	 */
	export function refreshCounts() {
		updateProductCounts();
	}
</script>

<!-- 일괄 저장 버튼 -->
<div class="batch-save-button-container">
	<button
		class={buttonClass()}
		disabled={!canSave}
		onclick={handleSave}
		title={canSave
			? '로컬스토리지의 모든 대기 중인 상품을 서버로 전송합니다.'
			: '저장할 상품이 없거나 이미 저장 중입니다.'}
	>
		<!-- 아이콘 -->
		<Icon data={buttonIcon()} class={isSubmitting ? 'animate-spin' : ''} />

		<!-- 버튼 텍스트 -->
		<span class="ml-2">{buttonText()}</span>
	</button>

	<!-- 진행률 표시 (저장 중일 때만) -->
	{#if isSubmitting}
		<div class="progress-container mt-2">
			<progress class="progress progress-primary w-full" value={progress} max="100"></progress>
			<div class="text-xs text-center mt-1 text-base-content/70">
				{Math.round(progress)}% 완료
			</div>
		</div>
	{/if}

	<!-- 결과 메시지 (결과 표시 중일 때만) -->
	{#if showResult && lastResult}
		<div class="result-message mt-2">
			<div class="alert {lastResult.success ? 'alert-success' : 'alert-error'} py-2">
				<Icon data={lastResult.success ? check : exclamationTriangle} />
				<div class="text-sm">
					<div class="font-medium">{lastResult.message}</div>
					{#if lastResult.successIds.length > 0}
						<div class="text-xs mt-1">성공: {lastResult.successIds.length}개</div>
					{/if}
					{#if lastResult.failedItems.length > 0}
						<div class="text-xs mt-1">실패: {lastResult.failedItems.length}개</div>
					{/if}
				</div>
			</div>
		</div>
	{/if}

	<!-- 상품 수 정보 (디버그용, 개발 환경에서만 표시) -->
	{#if import.meta.env.DEV}
		<div class="debug-info mt-2 text-xs text-base-content/50">
			전체: {productCounts.total} | 대기: {productCounts.pending} | 성공: {productCounts.success} | 실패:
			{productCounts.failed}
		</div>
	{/if}
</div>

<style>
	.batch-save-button-container {
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.progress-container {
		width: 100%;
		max-width: 20rem;
	}

	.result-message {
		width: 100%;
		max-width: 28rem;
	}

	.debug-info {
		border: 1px dashed hsl(var(--bc) / 0.2);
		border-radius: 0.5rem;
		padding: 0.25rem 0.5rem;
	}

	/* 버튼 호버 효과 */
	.btn:not(.btn-disabled):hover {
		transform: scale(1.05);
		transition: transform 0.2s;
	}

	/* 진행률 애니메이션 */
	.progress {
		transition: all 0.3s ease-out;
	}

	/* 결과 메시지 애니메이션 */
	.result-message {
		animation: slideIn 0.3s ease-out;
	}

	@keyframes slideIn {
		from {
			opacity: 0;
			transform: translateY(-10px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	/* 스피너 애니메이션 */
	:global(.animate-spin) {
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}
</style>
