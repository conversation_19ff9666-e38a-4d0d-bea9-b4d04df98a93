<!--
  BatchSaveButtonTest.svelte
  BatchSaveButton 컴포넌트 테스트 페이지
  
  개발 및 테스트 목적으로 사용됩니다.
-->

<script lang="ts">
	import BatchSaveButton from './BatchSaveButton.svelte';
	import { addProduct, getProductCount, clearAllProducts } from '$lib/services/batchProductService';
	import type { BatchSaveResult } from '$lib/types/batchTypes';

	// 테스트 상태
	let testPalletId = 'test-pallet-001';
	let productCounts = { total: 0, pending: 0, submitting: 0, success: 0, failed: 0 };
	let lastSaveResult: BatchSaveResult | null = null;
	let saveButtonRef: BatchSaveButton;

	// 초기 상품 수 로드
	updateCounts();

	/**
	 * 상품 수 업데이트
	 */
	function updateCounts() {
		productCounts = getProductCount(testPalletId);
	}

	/**
	 * 테스트 상품 추가
	 */
	async function addTestProduct() {
		const qaid = `TEST-${Date.now()}`;
		const success = await addProduct(qaid, { name: `테스트 상품 ${qaid}` }, testPalletId);

		if (success) {
			updateCounts();
			if (saveButtonRef) {
				saveButtonRef.refreshCounts();
			}
		}
	}

	/**
	 * 여러 테스트 상품 추가
	 */
	async function addMultipleTestProducts() {
		for (let i = 0; i < 5; i++) {
			await addTestProduct();
			// 잠시 대기
			await new Promise((resolve) => setTimeout(resolve, 100));
		}
	}

	/**
	 * 모든 상품 초기화
	 */
	function clearAllTestProducts() {
		clearAllProducts(testPalletId);
		updateCounts();
		if (saveButtonRef) {
			saveButtonRef.refreshCounts();
		}
	}

	/**
	 * 저장 시작 이벤트 핸들러
	 */
	function handleSaveStart(event: { palletId?: string }) {
		console.log('저장 시작:', event);
		lastSaveResult = null;
	}

	/**
	 * 저장 완료 이벤트 핸들러
	 */
	function handleSaveComplete(event: { result: BatchSaveResult }) {
		console.log('저장 완료:', event.result);
		lastSaveResult = event.result;
		updateCounts();
	}

	/**
	 * 저장 오류 이벤트 핸들러
	 */
	function handleSaveError(event: { error: string }) {
		console.error('저장 오류:', event.error);
		alert(`저장 오류: ${event.error}`);
	}
</script>

<div class="container mx-auto p-6 max-w-2xl">
	<h1 class="text-2xl font-bold mb-6">BatchSaveButton 테스트</h1>

	<!-- 테스트 컨트롤 -->
	<div class="card bg-base-100 shadow-xl mb-6">
		<div class="card-body">
			<h2 class="card-title">테스트 컨트롤</h2>

			<div class="flex flex-wrap gap-2 mb-4">
				<button class="btn btn-sm btn-outline" on:click={addTestProduct}>
					테스트 상품 1개 추가
				</button>
				<button class="btn btn-sm btn-outline" on:click={addMultipleTestProducts}>
					테스트 상품 5개 추가
				</button>
				<button class="btn btn-sm btn-error btn-outline" on:click={clearAllTestProducts}>
					모든 상품 삭제
				</button>
			</div>

			<div class="stats shadow">
				<div class="stat">
					<div class="stat-title">전체 상품</div>
					<div class="stat-value text-primary">{productCounts.total}</div>
				</div>
				<div class="stat">
					<div class="stat-title">대기 중</div>
					<div class="stat-value text-warning">{productCounts.pending}</div>
				</div>
				<div class="stat">
					<div class="stat-title">성공</div>
					<div class="stat-value text-success">{productCounts.success}</div>
				</div>
				<div class="stat">
					<div class="stat-title">실패</div>
					<div class="stat-value text-error">{productCounts.failed}</div>
				</div>
			</div>
		</div>
	</div>

	<!-- BatchSaveButton 테스트 -->
	<div class="card bg-base-100 shadow-xl mb-6">
		<div class="card-body">
			<h2 class="card-title">일괄 저장 버튼 테스트</h2>

			<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
				<!-- 기본 버튼 -->
				<div class="form-control">
					<label class="label">
						<span class="label-text">기본 버튼 (Medium, Primary)</span>
					</label>
					<BatchSaveButton
						bind:this={saveButtonRef}
						palletId={testPalletId}
						onsavestart={handleSaveStart}
						onsavecomplete={handleSaveComplete}
						onsaveerror={handleSaveError}
					/>
				</div>

				<!-- 작은 버튼 -->
				<div class="form-control">
					<label class="label">
						<span class="label-text">작은 버튼 (Small, Secondary)</span>
					</label>
					<BatchSaveButton
						palletId={testPalletId}
						size="sm"
						variant="secondary"
						onsavestart={handleSaveStart}
						onsavecomplete={handleSaveComplete}
						onsaveerror={handleSaveError}
					/>
				</div>

				<!-- 큰 버튼 -->
				<div class="form-control">
					<label class="label">
						<span class="label-text">큰 버튼 (Large, Success)</span>
					</label>
					<BatchSaveButton
						palletId={testPalletId}
						size="lg"
						variant="success"
						onsavestart={handleSaveStart}
						onsavecomplete={handleSaveComplete}
						onsaveerror={handleSaveError}
					/>
				</div>

				<!-- 비활성화된 버튼 -->
				<div class="form-control">
					<label class="label">
						<span class="label-text">비활성화된 버튼</span>
					</label>
					<BatchSaveButton
						palletId={testPalletId}
						disabled={true}
						variant="warning"
						onsavestart={handleSaveStart}
						onsavecomplete={handleSaveComplete}
						onsaveerror={handleSaveError}
					/>
				</div>
			</div>
		</div>
	</div>

	<!-- 마지막 저장 결과 -->
	{#if lastSaveResult}
		<div class="card bg-base-100 shadow-xl">
			<div class="card-body">
				<h2 class="card-title">마지막 저장 결과</h2>

				<div class="alert {lastSaveResult.success ? 'alert-success' : 'alert-error'}">
					<div>
						<div class="font-bold">{lastSaveResult.success ? '성공' : '실패'}</div>
						<div class="text-sm">{lastSaveResult.message}</div>
					</div>
				</div>

				{#if lastSaveResult.successIds.length > 0}
					<div class="mt-4">
						<h3 class="font-semibold text-success">
							성공한 상품 ({lastSaveResult.successIds.length}개)
						</h3>
						<div class="text-sm text-base-content/70">
							{lastSaveResult.successIds.join(', ')}
						</div>
					</div>
				{/if}

				{#if lastSaveResult.failedItems.length > 0}
					<div class="mt-4">
						<h3 class="font-semibold text-error">
							실패한 상품 ({lastSaveResult.failedItems.length}개)
						</h3>
						<div class="space-y-1">
							{#each lastSaveResult.failedItems as item}
								<div class="text-sm">
									<span class="font-mono">{item.qaid}</span>: {item.error}
								</div>
							{/each}
						</div>
					</div>
				{/if}
			</div>
		</div>
	{/if}
</div>
