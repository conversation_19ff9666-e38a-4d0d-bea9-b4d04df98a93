<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import Icon from 'svelte-awesome';
	import { faBox } from '@fortawesome/free-solid-svg-icons/faBox';
	import { faCheckCircle } from '@fortawesome/free-solid-svg-icons/faCheckCircle';
	import { faExclamationTriangle } from '@fortawesome/free-solid-svg-icons/faExclamationTriangle';
	import { faSpinner } from '@fortawesome/free-solid-svg-icons/faSpinner';
	import { faClock } from '@fortawesome/free-solid-svg-icons/faClock';

	import { getProductCount } from '$lib/services/batchProductService';
	import { getBatchState } from '$lib/utils/batchStorageUtils';

	// 컴포넌트 props 정의
	let {
		palletId = '',
		showDetails = true,
		className = '',
		autoRefresh = true,
		refreshInterval = 1000
	}: {
		palletId?: string;
		showDetails?: boolean;
		className?: string;
		autoRefresh?: boolean;
		refreshInterval?: number;
	} = $props();

	// 상태 변수들
	let productCounts = $state({
		total: 0,
		pending: 0,
		submitting: 0,
		success: 0,
		failed: 0
	});

	let isLoading = $state(false);
	let refreshTimer: NodeJS.Timeout | null = null;

	// 상품 수 업데이트 함수
	function updateProductCounts() {
		try {
			const counts = getProductCount(palletId || undefined);
			productCounts = counts;
		} catch (error) {
			console.error('상품 수 업데이트 중 오류 발생:', error);
		}
	}

	// 자동 새로고침 시작
	function startAutoRefresh() {
		if (autoRefresh && refreshInterval > 0) {
			refreshTimer = setInterval(() => {
				updateProductCounts();
			}, refreshInterval);
		}
	}

	// 자동 새로고침 중지
	function stopAutoRefresh() {
		if (refreshTimer) {
			clearInterval(refreshTimer);
			refreshTimer = null;
		}
	}

	// 컴포넌트 마운트 시 초기화
	onMount(() => {
		updateProductCounts();
		startAutoRefresh();
	});

	// 컴포넌트 언마운트 시 정리
	onDestroy(() => {
		stopAutoRefresh();
	});

	// 전체 상태 계산
	const overallStatus = $derived(
		(() => {
			if (productCounts.total === 0) return 'empty';
			if (productCounts.submitting > 0) return 'submitting';
			if (productCounts.failed > 0) return 'error';
			if (productCounts.pending > 0) return 'pending';
			if (productCounts.success > 0 && productCounts.pending === 0 && productCounts.failed === 0)
				return 'success';
			return 'mixed';
		})()
	);

	// 상태별 스타일 클래스
	const statusClasses = {
		empty: 'bg-base-200 text-base-content',
		pending: 'bg-warning text-warning-content',
		submitting: 'bg-info text-info-content',
		success: 'bg-success text-success-content',
		error: 'bg-error text-error-content',
		mixed: 'bg-warning text-warning-content'
	};

	// 상태별 아이콘
	const statusIcon = {
		empty: faBox,
		pending: faClock,
		submitting: faSpinner,
		success: faCheckCircle,
		error: faExclamationTriangle,
		mixed: faExclamationTriangle
	};

	// 상태별 메시지
	const statusMessage = $derived({
		empty: '저장된 상품이 없습니다',
		pending: `${productCounts.pending}개 상품이 임시 저장됨`,
		submitting: `${productCounts.submitting}개 상품 전송 중...`,
		success: `${productCounts.success}개 상품 저장 완료`,
		error: `${productCounts.failed}개 상품 저장 실패`,
		mixed: `${productCounts.pending}개 대기, ${productCounts.failed}개 실패`
	});

	// 수동 새로고침 함수
	function handleRefresh() {
		isLoading = true;
		updateProductCounts();
		setTimeout(() => {
			isLoading = false;
		}, 300);
	}
</script>

<!-- 배치 상태 표시 컴포넌트 -->
<div class="batch-status-indicator {className}">
	<!-- 메인 상태 표시 -->
	<div class="card bg-base-100 shadow-sm border border-base-300">
		<div class="card-body p-4">
			<!-- 상태 헤더 -->
			<div class="flex items-center justify-between mb-3">
				<h3 class="card-title text-base font-semibold">
					<Icon data={faBox} class="w-4 h-4" />
					배치 상태
					{#if palletId}
						<span class="text-sm text-base-content/70">({palletId})</span>
					{/if}
				</h3>

				<!-- 새로고침 버튼 -->
				<button
					class="btn btn-ghost btn-sm btn-circle"
					onclick={handleRefresh}
					disabled={isLoading}
					title="상태 새로고침"
				>
					<Icon data={faSpinner} class="w-4 h-4 {isLoading ? 'animate-spin' : ''}" />
				</button>
			</div>

			<!-- 메인 상태 표시 -->
			<div class="flex items-center gap-3 p-3 rounded-lg {statusClasses[overallStatus]}">
				<Icon
					data={statusIcon[overallStatus]}
					class="w-5 h-5 {overallStatus === 'submitting' ? 'animate-spin' : ''}"
				/>
				<div class="flex-1">
					<div class="font-medium">
						{statusMessage[overallStatus]}
					</div>
					{#if productCounts.total > 0}
						<div class="text-sm opacity-90">
							전체 {productCounts.total}개 상품
						</div>
					{/if}
				</div>
			</div>

			<!-- 상세 정보 표시 -->
			{#if showDetails && productCounts.total > 0}
				<div class="mt-3 space-y-2">
					<!-- 대기 중인 상품 -->
					{#if productCounts.pending > 0}
						<div class="flex items-center justify-between text-sm">
							<div class="flex items-center gap-2">
								<Icon data={faClock} class="w-3 h-3 text-warning" />
								<span>대기 중</span>
							</div>
							<span class="badge badge-warning badge-sm">
								{productCounts.pending}개
							</span>
						</div>
					{/if}

					<!-- 전송 중인 상품 -->
					{#if productCounts.submitting > 0}
						<div class="flex items-center justify-between text-sm">
							<div class="flex items-center gap-2">
								<Icon data={faSpinner} class="w-3 h-3 text-info animate-spin" />
								<span>전송 중</span>
							</div>
							<span class="badge badge-info badge-sm">
								{productCounts.submitting}개
							</span>
						</div>
					{/if}

					<!-- 성공한 상품 -->
					{#if productCounts.success > 0}
						<div class="flex items-center justify-between text-sm">
							<div class="flex items-center gap-2">
								<Icon data={faCheckCircle} class="w-3 h-3 text-success" />
								<span>저장 완료</span>
							</div>
							<span class="badge badge-success badge-sm">
								{productCounts.success}개
							</span>
						</div>
					{/if}

					<!-- 실패한 상품 -->
					{#if productCounts.failed > 0}
						<div class="flex items-center justify-between text-sm">
							<div class="flex items-center gap-2">
								<Icon data={faExclamationTriangle} class="w-3 h-3 text-error" />
								<span>저장 실패</span>
							</div>
							<span class="badge badge-error badge-sm">
								{productCounts.failed}개
							</span>
						</div>
					{/if}
				</div>
			{/if}

			<!-- 액션 버튼들 (상태에 따라 표시) -->
			{#if productCounts.total > 0}
				<div class="card-actions justify-end mt-3 pt-3 border-t border-base-300">
					{#if productCounts.pending > 0}
						<div class="tooltip" data-tip="임시 저장된 상품들을 서버로 전송합니다">
							<button class="btn btn-primary btn-sm">
								<Icon data={faCheckCircle} class="w-3 h-3" />
								일괄 저장
							</button>
						</div>
					{/if}

					{#if productCounts.failed > 0}
						<div class="tooltip" data-tip="실패한 상품들을 다시 전송합니다">
							<button class="btn btn-warning btn-sm">
								<Icon data={faSpinner} class="w-3 h-3" />
								재시도
							</button>
						</div>
					{/if}
				</div>
			{/if}
		</div>
	</div>
</div>

<style>
	.batch-status-indicator {
		@apply w-full;
	}

	/* 애니메이션 효과 */
	.animate-spin {
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}

	/* 상태별 배지 스타일 커스터마이징 */
	.badge-warning {
		@apply bg-warning/20 text-warning border-warning/30;
	}

	.badge-info {
		@apply bg-info/20 text-info border-info/30;
	}

	.badge-success {
		@apply bg-success/20 text-success border-success/30;
	}

	.badge-error {
		@apply bg-error/20 text-error border-error/30;
	}

	/* 툴팁 스타일 */
	.tooltip:before {
		@apply text-xs;
	}

	/* 카드 호버 효과 */
	.card:hover {
		@apply shadow-md;
		transition: box-shadow 0.2s ease-in-out;
	}

	/* 버튼 호버 효과 */
	.btn:hover {
		transform: translateY(-1px);
		transition: transform 0.1s ease-in-out;
	}

	/* 반응형 디자인 */
	@media (max-width: 640px) {
		.batch-status-indicator .card-body {
			@apply p-3;
		}

		.batch-status-indicator .card-title {
			@apply text-sm;
		}

		.batch-status-indicator .btn {
			@apply btn-xs;
		}
	}
</style>
