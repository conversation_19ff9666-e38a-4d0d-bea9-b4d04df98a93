<script lang="ts">
	import Icon from 'svelte-awesome';
	import { faExclamationTriangle } from '@fortawesome/free-solid-svg-icons/faExclamationTriangle';
	import { faInfoCircle } from '@fortawesome/free-solid-svg-icons/faInfoCircle';
	import { faHdd } from '@fortawesome/free-solid-svg-icons/faHdd';
	import { faTrash } from '@fortawesome/free-solid-svg-icons/faTrash';
	import { faUpload } from '@fortawesome/free-solid-svg-icons/faUpload';
	import { faRefresh } from '@fortawesome/free-solid-svg-icons/faRefresh';

	import { checkStorageCapacity, cleanupStorage } from '$lib/utils/batchStorageUtils';
	import { getProductCount } from '$lib/services/batchProductService';
	import type { StorageCapacityInfo } from '$lib/types/batchTypes';

	// 컴포넌트 props 정의
	let {
		className = '',
		autoRefresh = true,
		refreshInterval = 5000,
		showRecommendation = true,
		onSaveRecommended,
		onCleanupRequested
	}: {
		className?: string;
		autoRefresh?: boolean;
		refreshInterval?: number;
		showRecommendation?: boolean;
		onSaveRecommended?: () => void;
		onCleanupRequested?: () => void;
	} = $props();

	// 상태 변수들
	let storageInfo = $state<StorageCapacityInfo>({
		used: 0,
		available: 0,
		total: 0,
		percentUsed: 0,
		isNearLimit: false,
		batchUsed: 0,
		batchPercentUsed: 0
	});

	let productCounts = $state({
		total: 0,
		pending: 0,
		submitting: 0,
		success: 0,
		failed: 0
	});

	let isLoading = $state(false);
	let refreshTimer: NodeJS.Timeout | null = null;
	let showDetails = $state(false);

	// 스토리지 정보 업데이트 함수
	function updateStorageInfo() {
		try {
			storageInfo = checkStorageCapacity();
			productCounts = getProductCount();
		} catch (error) {
			console.error('스토리지 정보 업데이트 중 오류 발생:', error);
		}
	}

	// 자동 새로고침 시작
	function startAutoRefresh() {
		if (autoRefresh && refreshInterval > 0) {
			refreshTimer = setInterval(() => {
				updateStorageInfo();
			}, refreshInterval);
		}
	}

	// 자동 새로고침 중지
	function stopAutoRefresh() {
		if (refreshTimer) {
			clearInterval(refreshTimer);
			refreshTimer = null;
		}
	}

	// 수동 새로고침 함수
	function handleRefresh() {
		isLoading = true;
		updateStorageInfo();
		setTimeout(() => {
			isLoading = false;
		}, 300);
	}

	// 정리 버튼 클릭 핸들러
	function handleCleanup() {
		try {
			cleanupStorage();
			updateStorageInfo();
			onCleanupRequested?.();
		} catch (error) {
			console.error('스토리지 정리 중 오류 발생:', error);
		}
	}

	// 저장 권장 버튼 클릭 핸들러
	function handleSaveRecommendation() {
		onSaveRecommended?.();
	}

	// 컴포넌트 마운트 시 초기화
	$effect(() => {
		updateStorageInfo();
		startAutoRefresh();

		// 정리 함수 반환
		return () => {
			stopAutoRefresh();
		};
	});

	// 경고 레벨 계산
	const warningLevel = $derived(
		(() => {
			if (storageInfo.percentUsed >= 90) return 'critical';
			if (storageInfo.percentUsed >= 75) return 'warning';
			if (productCounts.pending >= 100) return 'recommendation';
			return 'normal';
		})()
	);

	// 경고 메시지
	const warningMessage = $derived({
		critical: '스토리지 용량이 부족합니다! 즉시 데이터를 저장하거나 정리해주세요.',
		warning: '스토리지 용량이 부족해지고 있습니다. 데이터 저장을 권장합니다.',
		recommendation: '많은 상품이 임시 저장되어 있습니다. 일괄 저장을 권장합니다.',
		normal: '스토리지 상태가 양호합니다.'
	});

	// 경고 레벨별 스타일
	const warningStyles = {
		critical: 'alert-error',
		warning: 'alert-warning',
		recommendation: 'alert-info',
		normal: 'alert-success'
	};

	// 경고 레벨별 아이콘
	const warningIcons = {
		critical: faExclamationTriangle,
		warning: faExclamationTriangle,
		recommendation: faInfoCircle,
		normal: faInfoCircle
	};

	// 표시 여부 결정
	const shouldShow = $derived(warningLevel !== 'normal' || showDetails);

	// 용량 바 색상
	const capacityBarColor = $derived(
		(() => {
			if (storageInfo.percentUsed >= 90) return 'bg-error';
			if (storageInfo.percentUsed >= 75) return 'bg-warning';
			if (storageInfo.percentUsed >= 50) return 'bg-info';
			return 'bg-success';
		})()
	);

	// 배치 데이터 용량 바 색상
	const batchCapacityBarColor = $derived(
		(() => {
			if ((storageInfo.batchPercentUsed || 0) >= 50) return 'bg-warning';
			if ((storageInfo.batchPercentUsed || 0) >= 25) return 'bg-info';
			return 'bg-primary';
		})()
	);
</script>

<!-- 스토리지 경고 컴포넌트 -->
{#if shouldShow}
	<div class="batch-storage-warning {className}">
		<!-- 경고 알림 -->
		{#if warningLevel !== 'normal'}
			<div class="alert {warningStyles[warningLevel]} mb-4">
				<Icon data={warningIcons[warningLevel]} class="w-5 h-5" />
				<div class="flex-1">
					<div class="font-medium">{warningMessage[warningLevel]}</div>
					{#if warningLevel === 'critical' || warningLevel === 'warning'}
						<div class="text-sm mt-1">
							현재 사용량: {storageInfo.percentUsed}% ({storageInfo.used}KB / {storageInfo.total}KB)
						</div>
					{:else if warningLevel === 'recommendation'}
						<div class="text-sm mt-1">
							임시 저장된 상품: {productCounts.pending}개
						</div>
					{/if}
				</div>

				<!-- 액션 버튼들 -->
				<div class="flex gap-2">
					{#if warningLevel === 'critical' || warningLevel === 'warning'}
						{#if productCounts.pending > 0}
							<button
								class="btn btn-sm btn-primary"
								onclick={handleSaveRecommendation}
								title="임시 저장된 상품들을 서버로 전송합니다"
							>
								<Icon data={faUpload} class="w-3 h-3" />
								일괄 저장
							</button>
						{/if}
						<button
							class="btn btn-sm btn-outline"
							onclick={handleCleanup}
							title="불필요한 데이터를 정리합니다"
						>
							<Icon data={faTrash} class="w-3 h-3" />
							정리
						</button>
					{:else if warningLevel === 'recommendation'}
						<button
							class="btn btn-sm btn-primary"
							onclick={handleSaveRecommendation}
							title="임시 저장된 상품들을 서버로 전송합니다"
						>
							<Icon data={faUpload} class="w-3 h-3" />
							일괄 저장
						</button>
					{/if}
				</div>
			</div>
		{/if}

		<!-- 상세 정보 카드 -->
		{#if showDetails || warningLevel === 'critical' || warningLevel === 'warning'}
			<div class="card bg-base-100 shadow-sm border border-base-300">
				<div class="card-body p-4">
					<!-- 헤더 -->
					<div class="flex items-center justify-between mb-3">
						<h3 class="card-title text-base font-semibold">
							<Icon data={faHdd} class="w-4 h-4" />
							스토리지 사용량
						</h3>

						<div class="flex gap-2">
							<!-- 상세 정보 토글 버튼 -->
							<button
								class="btn btn-ghost btn-sm btn-circle"
								onclick={() => (showDetails = !showDetails)}
								title={showDetails ? '간단히 보기' : '자세히 보기'}
							>
								<Icon data={faInfoCircle} class="w-4 h-4" />
							</button>

							<!-- 새로고침 버튼 -->
							<button
								class="btn btn-ghost btn-sm btn-circle"
								onclick={handleRefresh}
								disabled={isLoading}
								title="스토리지 정보 새로고침"
							>
								<Icon data={faRefresh} class="w-4 h-4 {isLoading ? 'animate-spin' : ''}" />
							</button>
						</div>
					</div>

					<!-- 전체 스토리지 사용량 -->
					<div class="space-y-3">
						<div>
							<div class="flex justify-between items-center mb-2">
								<span class="text-sm font-medium">전체 스토리지</span>
								<span class="text-sm text-base-content/70">
									{storageInfo.used}KB / {storageInfo.total}KB ({storageInfo.percentUsed}%)
								</span>
							</div>
							<div class="w-full bg-base-300 rounded-full h-2">
								<div
									class="h-2 rounded-full transition-all duration-300 {capacityBarColor}"
									style="width: {Math.min(storageInfo.percentUsed, 100)}%"
								></div>
							</div>
						</div>

						<!-- 배치 데이터 사용량 -->
						{#if showDetails && storageInfo.batchUsed !== undefined}
							<div>
								<div class="flex justify-between items-center mb-2">
									<span class="text-sm font-medium">배치 데이터</span>
									<span class="text-sm text-base-content/70">
										{storageInfo.batchUsed}KB ({storageInfo.batchPercentUsed}%)
									</span>
								</div>
								<div class="w-full bg-base-300 rounded-full h-2">
									<div
										class="h-2 rounded-full transition-all duration-300 {batchCapacityBarColor}"
										style="width: {Math.min(storageInfo.batchPercentUsed || 0, 100)}%"
									></div>
								</div>
							</div>
						{/if}

						<!-- 상품 수 정보 -->
						{#if showDetails && productCounts.total > 0}
							<div class="divider my-2"></div>
							<div class="grid grid-cols-2 gap-4 text-sm">
								<div class="stat">
									<div class="stat-title text-xs">임시 저장</div>
									<div class="stat-value text-lg text-warning">
										{productCounts.pending}
									</div>
									<div class="stat-desc">개 상품</div>
								</div>
								<div class="stat">
									<div class="stat-title text-xs">저장 완료</div>
									<div class="stat-value text-lg text-success">
										{productCounts.success}
									</div>
									<div class="stat-desc">개 상품</div>
								</div>
							</div>
						{/if}

						<!-- 권장 사항 -->
						{#if showRecommendation && (storageInfo.isNearLimit || productCounts.pending >= 50)}
							<div class="divider my-2"></div>
							<div class="bg-base-200 rounded-lg p-3">
								<h4 class="font-medium text-sm mb-2">권장 사항</h4>
								<ul class="text-sm space-y-1 text-base-content/80">
									{#if storageInfo.isNearLimit}
										<li>• 스토리지 용량이 부족합니다. 데이터를 서버로 저장해주세요.</li>
									{/if}
									{#if productCounts.pending >= 100}
										<li>• 100개 이상의 상품이 임시 저장되어 있습니다. 일괄 저장을 권장합니다.</li>
									{:else if productCounts.pending >= 50}
										<li>• 많은 상품이 임시 저장되어 있습니다. 주기적으로 저장해주세요.</li>
									{/if}
									{#if productCounts.failed > 0}
										<li>• 실패한 상품이 있습니다. 재시도를 통해 저장을 완료해주세요.</li>
									{/if}
								</ul>
							</div>
						{/if}
					</div>
				</div>
			</div>
		{/if}
	</div>
{/if}

<style>
	.batch-storage-warning {
		@apply w-full;
	}

	/* 애니메이션 효과 */
	.animate-spin {
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}

	/* 프로그레스 바 애니메이션 */
	.transition-all {
		transition: width 0.3s ease-in-out;
	}

	/* 알림 스타일 커스터마이징 */
	.alert {
		@apply rounded-lg;
	}

	.alert-error {
		@apply bg-error/10 border-error/30 text-error;
	}

	.alert-warning {
		@apply bg-warning/10 border-warning/30 text-warning;
	}

	.alert-info {
		@apply bg-info/10 border-info/30 text-info;
	}

	.alert-success {
		@apply bg-success/10 border-success/30 text-success;
	}

	/* 통계 카드 스타일 */
	.stat {
		@apply bg-base-100 rounded-lg p-3 text-center;
	}

	.stat-title {
		@apply text-base-content/60;
	}

	.stat-value {
		@apply font-bold;
	}

	.stat-desc {
		@apply text-base-content/60;
	}

	/* 버튼 호버 효과 */
	.btn:hover {
		transform: translateY(-1px);
		transition: transform 0.1s ease-in-out;
	}

	/* 카드 호버 효과 */
	.card:hover {
		@apply shadow-md;
		transition: box-shadow 0.2s ease-in-out;
	}

	/* 반응형 디자인 */
	@media (max-width: 640px) {
		.batch-storage-warning .card-body {
			@apply p-3;
		}

		.batch-storage-warning .grid-cols-2 {
			@apply grid-cols-1;
		}

		.batch-storage-warning .btn {
			@apply btn-xs;
		}

		.batch-storage-warning .alert {
			@apply text-sm;
		}
	}

	/* 다크 모드 지원 */
	@media (prefers-color-scheme: dark) {
		.bg-error {
			@apply bg-red-600;
		}

		.bg-warning {
			@apply bg-yellow-600;
		}

		.bg-info {
			@apply bg-blue-600;
		}

		.bg-success {
			@apply bg-green-600;
		}

		.bg-primary {
			@apply bg-purple-600;
		}
	}
</style>
