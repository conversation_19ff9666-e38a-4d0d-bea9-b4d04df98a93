/**
 * BatchProductList 컴포넌트 통합 테스트
 * UI 컴포넌트와 서비스 레이어 간의 통합을 테스트합니다.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/svelte';
import BatchProductList from '../BatchProductList.svelte';
import {
	addProduct,
	updateProduct,
	removeProduct,
	clearAllProducts
} from '$lib/services/batchProductService';
import { updateProductStatus } from '$lib/services/batchProductStatusService';
import { initBatchStorage } from '$lib/utils/batchStorageUtils';

// 로컬스토리지 모킹
const mockLocalStorage = (() => {
	let store: Record<string, string> = {};
	return {
		getItem: (key: string) => store[key] || null,
		setItem: (key: string, value: string) => {
			store[key] = value;
		},
		removeItem: (key: string) => {
			delete store[key];
		},
		clear: () => {
			store = {};
		},
		get length() {
			return Object.keys(store).length;
		},
		key: (index: number) => Object.keys(store)[index] || null
	};
})();

Object.defineProperty(window, 'localStorage', { value: mockLocalStorage });

// confirm 모킹
Object.defineProperty(window, 'confirm', {
	value: vi.fn(() => true)
});

describe('BatchProductList 통합 테스트', () => {
	const testPalletId = 'A-1-1-1-1';

	beforeEach(() => {
		mockLocalStorage.clear();
		initBatchStorage(true);
		vi.clearAllMocks();
		vi.mocked(window.confirm).mockReturnValue(true);
	});

	afterEach(() => {
		mockLocalStorage.clear();
		vi.restoreAllMocks();
	});

	describe('기본 렌더링 및 데이터 표시', () => {
		it('상품이 없을 때 빈 상태를 표시해야 함', async () => {
			const { component } = render(BatchProductList, {
				props: { palletId: testPalletId }
			});

			expect(screen.getByText('저장된 상품이 없습니다')).toBeInTheDocument();
			expect(screen.getByText('0개')).toBeInTheDocument();
		});

		it('상품 목록을 올바르게 표시해야 함', async () => {
			// 상품 추가
			await addProduct('QA001', { name: '테스트 상품 1' }, testPalletId);
			await addProduct('QA002', { name: '테스트 상품 2' }, testPalletId);
			await addProduct('QA003', { name: '테스트 상품 3' }, testPalletId);

			const { component } = render(BatchProductList, {
				props: { palletId: testPalletId }
			});

			// 헤더 정보 확인
			expect(screen.getByText('상품 목록')).toBeInTheDocument();
			expect(screen.getByText('3개')).toBeInTheDocument();
			expect(screen.getByText(`(${testPalletId})`)).toBeInTheDocument();

			// 상품 항목 확인
			expect(screen.getByText('QA001')).toBeInTheDocument();
			expect(screen.getByText('QA002')).toBeInTheDocument();
			expect(screen.getByText('QA003')).toBeInTheDocument();
		});

		it('다양한 상태의 상품을 올바르게 표시해야 함', async () => {
			// 상품 추가
			await addProduct('QA001', { name: '대기 상품' }, testPalletId);
			await addProduct('QA002', { name: '성공 상품' }, testPalletId);
			await addProduct('QA003', { name: '실패 상품' }, testPalletId);

			// 상태 변경
			const products = await import('$lib/services/batchProductService').then((m) =>
				m.getAllProducts({ palletId: testPalletId })
			);
			await updateProductStatus(products[1].id, 'success');
			await updateProductStatus(products[2].id, 'failed', '테스트 오류');

			const { component } = render(BatchProductList, {
				props: { palletId: testPalletId }
			});

			// 상품들이 표시되는지 확인
			expect(screen.getByText('QA001')).toBeInTheDocument();
			expect(screen.getByText('QA002')).toBeInTheDocument();
			expect(screen.getByText('QA003')).toBeInTheDocument();
		});
	});

	describe('필터링 및 검색 기능', () => {
		beforeEach(async () => {
			// 테스트용 상품 데이터 준비
			await addProduct('QA001', { name: '사과 상품' }, testPalletId);
			await addProduct('QA002', { name: '바나나 상품' }, testPalletId);
			await addProduct('QB001', { name: '오렌지 상품' }, testPalletId);
			await addProduct('QA003', { name: '포도 상품' }, testPalletId);

			// 일부 상품 상태 변경
			const products = await import('$lib/services/batchProductService').then((m) =>
				m.getAllProducts({ palletId: testPalletId })
			);
			await updateProductStatus(products[1].id, 'success');
			await updateProductStatus(products[2].id, 'failed', '테스트 오류');
		});

		it('검색어로 상품을 필터링할 수 있어야 함', async () => {
			const { component } = render(BatchProductList, {
				props: { palletId: testPalletId }
			});

			// 검색 입력
			const searchInput = screen.getByPlaceholderText('QAID 또는 상품 정보 검색...');
			await fireEvent.input(searchInput, { target: { value: 'QA' } });

			// QA로 시작하는 상품만 표시되어야 함
			expect(screen.getByText('QA001')).toBeInTheDocument();
			expect(screen.getByText('QA002')).toBeInTheDocument();
			expect(screen.getByText('QA003')).toBeInTheDocument();
			expect(screen.queryByText('QB001')).not.toBeInTheDocument();
		});

		it('상태별로 상품을 필터링할 수 있어야 함', async () => {
			const { component } = render(BatchProductList, {
				props: { palletId: testPalletId }
			});

			// 성공 상태 필터 선택
			const statusFilter = screen.getByDisplayValue('전체 상태');
			await fireEvent.change(statusFilter, { target: { value: 'success' } });

			// 성공 상태 상품만 표시되어야 함
			await waitFor(() => {
				expect(screen.getByText('QA002')).toBeInTheDocument();
				expect(screen.queryByText('QA001')).not.toBeInTheDocument();
				expect(screen.queryByText('QB001')).not.toBeInTheDocument();
				expect(screen.queryByText('QA003')).not.toBeInTheDocument();
			});
		});

		it('검색 결과가 없을 때 적절한 메시지를 표시해야 함', async () => {
			const { component } = render(BatchProductList, {
				props: { palletId: testPalletId }
			});

			// 존재하지 않는 검색어 입력
			const searchInput = screen.getByPlaceholderText('QAID 또는 상품 정보 검색...');
			await fireEvent.input(searchInput, { target: { value: 'NOTFOUND' } });

			expect(screen.getByText('검색 결과가 없습니다')).toBeInTheDocument();
			expect(screen.getByText('필터 초기화')).toBeInTheDocument();
		});

		it('필터 초기화 버튼이 동작해야 함', async () => {
			const { component } = render(BatchProductList, {
				props: { palletId: testPalletId }
			});

			// 검색어 입력 및 상태 필터 변경
			const searchInput = screen.getByPlaceholderText('QAID 또는 상품 정보 검색...');
			await fireEvent.input(searchInput, { target: { value: 'NOTFOUND' } });

			const statusFilter = screen.getByDisplayValue('전체 상태');
			await fireEvent.change(statusFilter, { target: { value: 'success' } });

			// 필터 초기화 버튼 클릭
			const resetButton = screen.getByText('필터 초기화');
			await fireEvent.click(resetButton);

			// 필터가 초기화되고 모든 상품이 표시되어야 함
			expect(searchInput).toHaveValue('');
			expect(statusFilter).toHaveValue('all');
			expect(screen.getByText('QA001')).toBeInTheDocument();
			expect(screen.getByText('QA002')).toBeInTheDocument();
		});
	});

	describe('정렬 기능', () => {
		beforeEach(async () => {
			// 시간차를 두고 상품 추가
			await addProduct('QC001', { name: '세 번째 상품' }, testPalletId);
			await new Promise((resolve) => setTimeout(resolve, 10));
			await addProduct('QA001', { name: '첫 번째 상품' }, testPalletId);
			await new Promise((resolve) => setTimeout(resolve, 10));
			await addProduct('QB001', { name: '두 번째 상품' }, testPalletId);
		});

		it('QAID순으로 정렬할 수 있어야 함', async () => {
			const { component } = render(BatchProductList, {
				props: { palletId: testPalletId }
			});

			// QAID순 정렬 선택
			const sortSelect = screen.getByDisplayValue('시간순');
			await fireEvent.change(sortSelect, { target: { value: 'qaid' } });

			// QAID 순서로 정렬되어야 함 (QA001, QB001, QC001)
			const productElements = screen.getAllByText(/^Q[ABC]001$/);
			expect(productElements[0]).toHaveTextContent('QA001');
			expect(productElements[1]).toHaveTextContent('QB001');
			expect(productElements[2]).toHaveTextContent('QC001');
		});

		it('시간순으로 정렬할 수 있어야 함', async () => {
			const { component } = render(BatchProductList, {
				props: { palletId: testPalletId }
			});

			// 기본적으로 시간순 내림차순 정렬되어야 함 (최신 순)
			const productElements = screen.getAllByText(/^Q[ABC]001$/);
			expect(productElements[0]).toHaveTextContent('QB001'); // 마지막에 추가된 상품
			expect(productElements[1]).toHaveTextContent('QA001');
			expect(productElements[2]).toHaveTextContent('QC001'); // 첫 번째로 추가된 상품
		});
	});

	describe('페이지네이션 기능', () => {
		beforeEach(async () => {
			// 페이지네이션 테스트를 위해 많은 상품 추가
			for (let i = 1; i <= 25; i++) {
				await addProduct(`QA${i.toString().padStart(3, '0')}`, { name: `상품 ${i}` }, testPalletId);
			}
		});

		it('페이지네이션이 올바르게 동작해야 함', async () => {
			const { component } = render(BatchProductList, {
				props: { palletId: testPalletId, itemsPerPage: 10 }
			});

			// 첫 페이지에 10개 상품이 표시되어야 함
			const productElements = screen.getAllByText(/^QA\d{3}$/);
			expect(productElements).toHaveLength(10);

			// 페이지네이션 버튼 확인
			expect(screen.getByText('1')).toBeInTheDocument();
			expect(screen.getByText('2')).toBeInTheDocument();
			expect(screen.getByText('3')).toBeInTheDocument();

			// 다음 페이지로 이동
			const nextButton = screen.getByText('다음');
			await fireEvent.click(nextButton);

			// 두 번째 페이지의 상품들이 표시되어야 함
			await waitFor(() => {
				const newProductElements = screen.getAllByText(/^QA\d{3}$/);
				expect(newProductElements).toHaveLength(10);
			});

			// 페이지 정보 확인
			expect(screen.getByText('3페이지 중 2페이지 (전체 25개)')).toBeInTheDocument();
		});

		it('마지막 페이지에서 남은 상품만 표시해야 함', async () => {
			const { component } = render(BatchProductList, {
				props: { palletId: testPalletId, itemsPerPage: 10 }
			});

			// 마지막 페이지로 이동
			const page3Button = screen.getByText('3');
			await fireEvent.click(page3Button);

			// 마지막 페이지에 5개 상품만 표시되어야 함
			await waitFor(() => {
				const productElements = screen.getAllByText(/^QA\d{3}$/);
				expect(productElements).toHaveLength(5);
			});
		});
	});

	describe('선택 및 일괄 작업 기능', () => {
		beforeEach(async () => {
			await addProduct('QA001', { name: '상품 1' }, testPalletId);
			await addProduct('QA002', { name: '상품 2' }, testPalletId);
			await addProduct('QA003', { name: '상품 3' }, testPalletId);
		});

		it('개별 상품을 선택할 수 있어야 함', async () => {
			const { component } = render(BatchProductList, {
				props: { palletId: testPalletId, showActions: true }
			});

			// 첫 번째 상품 선택
			const checkboxes = screen.getAllByRole('checkbox');
			const firstProductCheckbox = checkboxes[1]; // 0번째는 전체 선택 체크박스
			await fireEvent.click(firstProductCheckbox);

			// 선택 상태 확인
			expect(firstProductCheckbox).toBeChecked();
			expect(screen.getByText('1개 선택됨')).toBeInTheDocument();
		});

		it('전체 선택이 동작해야 함', async () => {
			const { component } = render(BatchProductList, {
				props: { palletId: testPalletId, showActions: true }
			});

			// 전체 선택 체크박스 클릭
			const selectAllCheckbox = screen.getByLabelText('전체 선택');
			await fireEvent.click(selectAllCheckbox);

			// 모든 상품이 선택되어야 함
			const checkboxes = screen.getAllByRole('checkbox');
			checkboxes.forEach((checkbox) => {
				expect(checkbox).toBeChecked();
			});

			expect(screen.getByText('3개 선택됨')).toBeInTheDocument();
		});

		it('선택된 상품을 삭제할 수 있어야 함', async () => {
			const { component } = render(BatchProductList, {
				props: { palletId: testPalletId, showActions: true }
			});

			// 첫 번째 상품 선택
			const checkboxes = screen.getAllByRole('checkbox');
			const firstProductCheckbox = checkboxes[1];
			await fireEvent.click(firstProductCheckbox);

			// 선택 삭제 버튼 클릭
			const deleteSelectedButton = screen.getByText('선택 삭제');
			await fireEvent.click(deleteSelectedButton);

			// 확인 대화상자가 표시되고 확인되어야 함
			expect(window.confirm).toHaveBeenCalledWith('선택된 1개 상품을 삭제하시겠습니까?');

			// 상품이 삭제되어야 함
			await waitFor(() => {
				expect(screen.getByText('2개')).toBeInTheDocument(); // 헤더의 상품 수
			});
		});

		it('전체 삭제가 동작해야 함', async () => {
			const { component } = render(BatchProductList, {
				props: { palletId: testPalletId, showActions: true }
			});

			// 전체 삭제 버튼 클릭
			const clearAllButton = screen.getByText('전체 삭제');
			await fireEvent.click(clearAllButton);

			// 확인 대화상자가 표시되어야 함
			expect(window.confirm).toHaveBeenCalledWith('모든 상품(3개)을 삭제하시겠습니까?');

			// 모든 상품이 삭제되어야 함
			await waitFor(() => {
				expect(screen.getByText('저장된 상품이 없습니다')).toBeInTheDocument();
			});
		});
	});

	describe('콜백 함수 테스트', () => {
		it('상품 업데이트 콜백이 호출되어야 함', async () => {
			const onProductUpdate = vi.fn();
			await addProduct('QA001', { name: '테스트 상품' }, testPalletId);

			const { component } = render(BatchProductList, {
				props: {
					palletId: testPalletId,
					onProductUpdate
				}
			});

			// 상품 업데이트 시뮬레이션 (실제로는 BatchProductItem에서 발생)
			// 이 테스트는 콜백이 올바르게 전달되는지 확인
			expect(component).toBeDefined();
		});

		it('상품 삭제 콜백이 호출되어야 함', async () => {
			const onProductDelete = vi.fn();
			await addProduct('QA001', { name: '테스트 상품' }, testPalletId);

			const { component } = render(BatchProductList, {
				props: {
					palletId: testPalletId,
					onProductDelete,
					showActions: true
				}
			});

			// 전체 삭제 실행
			const clearAllButton = screen.getByText('전체 삭제');
			await fireEvent.click(clearAllButton);

			// 삭제 후 콜백이 호출되었는지 확인 (실제로는 각 상품별로 호출됨)
			await waitFor(() => {
				expect(onProductDelete).toHaveBeenCalled();
			});
		});
	});

	describe('팔레트별 필터링', () => {
		it('특정 팔레트의 상품만 표시해야 함', async () => {
			const pallet1 = 'A-1-1-1-1';
			const pallet2 = 'A-1-1-1-2';

			// 각 팔레트에 상품 추가
			await addProduct('QA001', { name: '팔레트1 상품1' }, pallet1);
			await addProduct('QA002', { name: '팔레트1 상품2' }, pallet1);
			await addProduct('QA003', { name: '팔레트2 상품1' }, pallet2);

			const { component } = render(BatchProductList, {
				props: { palletId: pallet1 }
			});

			// 팔레트1의 상품만 표시되어야 함
			expect(screen.getByText('QA001')).toBeInTheDocument();
			expect(screen.getByText('QA002')).toBeInTheDocument();
			expect(screen.queryByText('QA003')).not.toBeInTheDocument();
			expect(screen.getByText('2개')).toBeInTheDocument();
		});

		it('팔레트 ID가 없을 때 모든 상품을 표시해야 함', async () => {
			await addProduct('QA001', { name: '상품1' }, 'A-1-1-1-1');
			await addProduct('QA002', { name: '상품2' }, 'A-1-1-1-2');

			const { component } = render(BatchProductList, {
				props: { palletId: '' }
			});

			// 모든 상품이 표시되어야 함
			expect(screen.getByText('QA001')).toBeInTheDocument();
			expect(screen.getByText('QA002')).toBeInTheDocument();
			expect(screen.getByText('2개')).toBeInTheDocument();
		});
	});

	describe('오류 처리', () => {
		it('서비스 오류 시 안전하게 처리해야 함', async () => {
			const originalConsoleError = console.error;
			console.error = vi.fn();

			// getAllProducts가 오류를 발생시키도록 모킹
			vi.doMock('$lib/services/batchProductService', () => ({
				getAllProducts: vi.fn().mockImplementation(() => {
					throw new Error('서비스 오류');
				}),
				clearAllProducts: vi.fn()
			}));

			const { component } = render(BatchProductList, {
				props: { palletId: testPalletId }
			});

			// 오류가 발생해도 컴포넌트가 정상적으로 렌더링되어야 함
			expect(screen.getByText('상품 목록')).toBeInTheDocument();

			console.error = originalConsoleError;
		});

		it('삭제 확인 취소 시 삭제가 실행되지 않아야 함', async () => {
			vi.mocked(window.confirm).mockReturnValueOnce(false);

			await addProduct('QA001', { name: '테스트 상품' }, testPalletId);

			const { component } = render(BatchProductList, {
				props: { palletId: testPalletId, showActions: true }
			});

			// 전체 삭제 버튼 클릭
			const clearAllButton = screen.getByText('전체 삭제');
			await fireEvent.click(clearAllButton);

			// 상품이 삭제되지 않아야 함
			expect(screen.getByText('QA001')).toBeInTheDocument();
			expect(screen.getByText('1개')).toBeInTheDocument();
		});
	});

	describe('성능 테스트', () => {
		it('대량 상품 렌더링 성능이 적절해야 함', async () => {
			// 대량 상품 추가
			for (let i = 1; i <= 1000; i++) {
				await addProduct(`QA${i.toString().padStart(4, '0')}`, { name: `상품 ${i}` }, testPalletId);
			}

			const startTime = Date.now();

			const { component } = render(BatchProductList, {
				props: { palletId: testPalletId, itemsPerPage: 50 }
			});

			// 첫 페이지 렌더링 확인
			await waitFor(() => {
				expect(screen.getByText('1000개')).toBeInTheDocument();
			});

			const endTime = Date.now();
			const renderTime = endTime - startTime;

			console.log(`1000개 상품 렌더링 시간: ${renderTime}ms`);

			// 렌더링 시간이 합리적인 범위 내에 있어야 함
			expect(renderTime).toBeLessThan(3000);

			// 페이지네이션으로 인해 실제로는 50개만 렌더링되어야 함
			const productElements = screen.getAllByText(/^QA\d{4}$/);
			expect(productElements).toHaveLength(50);
		});
	});
});
