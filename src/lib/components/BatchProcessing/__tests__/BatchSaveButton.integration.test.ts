/**
 * BatchSaveButton 컴포넌트 통합 테스트
 * UI 컴포넌트와 서비스 레이어 간의 통합을 테스트합니다.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/svelte';
import BatchSaveButton from '../BatchSaveButton.svelte';
import { addProduct, clearAllProducts } from '$lib/services/batchProductService';
import { initBatchStorage } from '$lib/utils/batchStorageUtils';

// 로컬스토리지 모킹
const mockLocalStorage = (() => {
	let store: Record<string, string> = {};
	return {
		getItem: (key: string) => store[key] || null,
		setItem: (key: string, value: string) => {
			store[key] = value;
		},
		removeItem: (key: string) => {
			delete store[key];
		},
		clear: () => {
			store = {};
		},
		get length() {
			return Object.keys(store).length;
		},
		key: (index: number) => Object.keys(store)[index] || null
	};
})();

Object.defineProperty(window, 'localStorage', { value: mockLocalStorage });

// palletApiService 모킹
const mockSaveProducts = vi.fn();
vi.mock('$lib/services/palletApiService', () => ({
	saveProducts: mockSaveProducts
}));

describe('BatchSaveButton 통합 테스트', () => {
	const testPalletId = 'A-1-1-1-1';

	beforeEach(() => {
		mockLocalStorage.clear();
		initBatchStorage(true);
		vi.clearAllMocks();
		mockSaveProducts.mockResolvedValue({
			success: true,
			message: '저장 성공',
			successIds: ['1', '2', '3'],
			failedItems: []
		});
	});

	afterEach(() => {
		mockLocalStorage.clear();
		vi.restoreAllMocks();
	});

	describe('기본 기능 테스트', () => {
		it('상품이 없을 때 버튼이 비활성화되어야 함', async () => {
			const { component } = render(BatchSaveButton, {
				props: { palletId: testPalletId }
			});

			const saveButton = screen.getByRole('button');
			expect(saveButton).toBeDisabled();
			expect(saveButton).toHaveTextContent('저장할 상품 없음');
		});

		it('상품이 있을 때 버튼이 활성화되고 올바른 텍스트를 표시해야 함', async () => {
			// 상품 추가
			await addProduct('QA001', { name: '테스트 상품 1' }, testPalletId);
			await addProduct('QA002', { name: '테스트 상품 2' }, testPalletId);

			const { component } = render(BatchSaveButton, {
				props: { palletId: testPalletId }
			});

			const saveButton = screen.getByRole('button');
			expect(saveButton).not.toBeDisabled();
			expect(saveButton).toHaveTextContent('일괄 저장 (2개)');
		});

		it('저장 버튼 클릭 시 일괄 저장이 실행되어야 함', async () => {
			// 상품 추가
			await addProduct('QA001', { name: '테스트 상품 1' }, testPalletId);
			await addProduct('QA002', { name: '테스트 상품 2' }, testPalletId);

			const { component } = render(BatchSaveButton, {
				props: { palletId: testPalletId }
			});

			const saveButton = screen.getByRole('button');
			await fireEvent.click(saveButton);

			// API 호출 확인
			expect(mockSaveProducts).toHaveBeenCalledTimes(1);
			expect(mockSaveProducts).toHaveBeenCalledWith(
				expect.arrayContaining([
					expect.objectContaining({ qaid: 'QA001' }),
					expect.objectContaining({ qaid: 'QA002' })
				])
			);
		});

		it('저장 중 상태를 올바르게 표시해야 함', async () => {
			// 지연된 Promise로 저장 중 상태 시뮬레이션
			let resolvePromise: (value: any) => void;
			const delayedPromise = new Promise((resolve) => {
				resolvePromise = resolve;
			});
			mockSaveProducts.mockReturnValueOnce(delayedPromise);

			await addProduct('QA001', { name: '테스트 상품' }, testPalletId);

			const { component } = render(BatchSaveButton, {
				props: { palletId: testPalletId }
			});

			const saveButton = screen.getByRole('button');
			await fireEvent.click(saveButton);

			// 저장 중 상태 확인
			expect(saveButton).toBeDisabled();
			expect(saveButton).toHaveTextContent(/저장 중/);

			// 진행률 표시 확인
			await waitFor(() => {
				expect(screen.getByRole('progressbar')).toBeInTheDocument();
			});

			// 저장 완료
			resolvePromise!({
				success: true,
				message: '저장 성공',
				successIds: ['1'],
				failedItems: []
			});

			// 완료 상태 확인
			await waitFor(
				() => {
					expect(saveButton).toHaveTextContent('저장 완료');
				},
				{ timeout: 2000 }
			);
		});

		it('저장 성공 시 성공 메시지를 표시해야 함', async () => {
			await addProduct('QA001', { name: '테스트 상품' }, testPalletId);

			const { component } = render(BatchSaveButton, {
				props: { palletId: testPalletId }
			});

			const saveButton = screen.getByRole('button');
			await fireEvent.click(saveButton);

			// 성공 메시지 확인
			await waitFor(
				() => {
					expect(screen.getByText('저장 성공')).toBeInTheDocument();
					expect(screen.getByText('성공: 1개')).toBeInTheDocument();
				},
				{ timeout: 2000 }
			);
		});

		it('저장 실패 시 실패 메시지를 표시해야 함', async () => {
			mockSaveProducts.mockResolvedValueOnce({
				success: false,
				message: '저장 실패',
				successIds: [],
				failedItems: [{ id: '1', qaid: 'QA001', error: '네트워크 오류' }]
			});

			await addProduct('QA001', { name: '테스트 상품' }, testPalletId);

			const { component } = render(BatchSaveButton, {
				props: { palletId: testPalletId }
			});

			const saveButton = screen.getByRole('button');
			await fireEvent.click(saveButton);

			// 실패 메시지 확인
			await waitFor(
				() => {
					expect(screen.getByText('저장 실패')).toBeInTheDocument();
					expect(screen.getByText('실패: 1개')).toBeInTheDocument();
				},
				{ timeout: 2000 }
			);
		});
	});

	describe('콜백 함수 테스트', () => {
		it('저장 시작 콜백이 호출되어야 함', async () => {
			const onSaveStart = vi.fn();
			await addProduct('QA001', { name: '테스트 상품' }, testPalletId);

			const { component } = render(BatchSaveButton, {
				props: {
					palletId: testPalletId,
					onsavestart: onSaveStart
				}
			});

			const saveButton = screen.getByRole('button');
			await fireEvent.click(saveButton);

			expect(onSaveStart).toHaveBeenCalledWith({ palletId: testPalletId });
		});

		it('저장 완료 콜백이 호출되어야 함', async () => {
			const onSaveComplete = vi.fn();
			await addProduct('QA001', { name: '테스트 상품' }, testPalletId);

			const { component } = render(BatchSaveButton, {
				props: {
					palletId: testPalletId,
					onsavecomplete: onSaveComplete
				}
			});

			const saveButton = screen.getByRole('button');
			await fireEvent.click(saveButton);

			await waitFor(
				() => {
					expect(onSaveComplete).toHaveBeenCalledWith({
						result: expect.objectContaining({
							success: true,
							message: '저장 성공'
						})
					});
				},
				{ timeout: 2000 }
			);
		});

		it('저장 오류 콜백이 호출되어야 함', async () => {
			const onSaveError = vi.fn();
			mockSaveProducts.mockRejectedValueOnce(new Error('네트워크 연결 실패'));

			await addProduct('QA001', { name: '테스트 상품' }, testPalletId);

			const { component } = render(BatchSaveButton, {
				props: {
					palletId: testPalletId,
					onsaveerror: onSaveError
				}
			});

			const saveButton = screen.getByRole('button');
			await fireEvent.click(saveButton);

			await waitFor(
				() => {
					expect(onSaveError).toHaveBeenCalledWith({
						error: '네트워크 연결 실패'
					});
				},
				{ timeout: 2000 }
			);
		});
	});

	describe('다양한 props 테스트', () => {
		it('다른 크기 옵션이 올바르게 적용되어야 함', async () => {
			await addProduct('QA001', { name: '테스트 상품' }, testPalletId);

			const { component: smallComponent } = render(BatchSaveButton, {
				props: { palletId: testPalletId, size: 'sm' }
			});

			const smallButton = screen.getByRole('button');
			expect(smallButton).toHaveClass('btn-sm');

			smallComponent.unmount();

			const { component: largeComponent } = render(BatchSaveButton, {
				props: { palletId: testPalletId, size: 'lg' }
			});

			const largeButton = screen.getByRole('button');
			expect(largeButton).toHaveClass('btn-lg');
		});

		it('다른 variant 옵션이 올바르게 적용되어야 함', async () => {
			await addProduct('QA001', { name: '테스트 상품' }, testPalletId);

			const { component } = render(BatchSaveButton, {
				props: { palletId: testPalletId, variant: 'warning' }
			});

			const button = screen.getByRole('button');
			expect(button).toHaveClass('btn-warning');
		});

		it('disabled prop이 올바르게 동작해야 함', async () => {
			await addProduct('QA001', { name: '테스트 상품' }, testPalletId);

			const { component } = render(BatchSaveButton, {
				props: { palletId: testPalletId, disabled: true }
			});

			const button = screen.getByRole('button');
			expect(button).toBeDisabled();
		});
	});

	describe('팔레트별 처리 테스트', () => {
		it('특정 팔레트의 상품만 저장해야 함', async () => {
			const pallet1 = 'A-1-1-1-1';
			const pallet2 = 'A-1-1-1-2';

			// 각 팔레트에 상품 추가
			await addProduct('QA001', { name: '팔레트1 상품' }, pallet1);
			await addProduct('QA002', { name: '팔레트2 상품' }, pallet2);

			const { component } = render(BatchSaveButton, {
				props: { palletId: pallet1 }
			});

			const saveButton = screen.getByRole('button');
			expect(saveButton).toHaveTextContent('일괄 저장 (1개)');

			await fireEvent.click(saveButton);

			// 팔레트1의 상품만 전송되었는지 확인
			expect(mockSaveProducts).toHaveBeenCalledWith(
				expect.arrayContaining([expect.objectContaining({ qaid: 'QA001', palletId: pallet1 })])
			);

			// 팔레트2의 상품은 포함되지 않았는지 확인
			const calledProducts = mockSaveProducts.mock.calls[0][0];
			expect(calledProducts.find((p: any) => p.qaid === 'QA002')).toBeUndefined();
		});

		it('팔레트 ID가 없을 때 전체 상품을 저장해야 함', async () => {
			await addProduct('QA001', { name: '상품1' }, 'A-1-1-1-1');
			await addProduct('QA002', { name: '상품2' }, 'A-1-1-1-2');

			const { component } = render(BatchSaveButton, {
				props: { palletId: undefined }
			});

			const saveButton = screen.getByRole('button');
			expect(saveButton).toHaveTextContent('일괄 저장 (2개)');

			await fireEvent.click(saveButton);

			// 모든 상품이 전송되었는지 확인
			expect(mockSaveProducts).toHaveBeenCalledWith(
				expect.arrayContaining([
					expect.objectContaining({ qaid: 'QA001' }),
					expect.objectContaining({ qaid: 'QA002' })
				])
			);
		});
	});

	describe('오류 처리 테스트', () => {
		it('네트워크 오류 시 적절한 오류 메시지를 표시해야 함', async () => {
			mockSaveProducts.mockRejectedValueOnce(new Error('네트워크 연결 실패'));

			await addProduct('QA001', { name: '테스트 상품' }, testPalletId);

			const { component } = render(BatchSaveButton, {
				props: { palletId: testPalletId }
			});

			const saveButton = screen.getByRole('button');
			await fireEvent.click(saveButton);

			// 오류 상태 확인
			await waitFor(
				() => {
					expect(saveButton).toHaveTextContent('저장 실패');
					expect(screen.getByText('네트워크 연결 실패')).toBeInTheDocument();
				},
				{ timeout: 2000 }
			);
		});

		it('서버 오류 응답 시 적절히 처리해야 함', async () => {
			mockSaveProducts.mockResolvedValueOnce({
				success: false,
				message: '서버 내부 오류',
				successIds: [],
				failedItems: [{ id: '1', qaid: 'QA001', error: '서버 오류' }]
			});

			await addProduct('QA001', { name: '테스트 상품' }, testPalletId);

			const { component } = render(BatchSaveButton, {
				props: { palletId: testPalletId }
			});

			const saveButton = screen.getByRole('button');
			await fireEvent.click(saveButton);

			await waitFor(
				() => {
					expect(screen.getByText('서버 내부 오류')).toBeInTheDocument();
					expect(screen.getByText('실패: 1개')).toBeInTheDocument();
				},
				{ timeout: 2000 }
			);
		});

		it('부분 성공 시 성공과 실패를 모두 표시해야 함', async () => {
			mockSaveProducts.mockResolvedValueOnce({
				success: false,
				message: '2개 성공, 1개 실패',
				successIds: ['1', '2'],
				failedItems: [{ id: '3', qaid: 'QA003', error: '검증 실패' }]
			});

			await addProduct('QA001', { name: '상품1' }, testPalletId);
			await addProduct('QA002', { name: '상품2' }, testPalletId);
			await addProduct('QA003', { name: '상품3' }, testPalletId);

			const { component } = render(BatchSaveButton, {
				props: { palletId: testPalletId }
			});

			const saveButton = screen.getByRole('button');
			await fireEvent.click(saveButton);

			await waitFor(
				() => {
					expect(screen.getByText('2개 성공, 1개 실패')).toBeInTheDocument();
					expect(screen.getByText('성공: 2개')).toBeInTheDocument();
					expect(screen.getByText('실패: 1개')).toBeInTheDocument();
				},
				{ timeout: 2000 }
			);
		});
	});

	describe('성능 테스트', () => {
		it('대량 상품 저장 시 적절한 성능을 보여야 함', async () => {
			// 대량 상품 추가
			for (let i = 1; i <= 100; i++) {
				await addProduct(`QA${i.toString().padStart(3, '0')}`, { name: `상품${i}` }, testPalletId);
			}

			const { component } = render(BatchSaveButton, {
				props: { palletId: testPalletId }
			});

			const saveButton = screen.getByRole('button');
			expect(saveButton).toHaveTextContent('일괄 저장 (100개)');

			const startTime = Date.now();
			await fireEvent.click(saveButton);

			await waitFor(
				() => {
					expect(screen.getByText('저장 완료')).toBeInTheDocument();
				},
				{ timeout: 5000 }
			);

			const endTime = Date.now();
			const processingTime = endTime - startTime;

			console.log(`100개 상품 저장 처리 시간: ${processingTime}ms`);

			// API가 한 번만 호출되었는지 확인
			expect(mockSaveProducts).toHaveBeenCalledTimes(1);
			expect(mockSaveProducts).toHaveBeenCalledWith(
				expect.arrayContaining(
					Array.from({ length: 100 }, (_, i) =>
						expect.objectContaining({ qaid: `QA${(i + 1).toString().padStart(3, '0')}` })
					)
				)
			);
		});

		it('연속 클릭 시 중복 실행을 방지해야 함', async () => {
			await addProduct('QA001', { name: '테스트 상품' }, testPalletId);

			const { component } = render(BatchSaveButton, {
				props: { palletId: testPalletId }
			});

			const saveButton = screen.getByRole('button');

			// 빠른 연속 클릭
			await fireEvent.click(saveButton);
			await fireEvent.click(saveButton);
			await fireEvent.click(saveButton);

			// API가 한 번만 호출되었는지 확인
			await waitFor(
				() => {
					expect(mockSaveProducts).toHaveBeenCalledTimes(1);
				},
				{ timeout: 2000 }
			);
		});
	});

	describe('접근성 테스트', () => {
		it('적절한 ARIA 속성을 가져야 함', async () => {
			await addProduct('QA001', { name: '테스트 상품' }, testPalletId);

			const { component } = render(BatchSaveButton, {
				props: { palletId: testPalletId }
			});

			const saveButton = screen.getByRole('button');
			expect(saveButton).toHaveAttribute('title');
		});

		it('키보드 네비게이션이 가능해야 함', async () => {
			await addProduct('QA001', { name: '테스트 상품' }, testPalletId);

			const { component } = render(BatchSaveButton, {
				props: { palletId: testPalletId }
			});

			const saveButton = screen.getByRole('button');

			// 포커스 가능 확인
			saveButton.focus();
			expect(document.activeElement).toBe(saveButton);

			// Enter 키로 실행 가능 확인
			await fireEvent.keyDown(saveButton, { key: 'Enter' });
			expect(mockSaveProducts).toHaveBeenCalled();
		});
	});
});
