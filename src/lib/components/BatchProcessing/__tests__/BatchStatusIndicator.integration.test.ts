/**
 * BatchStatusIndicator 컴포넌트 통합 테스트
 * UI 컴포넌트와 서비스 레이어 간의 통합을 테스트합니다.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/svelte';
import BatchStatusIndicator from '../BatchStatusIndicator.svelte';
import { addProduct, clearAllProducts, submitAllProducts } from '$lib/services/batchProductService';
import { initBatchStorage } from '$lib/utils/batchStorageUtils';

// 로컬스토리지 모킹
const mockLocalStorage = (() => {
	let store: Record<string, string> = {};
	return {
		getItem: (key: string) => store[key] || null,
		setItem: (key: string, value: string) => {
			store[key] = value;
		},
		removeItem: (key: string) => {
			delete store[key];
		},
		clear: () => {
			store = {};
		},
		get length() {
			return Object.keys(store).length;
		},
		key: (index: number) => Object.keys(store)[index] || null
	};
})();

Object.defineProperty(window, 'localStorage', { value: mockLocalStorage });

// palletApiService 모킹
vi.mock('$lib/services/palletApiService', () => ({
	saveProducts: vi.fn().mockResolvedValue({
		success: true,
		message: '저장 성공',
		successIds: ['1', '2', '3'],
		failedItems: []
	})
}));

describe('BatchStatusIndicator 통합 테스트', () => {
	const testPalletId = 'A-1-1-1-1';

	beforeEach(() => {
		mockLocalStorage.clear();
		initBatchStorage(true);
		vi.clearAllMocks();
	});

	afterEach(() => {
		mockLocalStorage.clear();
		vi.restoreAllMocks();
	});

	describe('서비스와의 통합', () => {
		it('상품 추가 시 상태가 실시간으로 업데이트되어야 함', async () => {
			const { component } = render(BatchStatusIndicator, {
				props: { palletId: testPalletId, autoRefresh: true, refreshInterval: 100 }
			});

			// 초기 상태 확인
			expect(screen.getByText('저장된 상품이 없습니다')).toBeInTheDocument();

			// 상품 추가
			await addProduct('QA001', { name: '테스트 상품 1' }, testPalletId);

			// 상태 업데이트 대기
			await waitFor(
				() => {
					expect(screen.getByText(/1개 상품이 임시 저장됨/)).toBeInTheDocument();
				},
				{ timeout: 1000 }
			);

			// 추가 상품 추가
			await addProduct('QA002', { name: '테스트 상품 2' }, testPalletId);
			await addProduct('QA003', { name: '테스트 상품 3' }, testPalletId);

			// 상태 업데이트 대기
			await waitFor(
				() => {
					expect(screen.getByText(/3개 상품이 임시 저장됨/)).toBeInTheDocument();
					expect(screen.getByText('전체 3개 상품')).toBeInTheDocument();
				},
				{ timeout: 1000 }
			);

			// 상세 정보 확인
			expect(screen.getByText('대기 중')).toBeInTheDocument();
			expect(screen.getByText('3개')).toBeInTheDocument();
		});

		it('일괄 저장 후 상태가 성공으로 변경되어야 함', async () => {
			// 상품 추가
			await addProduct('QA001', { name: '테스트 상품 1' }, testPalletId);
			await addProduct('QA002', { name: '테스트 상품 2' }, testPalletId);

			const { component } = render(BatchStatusIndicator, {
				props: { palletId: testPalletId, autoRefresh: true, refreshInterval: 100 }
			});

			// 대기 상태 확인
			await waitFor(() => {
				expect(screen.getByText(/2개 상품이 임시 저장됨/)).toBeInTheDocument();
			});

			// 일괄 저장 실행
			await submitAllProducts(testPalletId);

			// 성공 상태로 변경 확인
			await waitFor(
				() => {
					expect(screen.getByText(/2개 상품 저장 완료/)).toBeInTheDocument();
					expect(screen.getByText('저장 완료')).toBeInTheDocument();
				},
				{ timeout: 1000 }
			);
		});

		it('새로고침 버튼 클릭 시 상태가 즉시 업데이트되어야 함', async () => {
			const { component } = render(BatchStatusIndicator, {
				props: { palletId: testPalletId, autoRefresh: false }
			});

			// 상품 추가 (자동 새로고침 비활성화 상태)
			await addProduct('QA001', { name: '테스트 상품' }, testPalletId);

			// 새로고침 버튼 클릭
			const refreshButton = screen.getByTitle('상태 새로고침');
			await fireEvent.click(refreshButton);

			// 상태 업데이트 확인
			await waitFor(() => {
				expect(screen.getByText(/1개 상품이 임시 저장됨/)).toBeInTheDocument();
			});
		});

		it('팔레트 ID 변경 시 해당 팔레트의 상태를 표시해야 함', async () => {
			const pallet1 = 'A-1-1-1-1';
			const pallet2 = 'A-1-1-1-2';

			// 각 팔레트에 상품 추가
			await addProduct('QA001', { name: '팔레트1 상품' }, pallet1);
			await addProduct('QA002', { name: '팔레트1 상품2' }, pallet1);
			await addProduct('QA003', { name: '팔레트2 상품' }, pallet2);

			const { component } = render(BatchStatusIndicator, {
				props: { palletId: pallet1, autoRefresh: true, refreshInterval: 100 }
			});

			// 팔레트1 상태 확인
			await waitFor(() => {
				expect(screen.getByText(/2개 상품이 임시 저장됨/)).toBeInTheDocument();
				expect(screen.getByText(`(${pallet1})`)).toBeInTheDocument();
			});

			// 팔레트 ID 변경
			component.$set({ palletId: pallet2 });

			// 팔레트2 상태 확인
			await waitFor(() => {
				expect(screen.getByText(/1개 상품이 임시 저장됨/)).toBeInTheDocument();
				expect(screen.getByText(`(${pallet2})`)).toBeInTheDocument();
			});
		});
	});

	describe('오류 상황 처리', () => {
		it('서비스 오류 시 안전하게 처리해야 함', async () => {
			// getProductCount 함수가 오류를 발생시키도록 모킹
			const originalConsoleError = console.error;
			console.error = vi.fn();

			vi.doMock('$lib/services/batchProductService', () => ({
				getProductCount: vi.fn().mockImplementation(() => {
					throw new Error('서비스 오류');
				})
			}));

			const { component } = render(BatchStatusIndicator, {
				props: { palletId: testPalletId }
			});

			// 오류가 발생해도 컴포넌트가 정상적으로 렌더링되어야 함
			expect(screen.getByText('배치 상태')).toBeInTheDocument();

			// 콘솔 에러가 기록되어야 함
			expect(console.error).toHaveBeenCalledWith(
				'상품 수 업데이트 중 오류 발생:',
				expect.any(Error)
			);

			console.error = originalConsoleError;
		});

		it('로컬스토리지 접근 오류 시 기본값을 표시해야 함', async () => {
			// localStorage.getItem이 오류를 발생시키도록 모킹
			const originalGetItem = mockLocalStorage.getItem;
			mockLocalStorage.getItem = vi.fn().mockImplementation(() => {
				throw new Error('Storage access denied');
			});

			const { component } = render(BatchStatusIndicator, {
				props: { palletId: testPalletId }
			});

			// 기본 상태가 표시되어야 함
			expect(screen.getByText('저장된 상품이 없습니다')).toBeInTheDocument();

			mockLocalStorage.getItem = originalGetItem;
		});
	});

	describe('성능 테스트', () => {
		it('대량 상품 처리 시 성능이 적절해야 함', async () => {
			const startTime = Date.now();

			// 대량 상품 추가
			for (let i = 1; i <= 100; i++) {
				await addProduct(`QA${i.toString().padStart(3, '0')}`, { name: `상품${i}` }, testPalletId);
			}

			const { component } = render(BatchStatusIndicator, {
				props: { palletId: testPalletId, autoRefresh: true, refreshInterval: 100 }
			});

			// 상태 업데이트 확인
			await waitFor(
				() => {
					expect(screen.getByText(/100개 상품이 임시 저장됨/)).toBeInTheDocument();
				},
				{ timeout: 2000 }
			);

			const endTime = Date.now();
			const processingTime = endTime - startTime;

			// 처리 시간이 합리적인 범위 내에 있어야 함 (5초 이내)
			expect(processingTime).toBeLessThan(5000);

			console.log(`100개 상품 처리 시간: ${processingTime}ms`);
		});

		it('자동 새로고침이 메모리 누수 없이 동작해야 함', async () => {
			const { component, unmount } = render(BatchStatusIndicator, {
				props: { palletId: testPalletId, autoRefresh: true, refreshInterval: 50 }
			});

			// 상품 추가
			await addProduct('QA001', { name: '테스트 상품' }, testPalletId);

			// 잠시 대기하여 여러 번의 새로고침이 발생하도록 함
			await new Promise((resolve) => setTimeout(resolve, 200));

			// 컴포넌트 언마운트
			unmount();

			// 언마운트 후에도 타이머가 정리되었는지 확인
			// (실제로는 메모리 누수 검사 도구를 사용해야 하지만, 여기서는 기본적인 확인만)
			expect(true).toBe(true); // 언마운트가 성공적으로 완료됨
		});
	});

	describe('접근성 테스트', () => {
		it('스크린 리더를 위한 적절한 레이블이 있어야 함', async () => {
			await addProduct('QA001', { name: '테스트 상품' }, testPalletId);

			const { component } = render(BatchStatusIndicator, {
				props: { palletId: testPalletId }
			});

			// 제목 요소 확인
			expect(screen.getByRole('heading', { name: /배치 상태/ })).toBeInTheDocument();

			// 버튼에 적절한 title 속성 확인
			const refreshButton = screen.getByTitle('상태 새로고침');
			expect(refreshButton).toBeInTheDocument();
		});

		it('키보드 네비게이션이 가능해야 함', async () => {
			const { component } = render(BatchStatusIndicator, {
				props: { palletId: testPalletId }
			});

			const refreshButton = screen.getByTitle('상태 새로고침');

			// 포커스 가능 확인
			refreshButton.focus();
			expect(document.activeElement).toBe(refreshButton);

			// Enter 키로 클릭 가능 확인
			await fireEvent.keyDown(refreshButton, { key: 'Enter' });
			// 클릭 이벤트가 발생했는지 확인 (실제로는 더 정교한 테스트 필요)
		});
	});

	describe('다양한 상태 시나리오', () => {
		it('혼합 상태(성공/실패/대기)를 올바르게 표시해야 함', async () => {
			// palletApiService를 부분 실패로 모킹
			vi.mocked(await import('$lib/services/palletApiService')).saveProducts.mockResolvedValueOnce({
				success: false,
				message: '일부 실패',
				successIds: ['1'],
				failedItems: [{ id: '2', qaid: 'FAIL001', error: '테스트 실패' }]
			});

			// 상품 추가
			await addProduct('QA001', { name: '성공 상품' }, testPalletId);
			await addProduct('FAIL001', { name: '실패 상품' }, testPalletId);
			await addProduct('QA003', { name: '대기 상품' }, testPalletId);

			// 일부 상품 전송 (일부 실패)
			await submitAllProducts(testPalletId);

			const { component } = render(BatchStatusIndicator, {
				props: {
					palletId: testPalletId,
					showDetails: true,
					autoRefresh: true,
					refreshInterval: 100
				}
			});

			// 혼합 상태 확인
			await waitFor(
				() => {
					expect(screen.getByText(/대기.*실패/)).toBeInTheDocument();
				},
				{ timeout: 1000 }
			);

			// 상세 정보 확인
			expect(screen.getByText('저장 완료')).toBeInTheDocument();
			expect(screen.getByText('저장 실패')).toBeInTheDocument();
		});

		it('전송 중 상태를 올바르게 표시해야 함', async () => {
			// saveProducts를 지연시켜 전송 중 상태 시뮬레이션
			let resolvePromise: (value: any) => void;
			const delayedPromise = new Promise((resolve) => {
				resolvePromise = resolve;
			});

			vi.mocked(await import('$lib/services/palletApiService')).saveProducts.mockReturnValueOnce(
				delayedPromise
			);

			await addProduct('QA001', { name: '테스트 상품' }, testPalletId);

			const { component } = render(BatchStatusIndicator, {
				props: { palletId: testPalletId, autoRefresh: true, refreshInterval: 100 }
			});

			// 전송 시작
			const submitPromise = submitAllProducts(testPalletId);

			// 전송 중 상태 확인
			await waitFor(
				() => {
					expect(screen.getByText(/전송 중/)).toBeInTheDocument();
				},
				{ timeout: 500 }
			);

			// 전송 완료
			resolvePromise!({
				success: true,
				message: '저장 성공',
				successIds: ['1'],
				failedItems: []
			});

			await submitPromise;

			// 완료 상태 확인
			await waitFor(
				() => {
					expect(screen.getByText(/저장 완료/)).toBeInTheDocument();
				},
				{ timeout: 1000 }
			);
		});
	});
});
