import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/svelte';
import BatchStorageWarning from '../BatchStorageWarning.svelte';
import * as batchStorageUtils from '../../../utils/batchStorageUtils';
import * as batchProductService from '../../../services/batchProductService';
import type { StorageCapacityInfo } from '../../../types/batchTypes';

// 모의 로컬스토리지 구현
const mockLocalStorage = (() => {
	let store: Record<string, string> = {};
	return {
		getItem: vi.fn((key: string) => store[key] || null),
		setItem: vi.fn((key: string, value: string) => {
			store[key] = value;
		}),
		removeItem: vi.fn((key: string) => {
			delete store[key];
		}),
		clear: vi.fn(() => {
			store = {};
		}),
		get length() {
			return Object.keys(store).length;
		},
		key: vi.fn((index: number) => Object.keys(store)[index] || null)
	};
})();

// 전역 localStorage 모의
Object.defineProperty(window, 'localStorage', {
	value: mockLocalStorage
});

describe.skip('BatchStorageWarning 컴포넌트', () => {
	// 기본 모의 데이터
	const mockStorageInfo: StorageCapacityInfo = {
		used: 1024,
		available: 4096,
		total: 5120,
		percentUsed: 20,
		isNearLimit: false,
		batchUsed: 512,
		batchPercentUsed: 10
	};

	const mockProductCounts = {
		total: 10,
		pending: 5,
		submitting: 0,
		success: 3,
		failed: 2
	};

	beforeEach(() => {
		// 모의 함수 초기화
		vi.clearAllMocks();
		mockLocalStorage.clear();

		// 기본 모의 구현
		vi.spyOn(batchStorageUtils, 'checkStorageCapacity').mockReturnValue(mockStorageInfo);
		vi.spyOn(batchProductService, 'getProductCount').mockReturnValue(mockProductCounts);
		vi.spyOn(batchStorageUtils, 'cleanupStorage').mockImplementation(() => {});
	});

	afterEach(() => {
		vi.restoreAllMocks();
	});

	describe('기본 렌더링', () => {
		it('정상 상태에서는 컴포넌트가 표시되지 않아야 함', () => {
			render(BatchStorageWarning);

			// 정상 상태에서는 경고가 표시되지 않음
			expect(screen.queryByText(/스토리지/)).toBeNull();
		});

		it('showDetails가 true일 때는 항상 표시되어야 함', () => {
			render(BatchStorageWarning, {
				props: {
					showDetails: true
				}
			});

			expect(screen.getByText('스토리지 사용량')).toBeInTheDocument();
		});
	});

	describe('경고 레벨별 표시', () => {
		it('용량 부족 경고 (90% 이상)를 표시해야 함', () => {
			const criticalStorageInfo = {
				...mockStorageInfo,
				percentUsed: 95,
				isNearLimit: true
			};

			vi.spyOn(batchStorageUtils, 'checkStorageCapacity').mockReturnValue(criticalStorageInfo);

			render(BatchStorageWarning);

			expect(screen.getByText(/스토리지 용량이 부족합니다/)).toBeInTheDocument();
			expect(screen.getByText(/즉시 데이터를 저장하거나 정리해주세요/)).toBeInTheDocument();
		});

		it('용량 경고 (75% 이상)를 표시해야 함', () => {
			const warningStorageInfo = {
				...mockStorageInfo,
				percentUsed: 80,
				isNearLimit: true
			};

			vi.spyOn(batchStorageUtils, 'checkStorageCapacity').mockReturnValue(warningStorageInfo);

			render(BatchStorageWarning);

			expect(screen.getByText(/스토리지 용량이 부족해지고 있습니다/)).toBeInTheDocument();
			expect(screen.getByText(/데이터 저장을 권장합니다/)).toBeInTheDocument();
		});

		it('상품 수 권장 (100개 이상)을 표시해야 함', () => {
			const manyProductCounts = {
				...mockProductCounts,
				pending: 150
			};

			vi.spyOn(batchProductService, 'getProductCount').mockReturnValue(manyProductCounts);

			render(BatchStorageWarning);

			expect(screen.getByText(/많은 상품이 임시 저장되어 있습니다/)).toBeInTheDocument();
			expect(screen.getByText(/일괄 저장을 권장합니다/)).toBeInTheDocument();
		});
	});

	describe('사용자 상호작용', () => {
		it('새로고침 버튼 클릭 시 스토리지 정보를 업데이트해야 함', async () => {
			render(BatchStorageWarning, {
				props: {
					showDetails: true
				}
			});

			const refreshButton = screen.getByTitle('스토리지 정보 새로고침');
			await fireEvent.click(refreshButton);

			expect(batchStorageUtils.checkStorageCapacity).toHaveBeenCalled();
			expect(batchProductService.getProductCount).toHaveBeenCalled();
		});

		it('정리 버튼 클릭 시 스토리지 정리를 실행해야 함', async () => {
			const criticalStorageInfo = {
				...mockStorageInfo,
				percentUsed: 95,
				isNearLimit: true
			};

			vi.spyOn(batchStorageUtils, 'checkStorageCapacity').mockReturnValue(criticalStorageInfo);

			const onCleanupRequested = vi.fn();

			render(BatchStorageWarning, {
				props: {
					onCleanupRequested
				}
			});

			const cleanupButton = screen.getByTitle('불필요한 데이터를 정리합니다');
			await fireEvent.click(cleanupButton);

			expect(batchStorageUtils.cleanupStorage).toHaveBeenCalled();
			expect(onCleanupRequested).toHaveBeenCalled();
		});

		it('일괄 저장 버튼 클릭 시 콜백을 호출해야 함', async () => {
			const criticalStorageInfo = {
				...mockStorageInfo,
				percentUsed: 95,
				isNearLimit: true
			};

			vi.spyOn(batchStorageUtils, 'checkStorageCapacity').mockReturnValue(criticalStorageInfo);

			const onSaveRecommended = vi.fn();

			render(BatchStorageWarning, {
				props: {
					onSaveRecommended
				}
			});

			const saveButton = screen.getByTitle('임시 저장된 상품들을 서버로 전송합니다');
			await fireEvent.click(saveButton);

			expect(onSaveRecommended).toHaveBeenCalled();
		});

		it('상세 정보 토글 버튼이 작동해야 함', async () => {
			render(BatchStorageWarning, {
				props: {
					showDetails: true
				}
			});

			const toggleButton = screen.getByTitle('간단히 보기');
			await fireEvent.click(toggleButton);

			// 상세 정보가 숨겨져야 함
			await waitFor(() => {
				expect(screen.queryByText('배치 데이터')).toBeNull();
			});
		});
	});

	describe('자동 새로고침', () => {
		it('자동 새로고침이 활성화되어야 함', async () => {
			vi.useFakeTimers();

			render(BatchStorageWarning, {
				props: {
					autoRefresh: true,
					refreshInterval: 1000
				}
			});

			// 초기 호출
			expect(batchStorageUtils.checkStorageCapacity).toHaveBeenCalledTimes(1);

			// 1초 후 자동 새로고침
			vi.advanceTimersByTime(1000);
			expect(batchStorageUtils.checkStorageCapacity).toHaveBeenCalledTimes(2);

			vi.useRealTimers();
		});

		it('자동 새로고침이 비활성화되어야 함', async () => {
			vi.useFakeTimers();

			render(BatchStorageWarning, {
				props: {
					autoRefresh: false
				}
			});

			// 초기 호출만
			expect(batchStorageUtils.checkStorageCapacity).toHaveBeenCalledTimes(1);

			// 시간이 지나도 추가 호출 없음
			vi.advanceTimersByTime(5000);
			expect(batchStorageUtils.checkStorageCapacity).toHaveBeenCalledTimes(1);

			vi.useRealTimers();
		});
	});

	describe('프로그레스 바', () => {
		it('전체 스토리지 사용량 프로그레스 바를 표시해야 함', () => {
			render(BatchStorageWarning, {
				props: {
					showDetails: true
				}
			});

			expect(screen.getByText('전체 스토리지')).toBeInTheDocument();
			expect(screen.getByText(/1KB \/ 5KB \(20%\)/)).toBeInTheDocument();
		});

		it('배치 데이터 사용량 프로그레스 바를 표시해야 함', () => {
			render(BatchStorageWarning, {
				props: {
					showDetails: true
				}
			});

			expect(screen.getByText('배치 데이터')).toBeInTheDocument();
			expect(screen.getByText(/512B \(10%\)/)).toBeInTheDocument();
		});
	});

	describe('권장 사항', () => {
		it('용량 부족 시 권장 사항을 표시해야 함', () => {
			const criticalStorageInfo = {
				...mockStorageInfo,
				percentUsed: 95,
				isNearLimit: true
			};

			vi.spyOn(batchStorageUtils, 'checkStorageCapacity').mockReturnValue(criticalStorageInfo);

			render(BatchStorageWarning, {
				props: {
					showDetails: true,
					showRecommendation: true
				}
			});

			expect(screen.getByText('권장 사항')).toBeInTheDocument();
			expect(screen.getByText(/스토리지 용량이 부족합니다/)).toBeInTheDocument();
		});

		it('많은 상품 임시 저장 시 권장 사항을 표시해야 함', () => {
			const manyProductCounts = {
				...mockProductCounts,
				pending: 120
			};

			vi.spyOn(batchProductService, 'getProductCount').mockReturnValue(manyProductCounts);

			render(BatchStorageWarning, {
				props: {
					showDetails: true,
					showRecommendation: true
				}
			});

			expect(screen.getByText('권장 사항')).toBeInTheDocument();
			expect(screen.getByText(/100개 이상의 상품이 임시 저장되어 있습니다/)).toBeInTheDocument();
		});

		it('실패한 상품이 있을 때 권장 사항을 표시해야 함', () => {
			const failedProductCounts = {
				...mockProductCounts,
				failed: 5
			};

			vi.spyOn(batchProductService, 'getProductCount').mockReturnValue(failedProductCounts);

			render(BatchStorageWarning, {
				props: {
					showDetails: true,
					showRecommendation: true
				}
			});

			expect(screen.getByText('권장 사항')).toBeInTheDocument();
			expect(screen.getByText(/실패한 상품이 있습니다/)).toBeInTheDocument();
		});
	});

	describe('오류 처리', () => {
		it('스토리지 정보 조회 오류를 처리해야 함', () => {
			vi.spyOn(batchStorageUtils, 'checkStorageCapacity').mockImplementation(() => {
				throw new Error('스토리지 오류');
			});

			// 오류가 발생해도 컴포넌트가 크래시되지 않아야 함
			expect(() => {
				render(BatchStorageWarning, {
					props: {
						showDetails: true
					}
				});
			}).not.toThrow();
		});

		it('상품 수 조회 오류를 처리해야 함', () => {
			vi.spyOn(batchProductService, 'getProductCount').mockImplementation(() => {
				throw new Error('상품 수 조회 오류');
			});

			// 오류가 발생해도 컴포넌트가 크래시되지 않아야 함
			expect(() => {
				render(BatchStorageWarning, {
					props: {
						showDetails: true
					}
				});
			}).not.toThrow();
		});
	});
});
