<script lang="ts">
	import { faFileArrowDown } from '@fortawesome/free-solid-svg-icons/faFileArrowDown';
	import Icon from 'svelte-awesome';

	// 컴포넌트 props 타입 정의
	type ExcelDownloadProps = {
		color?: string;
		size?: string;
		margin?: string;
		useTooltip?: boolean;
		tooltipData?: string;
		onclick: () => void;
	};

	let {
		color = 'btn-success',
		size = 'btn-sm',
		margin = 'ml-2',
		useTooltip = false,
		tooltipData = '검색된 결과를 엑셀로 다운로드 합니다.',
		onclick
	} = $props();
</script>

{#if useTooltip}
	<button class="btn {color} {size} {margin} tooltip"
					data-tip={tooltipData}
					{onclick}
					type="button"
	>
		<Icon data={faFileArrowDown} />
		엑셀저장
	</button>
{:else}
	<button class="btn {color} {size} {margin}"
					{onclick}
					type="button"
	>
		<Icon data={faFileArrowDown} />
		엑셀저장
	</button>
{/if}