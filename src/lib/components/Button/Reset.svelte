<script>
	import { page } from '$app/state';
	import { faClockRotateLeft } from '@fortawesome/free-solid-svg-icons/faClockRotateLeft';

	import Icon from 'svelte-awesome';

	/** @type {{color?: string, size?: string, margin?: string, useTooltip?: boolean, tooltipData?: string, localUrl?: any}} */
	let {
		color = 'btn-info',
		size = 'btn-sm',
		margin = 'ml-2',
		useTooltip = false,
		tooltipData = "검색된 내용을 초기화 합니다.",
		localUrl = page.url.pathname
	} = $props();
</script>

{#if useTooltip}
	<button class="btn {color} {size} {margin} tooltip"
	        data-tip={tooltipData}
	        onclick={() => {
						window.location.href = localUrl;
	        }}
	        type="reset"
	>
		<Icon data={faClockRotateLeft} /> 초기화(F5)
	</button>
{:else}
	<button class="btn {color} {size} {margin}"
	        onclick={() => {
	        	window.location.href = localUrl;
	        }}
	        type="reset"
	>
		<Icon data={faClockRotateLeft} /> 초기화(F5)
	</button>
{/if}