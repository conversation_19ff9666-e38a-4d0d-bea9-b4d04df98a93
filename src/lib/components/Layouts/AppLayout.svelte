<script lang="ts">
	import type { User } from '$lib/User';
	
	import TopNav from '$components/Layouts/TopNav.svelte';
	import SideNav from '$components/Layouts/SideNav.svelte';
	import Footer from '$components/Layouts/Footer.svelte';

	interface Props {
		user: User;
		main?: import('svelte').Snippet;
	}

	let { user, main }: Props = $props();
</script>

<svelte:head>
	<title>CornerStone Project WMS</title>
</svelte:head>

<div class="flex flex-col h-screen overflow-hidden">
	<!-- 상단 고정 영역 -->
	<TopNav {user} />
	
	<!-- 하단 토글 가능 영역 -->
	<div class="flex flex-1 min-h-0">
		<!-- 좌측: 사이드바 메뉴 (토글 가능) -->
		<SideNav />
		
		<!-- 우측: 메인 콘텐츠 영역 -->
		<div class="flex-1 flex flex-col min-w-0">
			<!-- 메인 콘텐츠 -->
			<main class="flex-1 p-2 overflow-auto scrollbar-thin scrollbar-thumb-base-300 scrollbar-track-base-100">
				{@render main?.()}
			</main>
		</div>
	</div>
	
	<!-- 하단 푸터 -->
	<Footer />
</div>
