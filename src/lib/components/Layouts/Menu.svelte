<script lang="ts">
	import { onMount } from 'svelte';
	
	import { MENUS, menuStatus, menuToggle } from '$lib/Menu';

	import Icon from 'svelte-awesome';
	import { faApple } from '@fortawesome/free-brands-svg-icons/faApple';
	import { faHouse } from '@fortawesome/free-solid-svg-icons/faHouse';
	import { faShop } from '@fortawesome/free-solid-svg-icons/faShop';
	import { faTruckArrowRight } from '@fortawesome/free-solid-svg-icons/faTruckArrowRight';
	import { faHouseCircleCheck } from '@fortawesome/free-solid-svg-icons/faHouseCircleCheck';
	import { faGear } from '@fortawesome/free-solid-svg-icons/faGear';
	import { faSearch } from '@fortawesome/free-solid-svg-icons/faSearch';
	import { faLock } from '@fortawesome/free-solid-svg-icons/faLock';
	import { faScrewdriverWrench } from '@fortawesome/free-solid-svg-icons/faScrewdriverWrench';
	import { faChartPie } from '@fortawesome/free-solid-svg-icons/faChartPie';

	interface Props {
		routeId: string;
	}

	let { routeId }: Props = $props();

	// 메뉴 권한을 저장할 객체
	let menuPermissions: Record<string, boolean> = $state({});

	// 아이콘 맵핑
	const iconMap: {} = {
		'faApple': faApple,
		'faHouse': faHouse,
		'faShop': faShop,
		'faTruckArrowRight': faTruckArrowRight,
		'faHouseCircleCheck': faHouseCircleCheck,
		'faGear': faGear,
		'faSearch': faSearch,
		'faLock': faLock,
		'faScrewdriverWrench': faScrewdriverWrench,
		'faChartPie': faChartPie,
	};

	// 메뉴 항목이 권한이 있는지 확인하는 함수
	function hasMenuAccess(menuId: string): boolean {
		return menuPermissions[menuId] === true;
	}

	// 상위 메뉴가 표시되어야 하는지 확인 (하위 메뉴 중 하나라도 권한이 있으면 표시)
	function shouldShowParentMenu(menuId: string): boolean {
		const menu = MENUS.find(menu => menu.id === menuId);
		if (!menu) return false;

		// 부모 메뉴 자체가 권한이 있는지 확인
		if (hasMenuAccess(menuId)) return true;

		// 부모 메뉴의 하위 메뉴 중 하나라도 권한이 있는지 확인
		return menu.items.some(item => hasMenuAccess(item.id));
	}

	onMount(() => {
		const menuData = sessionStorage.getItem('menu');

		if (menuData) {
			try {
				menuPermissions = JSON.parse(menuData);
			} catch (e) {
				console.error('메뉴 권한 정보를 파싱하는데 실패했습니다:', e);
			}
		}
	});

</script>

<ul class="menu menu-md dropdown-content mt-1 bg-base-200 rounded-box w-52 z-50">
	{#each MENUS as menu}
		{#if shouldShowParentMenu(menu.id)}
			<li>
				<details bind:open={$menuStatus[menu.id]}>
					<summary onclick={(e) => {
						e.preventDefault();
						menuToggle(menu.id);
					}}>
						{#if menu.icon}
							<Icon data={iconMap[menu.icon]} />
						{/if}
						{menu.name}
					</summary>
					<ul>
						{#each menu.items as item}
							{#if hasMenuAccess(item.id)}
								<li>
									<a class={routeId === item.url ? 'active' : ''} href={item.url.replace('/(app)', '')}>
										{#if item.icon}
											<Icon data={iconMap[item.icon]} />
										{/if}
										{item.name}
									</a>
								</li>
							{/if}
						{/each}
					</ul>
				</details>
			</li>
		{/if}
	{/each}
</ul>