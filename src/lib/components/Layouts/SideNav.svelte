<script lang="ts">
	import { page } from '$app/state';
	import { sideNavVisible } from '$lib/stores/constant';

	import Menu from '$components/Layouts/Menu.svelte';

	let routeId: string = page.route.id as string;
</script>

{#if $sideNavVisible}
	<aside class="w-60 bg-base-100 flex-shrink-0 h-full">
		<div class="w-full h-full p-2 overflow-y-auto scrollbar-thin scrollbar-thumb-base-300 scrollbar-track-base-100">
			<Menu {routeId} />
		</div>
	</aside>
{/if}