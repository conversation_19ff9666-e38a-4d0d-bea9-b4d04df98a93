<script lang="ts">
	import { browser } from '$app/environment';

	import { setTheme, themes } from '$lib/Themes';
	import { authActions } from '$stores/authStore';
	import { sideNavVisible } from '$lib/stores/constant';

	import Icon from 'svelte-awesome';
	import { faRightFromBracket } from '@fortawesome/free-solid-svg-icons/faRightFromBracket';
	import { faPalette } from '@fortawesome/free-solid-svg-icons/faPalette';
	import { faCircleCheck } from '@fortawesome/free-solid-svg-icons/faCircleCheck';
	import { faAnglesLeft } from '@fortawesome/free-solid-svg-icons/faAnglesLeft';
	import { faBars } from '@fortawesome/free-solid-svg-icons/faBars';

	interface Props {
		user: User;
	}

	let { user }: Props = $props();

	let currentTheme = $state('light');
	if (browser) {
		currentTheme = window.localStorage.getItem('theme') ?? 'light';
	}
</script>

<svelte:window />

<div class="flex bg-base-100 h-12 flex-shrink-0">
	<!-- 좌측: 로고 및 토글 버튼 -->
	<div class="flex items-center justify-between w-60 px-3">
		<a href="/dashboard" class="text-base font-bold">CornerStone<b class="font-black">Project</b></a>
		
		<button class="btn btn-ghost btn-sm" tabindex="0" type="button"
						onclick={() => $sideNavVisible = !$sideNavVisible}
		>
			<Icon class="w-4 h-4" data={$sideNavVisible ? faAnglesLeft : faBars} />
		</button>
	</div>
	
	<!-- 우측: 내 정보/테마/로그아웃 -->
	<div class="flex-1">
		<nav class="navbar bg-base-100 flex w-full transition-all">
			<div class="w-full flex justify-end items-center">
				<!-- 사용자 메뉴 -->
				<div class="flex z-10">
					<ul class="menu menu-horizontal px-1">
						<li>
							<details>
								<summary>
									<span class="w-6 h-6 inline-flex">
										<img alt="{user?.name}"
												 class="rounded-full"
												 src="https://api.dicebear.com/7.x/initials/svg?seed=Cnspro&radius=50"
										/>
									</span>
									
									{user?.name}
								</summary>
								<ul class="dropdown-content menu bg-base-300 shadow rounded-box w-44 border border-base-300">
									<li><a href="/me/edit">내 정보</a></li>
								</ul>
							</details>
						</li>
					</ul>
				</div>
				
				<!-- 테마 선택 -->
				<div class="dropdown dropdown-hover dropdown-end z-10">
					<button class="btn btn-ghost" tabindex="0" type="button">
						<Icon data={faPalette} />
					</button>
					
					<ul class="dropdown-content menu bg-base-300 shadow rounded-box w-96 h-96 border border-base-300">
						{#each themes as theme}
							<li>
								<button class="items-center"
												onclick={() => {
													setTheme(theme);
													currentTheme = theme;
												}}
								>
									{#if theme === currentTheme}
										<Icon data={faCircleCheck} />
									{/if}
									{theme}
								</button>
							</li>
						{/each}
					</ul>
				</div>
				
				<!-- 로그아웃 -->
				<div class="z-10">
					<button class="btn btn-ghost" onclick={async () => {
						await authActions.logout();
					}} tabindex="0"
									type="button"
					>
						<Icon data={faRightFromBracket} label="로그아웃" />
					</button>
				</div>
			</div>
		</nav>
	</div>
</div>