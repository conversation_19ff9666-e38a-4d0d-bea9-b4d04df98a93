<script lang="ts">
  import type { MonitorBrand, MonitorUnit } from '$lib/types/monitorTypes';

  interface SearchParams {
    name?: string;
    brand?: MonitorBrand | '';
    unit?: MonitorUnit | '';
    min_size?: string;
    max_size?: string;
  }

  interface Props {
    params: SearchParams;
    onSearch: (params: { name?: string; brand?: MonitorBrand; unit?: MonitorUnit; min_size?: number; max_size?: number; }) => void;
  }

  let { params, onSearch }: Props = $props();

  function buildPayload() {
    const payload: any = {};
    if (params.name?.trim()) payload.name = params.name.trim();
    if (params.brand) payload.brand = params.brand as MonitorBrand;
    if (params.unit) payload.unit = params.unit as MonitorUnit;
    if (params.min_size && !Number.isNaN(Number(params.min_size))) payload.min_size = Number(params.min_size);
    if (params.max_size && !Number.isNaN(Number(params.max_size))) payload.max_size = Number(params.max_size);
    return payload;
  }

  function handleSubmit(e: Event) {
    e.preventDefault();
    onSearch(buildPayload());
  }

  function triggerInstantSearch() {
    onSearch(buildPayload());
  }
</script>

<form class="bg-base-100/80 rounded-lg p-3 shadow-sm" onsubmit={handleSubmit}>
  <div class="flex flex-wrap items-end gap-2">
    <label class="form-control w-64">
      <span class="label-text text-xs">상품명 (정확 매칭)</span>
      <input class="input input-xs input-bordered" bind:value={params.name} placeholder="예) 삼성 32인치 모니터" />
    </label>

    <label class="form-control w-40">
      <span class="label-text text-xs">브랜드</span>
      <select class="select select-xs select-bordered" bind:value={params.brand} onchange={triggerInstantSearch}>
        <option value="">전체</option>
        <option value="brand">brand</option>
        <option value="general">general</option>
      </select>
    </label>

    <label class="form-control w-36">
      <span class="label-text text-xs">단위</span>
      <select class="select select-xs select-bordered" bind:value={params.unit} onchange={triggerInstantSearch}>
        <option value="">전체</option>
        <option value="INCH">INCH</option>
        <option value="CM">CM</option>
      </select>
    </label>

    <label class="form-control w-36">
      <span class="label-text text-xs">최소 크기</span>
      <input class="input input-xs input-bordered" type="number" bind:value={params.min_size} placeholder="예) 27" />
    </label>

    <label class="form-control w-36">
      <span class="label-text text-xs">최대 크기</span>
      <input class="input input-xs input-bordered" type="number" bind:value={params.max_size} placeholder="예) 32" />
    </label>

    <div class="ml-auto">
      <button type="submit" class="btn btn-xs btn-primary">검색</button>
    </div>
  </div>
</form>


