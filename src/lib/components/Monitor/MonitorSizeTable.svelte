<script lang="ts">
  import type { MonitorBrand, MonitorSizeItem, MonitorUnit } from '$lib/types/monitorTypes';
	import { formatDateTimeToFullString } from '$lib/Functions';

  type SortField = 'name' | 'updated_at' | 'created_at';
  type SortDir = 'asc' | 'desc';

  interface Props {
    items: MonitorSizeItem[];
    startNo: number; // 페이지 시작 번호(역순 번호 표시에 사용)
    onSave: (id: number, payload: { brand: MonitorBrand; size: number; unit: MonitorUnit }) => Promise<void>;
    savingIds?: number[];
    sortBy: SortField;
    sortDir: SortDir;
    onSort: (field: SortField) => void;
    onError?: (message: string) => void;
  }

  let { items, startNo, onSave, savingIds = [], sortBy, sortDir, onSort, onError }: Props = $props();

  // 로컬 편집 상태 저장
  // 편집 상태: key = item.id
  let edits = $state<Record<number, { brand: MonitorBrand; size: string; unit: MonitorUnit }>>({});

  // 수정 버튼 클릭 시 폼 표시
  function startEdit(item: MonitorSizeItem) {
    edits[item.id] = { brand: item.brand, size: String(item.size), unit: item.unit };
    // 객체 재할당으로 반응성 트리거
    edits = { ...edits };
  }

  function cancelEdit(id: number) {
    delete edits[id];
    edits = { ...edits };
  }

  // 저장 처리
  async function save(id: number) {
    const e = edits[id];
    if (!e) return;
    const sizeNum = Number(e.size);
    if (Number.isNaN(sizeNum) || sizeNum < 0) {
      onError?.('사이즈는 0 이상의 숫자여야 합니다.');
      return;
    }
    await onSave(id, { brand: e.brand, size: sizeNum, unit: e.unit });
    delete edits[id];
    edits = { ...edits };
  }

  // 편집 여부 확인
  function isEditing(id: number) {
    return Boolean(edits[id]);
  }

  // 헤더 정렬 표시용 텍스트
  function sortIndicator(field: SortField) {
    if (sortBy !== field) return '';
    return sortDir === 'asc' ? '▲' : '▼';
  }
</script>

<div class="overflow-x-auto rounded-lg border border-base-300">
  <table class="table table-sm">
    <thead>
      <tr>
        <th class="w-16">번호</th>
        <th>
          <button class="link link-hover" onclick={() => onSort('name')}>이름 {sortIndicator('name')}</button>
        </th>
        <th class="w-24">브랜드</th>
        <th class="w-24">사이즈</th>
        <th class="w-20">단위</th>
        <th class="w-36">
          <button class="link link-hover" onclick={() => onSort('updated_at')}>수정시간 {sortIndicator('updated_at')}</button>
        </th>
        <th class="w-36">
					<button class="link link-hover" onclick={() => onSort('created_at')}>등록시간 {sortIndicator('created_at')}</button>
				</th>
        <th class="w-36">작업</th>
      </tr>
    </thead>
    <tbody>
      {#each items as item, idx (item.id)}
        <tr>
          <!-- 역순 번호: 페이지 시작번호에서 index를 뺀 값 -->
          <td>{Math.max(1, startNo - idx)}</td>
          <td>
            <div class="font-medium">{item.name}</div>
            <div class="text-xs text-base-content/60">{item.name_hash}</div>
          </td>
          <td>
            {#if isEditing(item.id)}
              <select class="select select-xs select-bordered" bind:value={edits[item.id].brand}>
                <option value="brand">brand</option>
                <option value="general">general</option>
              </select>
            {:else}
              <span class="badge badge-ghost badge-sm">{item.brand}</span>
            {/if}
          </td>
          <td>
            {#if isEditing(item.id)}
              <input class="input input-xs input-bordered w-24" type="number" bind:value={edits[item.id].size} />
            {:else}
              {item.size}
            {/if}
          </td>
          <td>
            {#if isEditing(item.id)}
              <select class="select select-xs select-bordered" bind:value={edits[item.id].unit}>
                <option value="INCH">INCH</option>
                <option value="CM">CM</option>
              </select>
            {:else}
              {item.unit}
            {/if}
          </td>
          <td class="text-xs">{formatDateTimeToFullString(item.updated_at)}</td>
          <td class="text-xs">{formatDateTimeToFullString(item.created_at)}</td>
          <td>
            {#if isEditing(item.id)}
              <div class="flex gap-1">
                <button class="btn btn-xs btn-primary" disabled={savingIds.includes(item.id)} onclick={() => save(item.id)}>저장</button>
                <button class="btn btn-xs" onclick={() => cancelEdit(item.id)}>취소</button>
              </div>
            {:else}
              <button class="btn btn-xs" onclick={() => startEdit(item)}>수정</button>
            {/if}
          </td>
        </tr>
      {/each}
    </tbody>
  </table>
</div>


