<script lang="ts">
	import type {
		PalletLocationSelectorProps,
		PalletLocationSelectorEvents
	} from '$lib/types/PalletTypes';
	import { createEventDispatcher, onMount } from 'svelte';
	import { loadedStore } from '$stores/loadedStore';
	import { getProcessGradeName } from '$stores/processStore';
	import { authClient } from '$lib/services/AxiosBackend';
	import { getFromLocalStorage, saveToLocalStorage } from '$lib/utils/StorageManager';
	import { AudioEvent, playAudio } from '$lib/utils/AudioManager';
	import { executeMessage } from '$lib/Functions';

	import Icon from 'svelte-awesome';
	import { faRedo } from '@fortawesome/free-solid-svg-icons/faRedo';
	import { faPrint } from '@fortawesome/free-solid-svg-icons/faPrint';
	import { faDolly } from '@fortawesome/free-solid-svg-icons/faDolly';

	// Props
	let {
		locationCountry = $bindable(),
		locationCity = $bindable(),
		store = $bindable(),
		line = $bindable(),
		rack = $bindable(),
		level = $bindable(),
		column = $bindable(),
		palletNumber = $bindable(),
		setLocation = $bindable(),
		workedLocationCode
	}: PalletLocationSelectorProps = $props();

	// 이벤트 디스패처
	const dispatch = createEventDispatcher<PalletLocationSelectorEvents>();

	// 내부 상태
	let isLoading = $state(false);
	let locationCode = $derived(
		`${locationCountry}-${locationCity}-${store}-${line}-${rack}-${level}-${column}`
	);
	let locationPlace = $derived(`${locationCountry}-${locationCity}`);

	/**
	 * 컴포넌트 마운트 시 초기화
	 */
	onMount(async () => {
		// 작업 중이던 위치 코드가 있으면 복원
		if (workedLocationCode) {
			const parts = workedLocationCode.split('-');
			if (parts.length >= 7) {
				locationCountry = parts[0];
				locationCity = parts[1];
				store = parts[2];
				line = parts[3];
				rack = parts[4];
				level = parts[5];
				column = parts[6];
			}
		}

		// 팔레트 목록 로드
		await reloadLocationCode();
	});

	/**
	 * 새로운 팔레트 번호를 생성하고 목록을 새로고침
	 */
	async function reloadLocationCode() {
		if (isLoading) return;

		isLoading = true;
		try {
			const { status, data } = await authClient.get(
				`/api/wms/pallets/loaded?location_country=${locationCountry}&location_city=${locationCity}&store=${store}&line=${line}&rack=${rack}`
			);

			if (status === 200) {
				loadedStore.set(data);
				executeMessage('팔레트 목록이 새로고침되었습니다.');
				playAudio(AudioEvent.REGISTER_COMPLETE);

				// 이벤트 발생
				dispatch('pallet-generate');
			} else {
				throw new Error('팔레트 목록을 불러오는데 실패했습니다.');
			}
		} catch (error) {
			console.error('팔레트 목록 로드 실패:', error);
			executeMessage('팔레트 목록 로드에 실패했습니다.');
			playAudio(AudioEvent.FAIL_AND_RETRY);
		} finally {
			isLoading = false;
		}
	}

	/**
	 * 팔레트 번호 설정
	 */
	function setPalletNo(pallet_no: string) {
		if (pallet_no !== '' && pallet_no !== '-') {
			palletNumber = pallet_no;

			// 팔레트 번호에서 level, column 추출
			const pallet_no_arr = pallet_no.split('-');
			if (pallet_no_arr.length >= 2) {
				level = pallet_no_arr[0];
				column = pallet_no_arr[1];

				// 자동으로 팔레트 확정
				setLocationCode();
			} else {
				level = '';
				column = '';
			}
		}
	}

	/**
	 * 팔레트 확정 처리
	 */
	async function setLocationCode() {
		if (!palletNumber) {
			executeMessage('팔레트 번호를 선택해 주세요.');
			playAudio(AudioEvent.FAIL_AND_RETRY);
			return;
		}

		if (!level || !column) {
			executeMessage('팔레트 위치 정보가 올바르지 않습니다.');
			playAudio(AudioEvent.FAIL_AND_RETRY);
			return;
		}

		try {
			const currentLocationCode = `${locationCountry}-${locationCity}-${store}-${line}-${rack}-${level}-${column}`;

			// 로컬스토리지에 작업 위치 저장
			saveToLocalStorage('worked_location_code', currentLocationCode);

			setLocation = true;

			executeMessage(`팔레트 ${palletNumber}이 확정되었습니다.`);
			playAudio(AudioEvent.REGISTER_COMPLETE);

			// 이벤트 발생
			dispatch('pallet-confirm', {
				locationCode: currentLocationCode,
				palletNumber
			});

			dispatch('location-change', {
				locationCode: currentLocationCode,
				palletNumber,
				setLocation: true
			});
		} catch (error) {
			console.error('팔레트 확정 실패:', error);
			executeMessage('팔레트 확정에 실패했습니다.');
			playAudio(AudioEvent.FAIL_AND_RETRY);
		}
	}

	/**
	 * 팔레트 변경 처리
	 */
	async function handleChangePallet() {
		if (!palletNumber) {
			setLocation = false;
			dispatch('location-change', {
				locationCode: '',
				palletNumber: '',
				setLocation: false
			});
			return;
		}

		try {
			// 팔레트 번호에서 위치 정보 추출
			const pallet_no_arr = palletNumber.split('-');
			if (pallet_no_arr.length >= 2) {
				level = pallet_no_arr[0];
				column = pallet_no_arr[1];

				// 비동기로 팔레트 확정 실행
				setTimeout(() => {
					setLocationCode();
				}, 100);
			}
		} catch (error) {
			console.error('팔레트 변경 처리 실패:', error);
			executeMessage('팔레트 변경 처리에 실패했습니다.');
			playAudio(AudioEvent.FAIL_AND_RETRY);
		}
	}

	/**
	 * 팔레트 번호 출력
	 */
	function printPalletNumber() {
		if (!palletNumber) {
			executeMessage('출력할 팔레트 번호가 없습니다.');
			playAudio(AudioEvent.FAIL_AND_RETRY);
			return;
		}

		try {
			// 팔레트 번호 출력 로직 (실제 구현 필요)
			executeMessage(`팔레트 번호 ${palletNumber} 출력을 요청했습니다.`);
			playAudio(AudioEvent.PRINT_LABEL);
		} catch (error) {
			console.error('팔레트 번호 출력 실패:', error);
			executeMessage('팔레트 번호 출력에 실패했습니다.');
			playAudio(AudioEvent.FAIL_AND_RETRY);
		}
	}
</script>

<!-- 점검완료 후 적재위치 -->
<div class="scan-container" id="location_box">
	<div class="scan-header">
		<div class="flex items-center gap-3">
			<Icon data={faDolly} scale={1.5} />
			<span class="text-xl font-bold">점검완료 후 적재위치</span>
		</div>
	</div>
	<div class="scan-content">
		<div class="flex flex-col lg:flex-row gap-4">
			<!-- 창고 지역 -->
			<div class="w-40 space-y-2">
				<div class="text-sm font-semibold text-gray-700 dark:text-gray-300">창고 지역</div>
				<div
					class="flex items-center h-10 px-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600"
				>
					<input bind:value={locationCountry} name="location_country" type="hidden" />
					<input bind:value={locationCity} name="location_city" type="hidden" />
					<span class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate"
						>코너스톤 C동</span
					>
				</div>
			</div>

			<!-- 존-층(단)-칸 -->
			<div class="w-48 space-y-2">
				<div class="text-sm font-semibold text-gray-700 dark:text-gray-300">존-층(단)-칸</div>
				<div class="flex items-center gap-1">
					<input
						bind:value={store}
						class="w-10 h-10 text-center text-lg font-bold bg-gray-700 text-gray-300 border border-gray-600 rounded-lg"
						id="store"
						name="store"
						readonly
						type="text"
					/>
					<span class="text-lg font-bold text-gray-700 dark:text-gray-300">-</span>
					<input
						bind:value={line}
						class="w-10 h-10 text-center text-lg font-bold bg-gray-700 text-gray-300 border border-gray-600 rounded-lg"
						id="line"
						name="line"
						readonly
						type="text"
					/>
					<span class="text-lg font-bold text-gray-700 dark:text-gray-300">-</span>
					<input
						bind:value={rack}
						class="w-10 h-10 text-center text-lg font-bold bg-gray-700 text-gray-300 border border-gray-600 rounded-lg"
						id="rack"
						name="rack"
						readonly
						type="text"
					/>
				</div>
			</div>

			<!-- 팔레트 번호 -->
			<div class="flex-1 space-y-2">
				<div class="text-sm font-semibold text-gray-700 dark:text-gray-300">팔레트 번호</div>
				<div class="flex items-center gap-2">
					<select
						bind:value={palletNumber}
						class="flex-1 px-3 py-2 text-sm font-medium bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg focus:border-blue-500 dark:focus:border-blue-400 focus:outline-none"
						id="pallet_no"
						onchange={handleChangePallet}
					>
						<option value="">선택</option>
						{#if $loadedStore.items && Array.isArray($loadedStore.items)}
							{#each $loadedStore.items as item (item.pallet_no || '')}
								<option value={item.pallet_no}>
									{item.pallet_no} [ {getProcessGradeName(item.palletGradeCode)} ]
								</option>
							{/each}
						{/if}
					</select>

					<!-- 새로고침 버튼 -->
					<button
						class="p-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors duration-200 disabled:opacity-50"
						onclick={reloadLocationCode}
						disabled={isLoading}
						type="button"
						title="팔레트 목록 새로고침"
					>
						<Icon data={faRedo} class={isLoading ? 'animate-spin' : ''} />
					</button>

					<!-- 팔레트 확정 버튼 -->
					<button
						class="w-24 h-10 bg-green-500 hover:bg-green-600 text-white text-sm font-medium rounded-lg transition-colors duration-200 disabled:opacity-50"
						onclick={setLocationCode}
						disabled={!palletNumber || isLoading}
						type="button"
					>
						팔레트확정
					</button>

					<!-- 출력 버튼 -->
					{#if palletNumber}
						<button
							class="p-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors duration-200"
							onclick={printPalletNumber}
							type="button"
							title="팔레트 번호 출력"
						>
							<Icon data={faPrint} scale={1.5} />
						</button>
					{/if}
				</div>
			</div>
		</div>
	</div>
</div>
