<script lang="ts">
	import type { ProductScannerProps, ProductScannerEvents } from '$lib/types/PalletTypes';
	import { createEventDispatcher, onMount, tick } from 'svelte';
	import { barcodeCommandService } from '$lib/services/BarcodeCommandService';
	import { AudioEvent, playAudio } from '$lib/utils/AudioManager';
	import { preventKoreanInput } from '$lib/Functions';

	import ScanMessage from '$components/UI/ScanMessage.svelte';
	import Icon from 'svelte-awesome';
	import { faBarcode } from '@fortawesome/free-solid-svg-icons/faBarcode';

	// Props
	let {
		isEnabled = $bindable(),
		scanMessage = $bindable(),
		focusOnMount = true,
		checkingBarcode = $bindable()
	}: ProductScannerProps = $props();

	// 이벤트 디스패처
	const dispatch = createEventDispatcher<ProductScannerEvents>();

	// 내부 상태
	let focusCheckingBarcode: HTMLInputElement;
	let isProcessing = $state(false);

	/**
	 * 컴포넌트 마운트 시 초기화
	 */
	onMount(async () => {
		// 한글 입력 방지 설정
		preventKoreanInput(checkingBarcode, (newValue) => {
			checkingBarcode = newValue;
		});

		// 자동 포커스 설정
		if (focusOnMount) {
			await activeFocus();
		}
	});

	/**
	 * 바코드 입력 필드에 포커스 설정
	 */
	async function activeFocus() {
		checkingBarcode = '';

		// DOM 요소가 존재하는지 확인
		if (focusCheckingBarcode) {
			focusCheckingBarcode.value = '';
		}

		await tick();

		// 포커스 설정 전에 다시 확인
		if (focusCheckingBarcode) {
			try {
				focusCheckingBarcode.focus();
				console.log('바코드 입력 필드 포커스 설정 성공');

				// 추가 안전장치: requestAnimationFrame으로 한 번 더 시도
				requestAnimationFrame(() => {
					if (focusCheckingBarcode && document.activeElement !== focusCheckingBarcode) {
						focusCheckingBarcode.focus();
						console.log('requestAnimationFrame으로 포커스 재설정');
					}
				});
			} catch (error) {
				console.error('포커스 설정 실패:', error);
			}
		} else {
			console.error('focusCheckingBarcode가 null입니다');
		}
	}

	/**
	 * 바코드 입력 처리
	 */
	async function handleBarcodeInput(event: Event) {
		if (isProcessing || !isEnabled) return;

		event.preventDefault();
		isProcessing = true;

		try {
			// 스캔 완료 오디오 재생
			await playAudio(AudioEvent.SCAN_COMPLETE);

			const barcode = checkingBarcode.trim();
			if (barcode === '') {
				scanMessage = '바코드를 스캔(입력)해 주시기 바랍니다.';
				return;
			}

			// 바코드 분석
			const analysisResult = barcodeCommandService.analyzeBarcode(barcode);

			// 바코드 스캔 이벤트 발생
			dispatch('barcode-scan', {
				barcode: analysisResult.processedBarcode || barcode,
				type: analysisResult.type
			});

			// 바코드 입력 이벤트 발생 (호환성을 위해)
			dispatch('barcode-input', {
				barcode: analysisResult.processedBarcode || barcode
			});

			// 바코드 입력 필드 초기화
			checkingBarcode = '';

			// 포커스 재설정
			setTimeout(() => {
				activeFocus();
			}, 100);
		} catch (error) {
			console.error('바코드 입력 처리 실패:', error);
			scanMessage = '바코드 처리 중 오류가 발생했습니다.';
			await playAudio(AudioEvent.FAIL_AND_RETRY);
		} finally {
			isProcessing = false;
		}
	}

	/**
	 * 키보드 이벤트 처리
	 */
	async function handleKeyDown(event: KeyboardEvent) {
		if (event.key === 'Enter') {
			await handleBarcodeInput(event);
		}
	}

	/**
	 * 외부에서 포커스 요청 시 처리
	 */
	export function requestFocus() {
		dispatch('scan-focus-request');
		activeFocus();
	}

	/**
	 * 바코드 입력 필드 초기화
	 */
	export function clearInput() {
		checkingBarcode = '';
		activeFocus();
	}

	/**
	 * 스캔 메시지 업데이트
	 */
	export function updateMessage(message: string) {
		scanMessage = message;
	}
</script>

<!--바코드 스캔-->
<div class="w-full">
	<div class="scan-container" id="product_box">
		<div class="scan-header">
			<div class="flex items-center gap-3">
				<Icon data={faBarcode} class="text-xl" />
				<span class="text-xl font-bold">바코드 스캔</span>
			</div>
		</div>

		<div class="scan-content">
			<!-- 스캔 메시지 -->
			<ScanMessage
				message={!isEnabled
					? '적재할 팔레트를 먼저 확정해 주세요.'
					: scanMessage || '바코드 값을 입력(스캔)해 주시기 바랍니다.'}
				type={!isEnabled || scanMessage ? 'error' : 'info'}
				show={true}
			/>

			<!-- 바코드 입력 -->
			<div class="flex flex-row gap-4">
				<div class="flex-1 scan-input-group">
					<input
						bind:this={focusCheckingBarcode}
						bind:value={checkingBarcode}
						class="scan-input"
						class:disabled={!isEnabled || isProcessing}
						id="checking_barcode"
						onkeydown={handleKeyDown}
						placeholder="바코드 입력 또는 스캔"
						disabled={!isEnabled || isProcessing}
						type="text"
					/>

					<button
						class="scan-button"
						class:disabled={!isEnabled || isProcessing}
						id="checking_barcode_button"
						onclick={handleBarcodeInput}
						disabled={!isEnabled || isProcessing}
						type="button"
					>
						{isProcessing ? '처리중...' : '확인'}
					</button>
				</div>

				<!-- 추가 컨텐츠 슬롯 (팔레트 정보 등) -->
				<div class="flex-1">
					<slot />
				</div>
			</div>
		</div>
	</div>
</div>
