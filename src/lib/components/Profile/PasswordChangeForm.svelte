<script lang="ts">
	import Icon from 'svelte-awesome';
	import { faEyeSlash } from '@fortawesome/free-solid-svg-icons/faEyeSlash';
	import { faEye } from '@fortawesome/free-solid-svg-icons/faEye';

	interface PasswordChange {
		currentPassword: string;
		newPassword: string;
		passwordConfirmation: string;
	}

	let { passwordChange, onSubmit } = $props<{
		passwordChange: PasswordChange | null | undefined;
		onSubmit: (data: PasswordChange) => void;
	}>();

	// 기본값 설정
	let formData = $state({
		currentPassword: passwordChange?.currentPassword || '',
		newPassword: passwordChange?.newPassword || '',
		passwordConfirmation: passwordChange?.passwordConfirmation || ''
	});

	// 비밀번호 표시 상태
	let showCurrentPassword = $state(false);
	let showNewPassword = $state(false);
	let showConfirmPassword = $state(false);

	// 제출 오류 메시지 상태
	let submitErrorMessage = $state('');

	// 비밀번호 유효성 검사
	let validationErrors = $derived.by(() => {
		const errors: string[] = [];
		
		// 새 비밀번호가 현재 비밀번호와 같은지 확인
		if (formData.newPassword && formData.currentPassword && formData.newPassword === formData.currentPassword) {
			errors.push('새 비밀번호는 현재 비밀번호와 달라야 합니다.');
		}
		
		// 새 비밀번호와 확인 비밀번호가 다른지 확인
		if (formData.newPassword && formData.passwordConfirmation && formData.newPassword !== formData.passwordConfirmation) {
			errors.push('새 비밀번호와 확인 비밀번호가 일치하지 않습니다.');
		}
		
		// 새 비밀번호 복잡도 검사
		if (formData.newPassword) {
			if (formData.newPassword.length < 8) {
				errors.push('비밀번호는 8자 이상이어야 합니다.');
			}
			if (!/\d/.test(formData.newPassword)) {
				errors.push('비밀번호에 숫자가 포함되어야 합니다.');
			}
			if (!/[A-Z]/.test(formData.newPassword)) {
				errors.push('비밀번호에 영문 대문자가 포함되어야 합니다.');
			}
			if (!/[a-z]/.test(formData.newPassword)) {
				errors.push('비밀번호에 영문 소문자가 포함되어야 합니다.');
			}
			if (!/[!@#$%^&*(),.?":{}|<>]/.test(formData.newPassword)) {
				errors.push('비밀번호에 특수문자가 포함되어야 합니다.');
			}
		}
		
		return errors;
	});

	// 폼 제출 처리
	function handleSubmit(event: SubmitEvent) {
		event.preventDefault();
		
		// 제출 오류 메시지 초기화
		submitErrorMessage = '';
		
		// 유효성 검사 수행
		if (validationErrors.length > 0) {
			// 오류가 있으면 메시지 표시
			submitErrorMessage = '비밀번호 검증에 실패했습니다. 아래 오류를 확인해주세요.';
			return;
		}
		
		// 모든 검증을 통과한 경우에만 제출
		onSubmit(formData);
	}
</script>

<form
	class="w-full max-w-4xl mx-auto bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden"
	id="password_change_form"
	onsubmit={handleSubmit}
>
	<!-- 헤더 섹션 -->
	<div class="bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-6">
		<div class="flex items-center space-x-3">
			<div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
				<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
				</svg>
			</div>
			<div>
				<h2 class="text-xl font-bold text-white">비밀번호 변경</h2>
				<p class="text-blue-100 text-sm">안전한 비밀번호로 계정을 보호하세요</p>
			</div>
		</div>
	</div>

	<!-- 메인 콘텐츠 -->
	<div class="flex flex-col lg:flex-row">
		<!-- 폼 내용 (왼쪽) -->
		<div class="flex-1 px-8 py-6 space-y-6">
			<!-- 현재 비밀번호 -->
			<div class="space-y-2">
				<label class="block text-sm font-medium text-gray-700" for="current_password">
					현재 비밀번호
				</label>
				<div class="relative">
					<input
						bind:value={formData.currentPassword}
						class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
						id="current_password"
						name="current_password"
						placeholder="현재 비밀번호를 입력하세요"
						required
						type={showCurrentPassword ? 'text' : 'password'}
					/>
					<button
						class="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
						onclick={() => showCurrentPassword = !showCurrentPassword}
						type="button"
					>
						{#if showCurrentPassword}
							<Icon data={faEyeSlash} />
						{:else}
							<Icon data={faEye} />
						{/if}
					</button>
				</div>
			</div>

			<!-- 새 비밀번호 -->
			<div class="space-y-2">
				<label class="block text-sm font-medium text-gray-700" for="new_password">
					새 비밀번호
				</label>
				<div class="relative">
					<input
						bind:value={formData.newPassword}
						class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
						id="new_password"
						name="new_password"
						placeholder="새 비밀번호를 입력하세요"
						required
						type={showNewPassword ? 'text' : 'password'}
					/>
					<button
						class="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
						onclick={() => showNewPassword = !showNewPassword}
						type="button"
					>
						{#if showNewPassword}
							<Icon data={faEyeSlash} />
						{:else}
							<Icon data={faEye} />
						{/if}
					</button>
				</div>
			</div>

			<!-- 새 비밀번호 확인 -->
			<div class="space-y-2">
				<label class="block text-sm font-medium text-gray-700" for="password_confirmation">
					새 비밀번호 확인
				</label>
				<div class="relative">
					<input
						bind:value={formData.passwordConfirmation}
						class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
						id="password_confirmation"
						name="password_confirmation"
						placeholder="새 비밀번호를 다시 입력하세요"
						required
						type={showConfirmPassword ? 'text' : 'password'}
					/>
					<button
						class="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
						onclick={() => showConfirmPassword = !showConfirmPassword}
						type="button"
					>
						{#if showConfirmPassword}
							<Icon data={faEyeSlash} />
						{:else}
							<Icon data={faEye} />
						{/if}
					</button>
				</div>
			</div>
		</div>

		<!-- 사이드바 (오른쪽) -->
		<div class="lg:w-80 bg-gray-50 border-l border-gray-200 p-6">
			{#if submitErrorMessage}
				<!-- 제출 오류 메시지 -->
				<div class="bg-red-50 border border-red-200 rounded-xl p-4 mb-4">
					<div class="flex items-center space-x-2 mb-3">
						<div class="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
							<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
							</svg>
						</div>
						<span class="text-sm font-semibold text-red-800">제출 오류</span>
					</div>
					<p class="text-sm text-red-700 mb-3">{submitErrorMessage}</p>
					
					<!-- 구체적인 오류 목록 -->
					{#if validationErrors.length > 0}
						<div class="border-t border-red-200 pt-3">
							<div class="text-xs font-medium text-red-800 mb-2">구체적인 오류:</div>
							<ul class="space-y-1 mb-3">
								{#each validationErrors as error}
									<li class="flex items-start text-xs text-red-700">
										<span class="text-red-500 mr-2 mt-0.5">•</span>
										<span>{error}</span>
									</li>
								{/each}
							</ul>
						</div>
					{/if}

					<!-- 비밀번호 예시 -->
					<div class="border-t border-red-200 pt-3">
						<div class="text-xs font-medium text-red-800 mb-2">올바른 비밀번호 예시:</div>
						<div class="text-xs">
							<code class="bg-red-100 px-2 py-1 rounded text-red-800 font-mono">MyPass123!</code>
							<span class="text-red-600 mx-1">또는</span>
							<code class="bg-red-100 px-2 py-1 rounded text-red-800 font-mono">Secure@2025</code>
						</div>
					</div>
				</div>
			{:else if validationErrors.length > 0}
				<!-- 유효성 검사 오류 -->
				<div class="bg-red-50 border border-red-200 rounded-xl p-4">
					<div class="flex items-center space-x-2 mb-3">
						<div class="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
							<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
							</svg>
						</div>
						<span class="text-sm font-semibold text-red-800">비밀번호 검증 오류</span>
					</div>
					<ul class="space-y-2 mb-3">
						{#each validationErrors as error}
							<li class="flex items-start text-sm text-red-700">
								<span class="text-red-500 mr-2 mt-0.5">•</span>
								<span>{error}</span>
							</li>
						{/each}
					</ul>

					<!-- 비밀번호 예시 -->
					<div class="border-t border-red-200 pt-3">
						<div class="text-xs font-medium text-red-800 mb-2">올바른 비밀번호 예시:</div>
						<div class="text-xs">
							<code class="bg-red-100 px-2 py-1 rounded text-red-800 font-mono">MyPass123!</code>
							<span class="text-red-600 mx-1">또는</span>
							<code class="bg-red-100 px-2 py-1 rounded text-red-800 font-mono">Secure@2025</code>
						</div>
					</div>
				</div>
			{:else if formData.newPassword && formData.passwordConfirmation && formData.newPassword === formData.passwordConfirmation}
				<!-- 성공 메시지 -->
				<div class="bg-green-50 border border-green-200 rounded-xl p-4">
					<div class="flex items-center space-x-2 mb-3">
						<div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
							<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
							</svg>
						</div>
						<span class="text-sm font-semibold text-green-800">비밀번호가 일치합니다</span>
					</div>
					<p class="text-sm text-green-700">모든 조건을 만족합니다. 제출 버튼을 클릭하세요.</p>
				</div>
			{:else}
				<!-- 비밀번호 요구사항 -->
				<div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-4">
					<div class="flex items-center space-x-2 mb-4">
						<div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
							<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
							</svg>
						</div>
						<span class="text-sm font-semibold text-blue-800">비밀번호 요구사항</span>
					</div>
					<div class="space-y-2 text-sm text-blue-700">
						<p>• 8자 이상</p>
						<p>• 숫자 1개 이상</p>
						<p>• 영문 대문자 1개 이상</p>
						<p>• 영문 소문자 1개 이상</p>
						<p>• 특수문자 1개 이상</p>
					</div>
					<div class="mt-4 pt-3 border-t border-blue-200">
						<span class="text-xs text-blue-600">예시: </span>
						<code class="bg-blue-100 px-2 py-1 rounded text-blue-800 font-mono text-xs">MyPass123!</code>
						<span class="text-blue-500 mx-1">또는</span>
						<code class="bg-blue-100 px-2 py-1 rounded text-blue-800 font-mono text-xs">Secure@2025</code>
					</div>
				</div>
			{/if}
		</div>
	</div>

	<!-- 제출 버튼 -->
	<div class="px-8 py-6 bg-gray-50 border-t border-gray-100">
		<button 
			class="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 shadow-lg hover:shadow-xl" 
			type="submit"
		>
			<div class="flex items-center justify-center space-x-2">
				<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
				</svg>
				<span>비밀번호 변경</span>
			</div>
		</button>
	</div>
</form> 