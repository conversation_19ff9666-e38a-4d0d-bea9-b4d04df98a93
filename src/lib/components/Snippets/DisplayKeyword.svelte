<script lang="ts">
interface Props {
	display_keyword1: string;
	display_keyword2?: string;
}

let {
	display_keyword1,
	display_keyword2
}: Props = $props();
</script>

<!-- 눈에 잘 띄는 검색어 표시 -->
{#if display_keyword1 || display_keyword2}
	<div class="flex items-center gap-3 p-3 bg-primary/5 rounded-lg border-2 border-primary/20 shadow-sm">
		<!-- 제목 부분 - 더 눈에 띄게 -->
		<div class="flex items-center gap-2">
			<span class="text-base font-bold text-primary">🔍 검색어</span>
			<span class="text-primary/80 font-medium">:</span>
		</div>
		
		<!-- 검색어 표시 부분 - 더 강조 -->
		<div class="flex items-center gap-2">
			{#if display_keyword1}
				<span class="px-3 py-2 bg-primary/10 text-primary text-base font-semibold rounded-lg border border-primary/30 shadow-sm">
					{display_keyword1}
				</span>
			{/if}
			
			{#if display_keyword2}
				<span class="px-3 py-2 bg-success/10 text-success text-base font-semibold rounded-lg border border-success/30 shadow-sm">
					{display_keyword2}
				</span>
			{/if}
		</div>
		
		<!-- 검색어가 없을 때 안내 메시지 -->
		{#if !display_keyword1 && !display_keyword2}
			<span class="text-base-content/50 font-medium italic">검색어를 입력해주세요</span>
		{/if}
	</div>
{/if}