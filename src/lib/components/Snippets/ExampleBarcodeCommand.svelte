<script lang="ts">
	interface Props {
		commandVisible: boolean;
		page: string;
	}

	let { commandVisible, page = 'pallet' }: Props = $props();
	
	import Barcode from '$components/Snippets/Barcode.svelte';
	
	import Icon from 'svelte-awesome';
	import { faBell } from '@fortawesome/free-regular-svg-icons/faBell';
</script>

<div class="py-3"></div>

<div class="w-full border border-neutral-400">
	<div class="w-full p-3 flex flex-col items-center justify-center">
		<div
			class="w-full p-2 flex items-center justify-center text-2xl cursor-pointer"
			id="command_barcode_title"
			onclick={() => (commandVisible = !commandVisible)}
			role="presentation"
		>
			<Icon data={faBell} scale={2} />
			명령어 바코드
		</div>
		
		{#if commandVisible}
			{#if page === 'pallet'}
				<div class="w-full flex flex-col border border-neutral-400">
					<div class="w-full flex flex-row">
						<div class="w-1/4 flex items-center p-2 border-b border-r border-neutral-400 bg-neutral-300 text-black font-bold">
							팔레트교체
						</div>
						<div class="w-1/4 flex items-center justify-center p-2 border-b border-neutral-400">
							<Barcode
								id="barcode1"
								value="change_pallet/UX-405"
								options={{ fontOptions: 'bold' }}
							/>
						</div>
						<div class="w-2/4 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
							'change_pallet/UX-405'와 같이 'change_pallet/'다음에 해당팔레트번호를 추가한
							값을 스캔(입력)하거나 그냥 'UX-405'와 같은 팔레트번호를 스캔(입력)합니다.
						</div>
					</div>
					
					<div class="w-full flex flex-row">
						<div class="w-1/4 flex items-center p-2 border-b border-r border-neutral-400 bg-neutral-300 text-black font-bold">
							점검완료
						</div>
						<div class="w-1/4 flex items-center justify-center p-2 border-b border-neutral-400">
							<Barcode id="barcode2" value="complete" options={{ fontOptions: 'bold' }} />
						</div>
						<div class="w-2/4 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
							점검완료를 진행합니다. 해당 번호의 팔레트에 적재합니다.
						</div>
					</div>
					
					<div class="w-full flex flex-row">
						<div class="w-1/4 flex items-center p-2 border-b border-r border-neutral-400 bg-neutral-300 text-black font-bold">
							취소
						</div>
						<div class="w-1/4 flex items-center justify-center p-2 border-b border-neutral-400">
							<Barcode id="barcode3" value="cancel" options={{ fontOptions: 'bold' }} />
						</div>
						<div class="w-2/4 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
							점검취소를 진행합니다.
						</div>
					</div>
					
					<div class="w-full flex flex-row">
						<div class="w-1/4 flex items-center p-2 border-b border-r border-neutral-400 bg-neutral-300 text-black font-bold">
							증상내용
						</div>
						<div class="w-1/4 flex items-center justify-center p-2 border-b border-neutral-400">
							<Barcode
								id="barcode4"
								value="check/CH_OK"
								options={{ fontOptions: 'bold' }}
							/>
						</div>
						<div class="w-2/4 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
							'check/CH_OK'와 같이 'check/'다음에 증상내용코드를 추가한 값을
							스캔(입력)합니다.
						</div>
					</div>
					
					<div class="w-full flex flex-row">
						<div class="w-1/4 flex items-center p-2 border-b border-r border-neutral-400 bg-neutral-300 text-black font-bold">
							처리내용
						</div>
						<div class="w-1/4 flex items-center justify-center p-2 border-b border-neutral-400">
							<Barcode
								id="barcode5"
								value="repair/RP_CONFIRM"
								options={{ fontOptions: 'bold' }}
							/>
						</div>
						<div class="w-2/4 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
							'repair/RP_CONFIRM'과 같이 'repair/'다음에 처리내용코드를 추가한 값을
							스캔(입력)합니다.
						</div>
					</div>
					
					<div class="w-full flex flex-row">
						<div class="w-1/4 flex items-center p-2 border-b border-r border-neutral-400 bg-neutral-300 text-black font-bold">
							수리상태
						</div>
						<div class="w-1/4 flex items-center justify-center p-2 border-b border-neutral-400">
							<Barcode
								id="barcode6"
								value="grade/ST_BEST"
								options={{ fontOptions: 'bold' }}
							/>
						</div>
						<div class="w-2/4 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
							'grade/ST_BEST'와 같이 'grade/'다음에 수리상태코드를 추가한 값을
							스캔(입력)합니다.
						</div>
					</div>
					
					<div class="w-full flex flex-row">
						<div class="w-1/4 flex items-center p-2 border-b border-r border-neutral-400 bg-neutral-300 text-black font-bold">
							수리비용 사이즈기준 조회
						</div>
						<div class="w-1/4 flex items-center justify-center p-2 border-b border-neutral-400">
							<Barcode
								id="barcode7"
								value="basistype/size"
								options={{ fontOptions: 'bold' }}
							/>
						</div>
						<div class="w-2/4 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
							'basistype/size'를 스캔(입력)하면 해당 상품의 크기에 따른 비용측정기준을
							조회합니다.
						</div>
					</div>
					
					<div class="w-full flex flex-row">
						<div class="w-1/4 flex items-center p-2 border-b border-r border-neutral-400 bg-neutral-300 text-black font-bold">
							센티(cm)단위 수리비용 조회
						</div>
						<div class="w-1/4 flex items-center justify-center p-2 border-b border-neutral-400">
							<Barcode
								id="barcode8"
								value="basisunit/cm"
								options={{ fontOptions: 'bold' }}
							/>
						</div>
						<div class="w-2/4 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
							'basisunit/cm'를 스캔(입력)하면 해당 상품명에서 cm값을 읽어서 크기비용을
							조회합니다.
						</div>
					</div>
					
					<div class="w-full flex flex-row">
						<div class="w-1/4 flex items-center p-2 border-b border-r border-neutral-400 bg-neutral-300 text-black font-bold">
							인치(inch)단위 수리비용 조회
						</div>
						<div class="w-1/4 flex items-center justify-center p-2 border-b border-neutral-400">
							<Barcode
								id="barcode9"
								value="basisunit/inch"
								options={{ fontOptions: 'bold' }}
							/>
						</div>
						<div class="w-2/4 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
							'basisunit/inch'를 스캔(입력)하면 해당 상품명에서 inch(또는 인치 또는 그냥
							숫자)값을 읽어서 크기비용을 조회합니다.
						</div>
					</div>
					
					<div class="w-full flex flex-row">
						<div class="w-1/4 flex items-center p-2 border-b border-r border-neutral-400 bg-neutral-300 text-black font-bold">
							수리비용 금액기준 조회
						</div>
						<div class="w-1/4 flex items-center justify-center p-2 border-b border-neutral-400">
							<Barcode
								id="barcode10"
								value="basistype/price"
								options={{ fontOptions: 'bold' }}
							/>
						</div>
						<div class="w-2/4 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
							'basistype/price'를 스캔(입력)하면 해당 상품의 판매가에 따른 비용을
							조회합니다.
						</div>
					</div>
					
					<div class="w-full flex flex-row">
						<div class="w-1/4 flex items-center p-2 border-b border-r border-neutral-400 bg-neutral-300 text-black font-bold">
							금액(원)단위 수리비용 조회
						</div>
						<div class="w-1/4 flex items-center justify-center p-2 border-b border-neutral-400">
							<Barcode
								id="barcode11"
								value="basisunit/won"
								options={{ fontOptions: 'bold' }}
							/>
						</div>
						<div class="w-2/4 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
							'basisunit/won'을 스캔(입력)하면 해당 상품의 판매가에 따른 비용을 조회를
							위한 측정단위를 변경합니다.
						</div>
					</div>
					
					<div class="w-full flex flex-row">
						<div class="w-1/4 flex items-center p-2 border-b border-r border-neutral-400 bg-neutral-300 text-black font-bold">
							수리비용 공통기준 조회
						</div>
						<div class="w-1/4 flex items-center justify-center p-2 border-b border-neutral-400">
							<Barcode
								id="barcode12"
								value="basistype/none"
								options={{ fontOptions: 'bold' }}
							/>
						</div>
						<div class="w-2/4 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
							'basistype/none'을 스캔(입력)하면 해당 상품의 일반(기준없는)비용을
							조회합니다.
						</div>
					</div>
					
					<div class="w-full flex flex-row">
						<div class="w-1/4 flex items-center p-2 border-b border-r border-neutral-400 bg-neutral-300 text-black font-bold">
							OS설치비용
						</div>
						<div class="w-1/4 flex items-center justify-center p-2 border-b border-neutral-400">
							<Barcode
								id="barcode13"
								value="osinstall"
								options={{ fontOptions: 'bold' }}
							/>
						</div>
						<div class="w-2/4 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
							'osinstall'을 스캔(입력)하면 운영체재 재설치를 완료한 경우 비용청구를
							추가합니다.
						</div>
					</div>
					
					<div class="w-full flex flex-row">
						<div class="w-1/4 flex items-center p-2 border-b border-r border-neutral-400 bg-neutral-300 text-black font-bold">
							별도수리비용
						</div>
						<div class="w-1/4 flex items-center justify-center p-2 border-b border-neutral-400">
							<Barcode
								id="barcode14"
								value="fix/FX_CABLE@3000"
								options={{ fontOptions: 'bold' }}
							/>
						</div>
						<div class="w-2/4 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
							'fix/FX_CABLE@3000'와 같이 'fix/'다음에 추가비용코드 뒤에 '@'를 붙이고, 그
							뒤에 별도수리금액을 숫자값으로 추가한 바코드를 스캔(입력)합니다.
						</div>
					</div>
					
					<div class="w-full flex flex-row">
						<div class="w-1/4 flex items-center p-2 border-b border-r border-neutral-400 bg-neutral-300 text-black font-bold">
							기타추가비용
						</div>
						<div class="w-1/4 flex items-center justify-center p-2 border-b border-neutral-400">
							<Barcode
								id="barcode15"
								value="charge/CG_BOX@800"
								options={{ fontOptions: 'bold' }}
							/>
						</div>
						<div class="w-2/4 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
							'charge/CG_BOX@800'와 같이 'charge/'다음에 추가비용코드 뒤에 '@'를 붙이고,
							그 뒤에 비용금액을 숫자값으로 추가한 바코드를 스캔(입력)합니다.
						</div>
					</div>
				</div>
			{:else}
				<div class="w-full flex flex-col border border-neutral-400">
					<div class="w-full flex flex-row">
						<div class="w-1/3 flex flex-col border-b border-r border-neutral-400">
							<div class="w-full flex items-center justify-center p-2 bg-neutral-300 text-black font-bold">
								점검완료
							</div>
							<div class="w-full flex items-center justify-center p-2">
								<Barcode id="barcode1" value="complete" options={{ fontOptions: 'bold' }} />
							</div>
						</div>
						<div class="w-2/3 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
							점검완료를 진행합니다. 해당 번호의 팔레트에 적재합니다.
						</div>
					</div>
					<div class="w-full flex flex-row">
						<div class="w-1/3 flex flex-col border-b border-r border-neutral-400">
							<div class="w-full flex items-center justify-center p-2 bg-neutral-300 text-black font-bold">
								취소
							</div>
							<div class="w-full flex items-center justify-center p-2">
								<Barcode id="barcode2" value="cancel" options={{ fontOptions: 'bold' }} />
							</div>
						</div>
						<div class="w-2/3 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
							점검취소를 진행합니다.
						</div>
					</div>
					<div class="w-full flex flex-row">
						<div class="w-1/3 flex flex-col border-b border-r border-neutral-400">
							<div class="w-full flex items-center justify-center p-2 bg-neutral-300 text-black font-bold">
								증상내용
							</div>
							<div class="w-full flex items-center justify-center p-2">
								<Barcode id="barcode3" value="check/CH_OK" options={{ fontOptions: 'bold' }} />
							</div>
						</div>
						<div class="w-2/3 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
							'check/CH_OK'와 같이 'check/'다음에 증상내용코드를 추가한 값을 스캔(입력)합니다.
						</div>
					</div>
					<div class="w-full flex flex-row">
						<div class="w-1/3 flex flex-col border-b border-r border-neutral-400">
							<div class="w-full flex items-center justify-center p-2 bg-neutral-300 text-black font-bold">
								처리내용
							</div>
							<div class="w-full flex items-center justify-center p-2">
								<Barcode id="barcode4" value="repair/RP_CONFIRM" options={{ fontOptions: 'bold' }} />
							</div>
						</div>
						<div class="w-2/3 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
							'repair/RP_CONFIRM'과 같이 'repair/'다음에 처리내용코드를 추가한 값을 스캔(입력)합니다.
						</div>
					</div>
					<div class="w-full flex flex-row">
						<div class="w-1/3 flex flex-col border-b border-r border-neutral-400">
							<div class="w-full flex items-center justify-center p-2 bg-neutral-300 text-black font-bold">
								수리상태
							</div>
							<div class="w-full flex items-center justify-center p-2">
								<Barcode id="barcode5" value="grade/ST_BEST" options={{ fontOptions: 'bold' }} />
							</div>
						</div>
						<div class="w-2/3 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
							'grade/ST_BEST'와 같이 'grade/'다음에 수리상태코드를 추가한 값을 스캔(입력)합니다.
						</div>
					</div>
					<div class="w-full flex flex-row">
						<div class="w-1/3 flex flex-col border-b border-r border-neutral-400">
							<div class="w-full flex items-center justify-center p-2 bg-neutral-300 text-black font-bold">
								OS설치비용
							</div>
							<div class="w-full flex items-center justify-center p-2">
								<Barcode id="barcode6" value="osinstall" options={{ fontOptions: 'bold' }} />
							</div>
						</div>
						<div class="w-2/3 flex items-center p-2 border-b border-l border-neutral-400 text-sm">
							'osinstall'을 스캔(입력)하면 운영체재 재설치를 완료한 경우 비용청구를 추가합니다.
						</div>
					</div>
				</div>
			{/if}
		{/if}
	</div>
</div>