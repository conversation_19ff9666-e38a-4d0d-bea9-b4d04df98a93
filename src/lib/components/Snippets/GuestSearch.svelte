<script lang="ts">
	import { loadProduct, productStore } from '$stores/productStore';
	import { getProcessGradeColorButton } from '$stores/processStore';
	import { onMount } from 'svelte';
	import {
		getNumberFormat,
		displayRepairUsername,
		displayRepairTime,
		generateRandomNumber
	} from '$lib/Functions';
	import { getUser } from '$lib/User';
	import { openWebviewWindow } from '$lib/services/windowService';

	import SearchUI from '../UI/SearchUI.svelte';
	import Paginate from '$components/UI/Paginate.svelte';
	import DisplayKeyword from '$components/Snippets/DisplayKeyword.svelte';
	import TableTop from '$components/UI/TableTop.svelte';

	import Icon from 'svelte-awesome';
	import { faCopyright } from '@fortawesome/free-regular-svg-icons/faCopyright';
	import { REPAIR_STATUS_WAITING } from '$stores/repairStore';

	let user = getUser();

	// 게스트 전용 독립적인 상태 관리
	let keyword1 = $state('');
	let display_keyword1 = $state('');
	let p = $state('1');
	let pageSize = $state('16');
	let searchParams = $state('');
	let apiSearchParams = $state('');
	let localUrl = $state('/works/repairs');
	let startNo = $state(0);
	let isLoading = $state(false);

	// 테이블 아이템의 대기 상태 체크 함수
	function isItemWaiting(item: any): boolean {
		return item?.repair_product?.status === REPAIR_STATUS_WAITING;
	}

	// 게스트 전용 데이터 로딩 함수
	async function makeData(search: boolean = false) {
		if (!display_keyword1 || display_keyword1 === '') {
			return false;
		}

		isLoading = true;

		if (search) {
			p = '1';
		}

		const common_params = {
			searchType: 'qaid',
			keyword1: display_keyword1,
			pageSize
		};

		const params = new URLSearchParams({ p: p, ...common_params });
		const api_params = new URLSearchParams({ page: p, ...common_params });

		searchParams = params.toString();
		apiSearchParams = api_params.toString();

		await loadProduct(`/api/wms/products/search?${apiSearchParams}`, user);

		if ($productStore) {
			startNo = $productStore.pageStartNo ?? 0;
		}

		isLoading = false;
	}

	// 페이지 변경 핸들러
	async function handlePageChange(newPage: number) {
		p = newPage.toString();
		await makeData();
	}

	// 검색 파라미터 변경 핸들러
	async function changeSearchParams(newParams: string) {
		searchParams = newParams;
		await makeData(true);
	}

	// 검색 엔터 핸들러
	async function handleSearchEnter(e: KeyboardEvent, type: number) {
		if (e.key === 'Enter') {
			if (type === 1) {
				keyword1 = keyword1.trim();
				display_keyword1 = keyword1;
			}
			await makeData(true);
			keyword1 = '';
		}
	}

	// 컴포넌트 마운트 시 초기 데이터 로드
	onMount(async () => {
		$productStore = {};
		await makeData();
	});
</script>

<!-- 게스트 전용 검색 UI -->
<div class="guest-search-container">
	<SearchUI>
		<div class="w-full flex">
			<div class="flex flex-grow items-center pl-5 py-1 text-sm">
				<span class="mr-2"> QAID 검색 </span>
				<label class="input input-bordered input-sm flex items-center justify-center gap-2">
					<input
						bind:value={keyword1}
						class="grow bg-base-100"
						onkeydown={async (e) => await handleSearchEnter(e, 1)}
						placeholder="QAID 검색"
						type="text"
					/>
				</label>
			</div>
		</div>

		<DisplayKeyword {display_keyword1} />
	</SearchUI>

	<!-- 리스트 시작 -->
	<div class="px-2 flex-1 overflow-hidden">
		<TableTop onUpdate={changeSearchParams} {pageSize} total={$productStore.pageTotal ?? 0} />

		<div class="overflow-auto h-full">
			<table class="table text-xs table-pin-rows table-zebra w-full">
				<thead class="uppercase relative">
					<tr class="table-header-gradient text-center h-[50px] shadow-lg border-b-2">
						<th class="p-2 font-bold text-xs tracking-wide">번호</th>
						<th class="p-2 font-bold text-xs tracking-wide">QAID</th>
						<th class="p-2 font-bold text-xs tracking-wide">상품명</th>
						<th class="p-2 font-bold text-xs tracking-wide">점검수리</th>
						<th class="p-2 font-bold text-xs tracking-wide">점검자</th>
						<th class="p-2 font-bold text-xs tracking-wide">점검(수리)<br />일자</th>
					</tr>
				</thead>

				<tbody>
					{#if $productStore.products}
						{#each $productStore.products as item, index}
							<tr class="hover:bg-base-content/10" class:waiting-product-row={isItemWaiting(item)}>
								<td class="w-[50px] min-w-[50px] max-w-[50px] p-0.5 text-center">
									{getNumberFormat(startNo - index)}
								</td>
								<td class="w-[100px] min-w-[100px] max-w-[100px] p-0.5 text-center">
									{item.qaid}
								</td>
								<td class="p-0.5">
									<div>
										{item.name}
										{#if item.link?.product_id && item.link.product_id.slice(-8) !== '00000000'}
											<a
												href="https://www.coupang.com/"
												onclick={(e) => {
													e.preventDefault();

													const id = generateRandomNumber();
													const url = `https://www.coupang.com/vp/products/${item.link?.product_id}?itemId=${item.link?.item_id}&vendorItemId=${item.link?.vendor_item_id}`;
													openWebviewWindow(url, `coupang-${id}`, { width: 1280, height: 1024 });
												}}
											>
												<Icon data={faCopyright} class="text-error" />
											</a>
										{/if}
									</div>
									<div class="text-base-content/50">{item.barcode}</div>
								</td>
								<td class="w-[50px] min-w-[50px] max-w-[50px] p-0.5 text-center">
									{@html getProcessGradeColorButton(item)}
								</td>
								<td class="w-[50px] min-w-[50px] max-w-[50px] p-0.5 text-center">
									{displayRepairUsername(item)}
								</td>
								<td class="w-[90px] min-w-[90px] max-w-[90px] p-0.5 text-center">
									{displayRepairTime(item)}
								</td>
							</tr>
						{/each}
					{:else}
						<tr>
							<td colspan="14" class="text-center">검색 결과가 없습니다.</td>
						</tr>
					{/if}
				</tbody>
			</table>
		</div>
	</div>

	<!-- Pagination -->
	{#if $productStore.pageTotal && $productStore.pageTotal > 0}
		<Paginate store={productStore} {localUrl} onPageChange={handlePageChange} {searchParams} />
	{/if}
</div>

<style>
	.guest-search-container {
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
	}
</style>
