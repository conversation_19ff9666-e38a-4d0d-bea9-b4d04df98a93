<script lang="ts">
	interface Props {
		isAg: string;
		onUpdate: any;
	}

	let { isAg, onUpdate }: Props = $props();
	let isChecked = $state(isAg === 'Y');

	async function updateParams() {
		onUpdate({
			detail: {
				isAg: isChecked ? 'Y' : 'N'
			}
		});
	}
</script>

<!-- 모던하고 컴팩트한 체크박스 -->
<div class="flex items-center gap-2 p-2 bg-success/10 rounded-lg hover:bg-success/20 transition-colors">
	<label class="flex items-center gap-2 cursor-pointer">
		<input
			bind:checked={isChecked}
			class="checkbox checkbox-sm checkbox-success"
			onchange={updateParams}
			type="checkbox"
		/>
		<span class="text-sm font-medium {isChecked ? 'text-success' : 'text-success/70'}">AG</span>
	</label>
</div>