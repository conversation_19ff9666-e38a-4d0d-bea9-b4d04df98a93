<script lang="ts">
	import SearchField from '$components/Snippets/SearchField.svelte';
	import SearchFieldTitle from '$components/Snippets/SearchFieldTitle.svelte';
	import SearchFieldContent from '$components/Snippets/SearchFieldContent.svelte';

	import Icon from 'svelte-awesome';
	import { faArrowRight } from '@fortawesome/free-solid-svg-icons/faArrowRight';

	interface Props {
		category: any;
		cate4Selected: string;
		cate5Selected: string;
		onUpdate: any;
	}

	let { category, cate4Selected, cate5Selected, onUpdate }: Props = $props();

	function updateCate4(event: Event) {
		const target = event.target as HTMLSelectElement;
		if (target) {
			cate4Selected = target.value;
			onUpdate({
				detail: {
					cate4: cate4Selected,
					cate5: ''
				}
			});
		}
	}


	function updateCate5(event: Event) {
		const target = event.target as HTMLSelectElement;
		if (target) {
			cate5Selected = target.value;
			onUpdate({
				detail: {
					cate4: cate4Selected,
					cate5: cate5Selected
				}
			});
		}
	}
</script>

<!-- 모던하고 컴팩트한 카테고리 선택 행 -->
<SearchField>
	<SearchFieldTitle title="카테고리" />
	<SearchFieldContent>
		<select bind:value={cate4Selected}
						class="select select-sm select-bordered bg-base-100 border-base-300 focus:border-primary focus:ring-1 focus:ring-primary text-sm min-w-0"
						onchange={updateCate4}>
			<option value="">4차 카테고리</option>
			{#each category as item (item.id)}
				<option value={String(item.id)}>{item.name}</option>
			{/each}
		</select>
		
		<div class="flex items-center text-base-content/60">
			<Icon data={faArrowRight} scale={1.5} />
		</div>
		
		{#if cate4Selected}
			<select bind:value={cate5Selected}
							class="select select-sm select-bordered bg-base-100 border-base-300 focus:border-primary focus:ring-1 focus:ring-primary text-sm min-w-0"
							onchange={updateCate5}
			>
				<option value="">5차 카테고리</option>
				{#each category.find((item: any) => String(item.id) === cate4Selected).cate5 as item}
					<option value={String(item.id)}>{item.name}</option>
				{/each}
			</select>
		{:else}
			<select bind:value={cate5Selected}
							class="select select-sm select-bordered bg-base-100 border-base-300 focus:border-primary focus:ring-1 focus:ring-primary text-sm min-w-0"
							disabled
			>
				<option value="">5차 카테고리</option>
			</select>
		{/if}
	</SearchFieldContent>
</SearchField>