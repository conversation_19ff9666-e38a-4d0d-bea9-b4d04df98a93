<script lang="ts">
	import SearchField from '$components/Snippets/SearchField.svelte';
	import SearchFieldTitle from '$components/Snippets/SearchFieldTitle.svelte';
	import SearchFieldContent from '$components/Snippets/SearchFieldContent.svelte';

	interface Props {
		title?: string;
		exportedDate: string;
		onUpdate: any;
		children?: import('svelte').Snippet;
	}

	let {
		title = '출고일',
		exportedDate,
		onUpdate,
		children
	}: Props = $props();

	// 날짜 업데이트 함수
	function updateDate(event: Event) {
		try {
			const exported_date = new Date(exportedDate || '');

			// 날짜가 유효한지 확인
			if (isNaN(exported_date.getTime())) {
				return;
			}

			// 작은 값은 beginAt, 큰 값은 endAt으로 설정
			const formattedExportedDate = exported_date.toISOString().split('T')[0];

			onUpdate({
				detail: {
					exportedDate: formattedExportedDate,
				}
			});
		} catch (error) {
			console.error('날짜 처리 중 오류가 발생했습니다:', error);
		}
	}
</script>

<SearchField>
	<SearchFieldTitle title={title} />
	<SearchFieldContent>
		<input bind:value={exportedDate}
					 class="input input-sm input-bordered bg-base-100 border-base-300 focus:border-primary focus:ring-1 focus:ring-primary w-32 sm:w-36 text-sm"
					 name="beginAt"
					 onchange={updateDate}
					 type="date"
		>
		
		<div class="flex items-center gap-1">
			{@render children?.()}
		</div>
	</SearchFieldContent>
</SearchField>