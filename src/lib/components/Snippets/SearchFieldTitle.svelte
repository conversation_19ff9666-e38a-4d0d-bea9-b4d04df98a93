<script lang="ts">
	interface Props {
		children?: import('svelte').Snippet;
		title: string;
	}

	/** @type {{children?: import('svelte').Snippet}} */
	let { children, title }: Props = $props();
</script>

<div class="flex items-center w-32 sm:w-36 px-3 py-2 bg-base-200 text-base-content font-medium text-sm rounded-lg border border-base-300">
	<label class="flex items-center">
		{title}
		
		{@render children?.()}
	</label>
</div>