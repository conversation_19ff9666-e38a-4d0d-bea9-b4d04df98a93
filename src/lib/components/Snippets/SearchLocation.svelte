<script>
	import SearchField from '$components/Snippets/SearchField.svelte';
	import SearchFieldTitle from '$components/Snippets/SearchFieldTitle.svelte';
	import SearchFieldContent from '$components/Snippets/SearchFieldContent.svelte';

	/** @type {{store?: string, line?: string, rack?: string, level?: string, column?: string, onUpdate: any}} */
	let {
		store,
		line,
		rack,
		level,
		column,
		onUpdate
	} = $props();

	async function updateParams () {
		onUpdate({
			detail: {
				store: store,
				line: line,
				rack: rack,
				level: level,
				column: column
			}
		});
	}
</script>

<SearchField>
	<SearchFieldTitle title="팔레트 위치" />
	<SearchFieldContent>
		<input bind:value={store}
					 class="input input-bordered input-sm p-0 text-center w-16 h-8"
					 id="store"
					 name="store" onchange={updateParams}
					 placeholder="존"
					 type="text" />
		-
		<input bind:value={line}
					 class="input input-bordered input-sm p-0 text-center w-16 h-8"
					 id="line"
					 min="1" name="line"
					 onchange={updateParams} placeholder="층"
					 step="1"
					 type="number" />
		-
		<input bind:value={rack}
					 class="input input-bordered input-sm p-0 text-center w-16 h-8"
					 id="rack"
					 min="1" name="rack"
					 onchange={updateParams} placeholder="번"
					 step="1"
					 type="number" />
		-
		<input bind:value={level}
					 class="input input-bordered input-sm p-0 text-center w-20 h-8"
					 id="pallet"
					 name="pallet" onchange={updateParams}
					 placeholder="팔레트"
					 type="text" />
		-
		<input bind:value={column}
					 class="input input-bordered input-sm p-0 text-center w-16 h-8"
					 id="no"
					 min="0" name="no"
					 onchange={updateParams} placeholder="번호"
					 step="1"
					 type="number" />
	</SearchFieldContent>
</SearchField>