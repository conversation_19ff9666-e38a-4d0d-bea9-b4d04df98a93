<script lang="ts">
	import { locationEnable } from '$stores/locationStore';

	import SearchField from '$components/Snippets/SearchField.svelte';
	import SearchFieldTitle from '$components/Snippets/SearchFieldTitle.svelte';
	import SearchFieldContent from '$components/Snippets/SearchFieldContent.svelte';
	import SearchButton from '$components/Button/Search.svelte';

	interface Props {
		locationEnableGroup: string;
		onUpdate: any;
	}

	let { locationEnableGroup, onUpdate }: Props = $props();

	function updateParams(event: Event) {
		const target = event.target as HTMLElement;
		onUpdate({
			detail: {
				locationEnableGroup: target.value
			}
		});
	}
</script>

<SearchField>
	<SearchFieldTitle title="사용가능 여부" />
	<SearchFieldContent>
		<label class="cursor-pointer pl-2">
			<input bind:group={locationEnableGroup} class="radio-success radio-sm"
						 onclick={updateParams}
						 type="radio"
						 value="YN"> 전체
		</label>
		{#each $locationEnable as enable}
			<label class="cursor-pointer pl-2">
				<input bind:group={locationEnableGroup} class="radio-success radio-sm"
							 onclick={updateParams}
							 type="radio"
							 value={enable.value}> {enable.text}
			</label>
		{/each}
		
		<SearchButton onclick={updateParams} tooltipData="검색" useTooltip={true} />
	</SearchFieldContent>
</SearchField>