<script lang="ts">
	import SearchField from '$components/Snippets/SearchField.svelte';
	import SearchFieldTitle from '$components/Snippets/SearchFieldTitle.svelte';
	import SearchFieldContent from '$components/Snippets/SearchFieldContent.svelte';
	import SearchButton from '$components/Button/Search.svelte';

	import Icon from 'svelte-awesome';
	import { faXmark } from '@fortawesome/free-solid-svg-icons/faXmark';

	interface Props {
		keyword: string;
		children?: import('svelte').Snippet;
		onUpdate: any;
	}

	let { keyword, onUpdate, children }: Props = $props();

	async function updateParams() {
		onUpdate({
			detail: {
				keyword
			}
		});
	}
</script>

<SearchField>
	<SearchFieldTitle title="팔레트 번호" />
	<SearchFieldContent>
		<label class="input input-sm input-bordered flex items-center justify-center gap-2">
			<input bind:value={keyword}
						 class="grow bg-base-100"
						 onkeydown={e => {if (e.key === "Enter") { updateParams() }}}
						 type="text"
			/>
			
			<span onclick={() => {keyword = ''}} role="presentation">
				<Icon class="cursor-pointer" data={faXmark} />
			</span>
		</label>
		
		<SearchButton onclick={updateParams} tooltipData="검색" tooltipDirection="tooltip-bottom" useTooltip={true} />
		
		{@render children?.()}
	</SearchFieldContent>
</SearchField>