<script lang="ts">
	import { palletProductCheckedStatuses } from '$stores/palletProductStore';

	interface Props {
		checkedStatusGroup: string;
		onUpdate: any;
	}

	import SearchField from '$components/Snippets/SearchField.svelte';
	import SearchFieldTitle from '$components/Snippets/SearchFieldTitle.svelte';
	import SearchFieldContent from '$components/Snippets/SearchFieldContent.svelte';

	let { checkedStatusGroup, onUpdate }: Props = $props();

	function updatePalletStatus(event: Event) {
		const target = event.target as HTMLElement;

		onUpdate({
			detail: {
				checkedStatusGroup: target.value
			}
		});
	}
</script>

<SearchField>
	<SearchFieldTitle title="출고 검수 여부" />
	<SearchFieldContent>
		<label class="cursor-pointer pl-2">
			<input bind:group={checkedStatusGroup} class="radio-success radio-sm"
						 onclick={updatePalletStatus}
						 type="radio" value=""> 전체
		</label>
		{#each $palletProductCheckedStatuses as status}
			<label class="cursor-pointer pl-2">
				<input bind:group={checkedStatusGroup} class="radio-success radio-sm"
							 onclick={updatePalletStatus}
							 type="radio" value={status.value.toString()}> {status.text}
			</label>
		{/each}
	</SearchFieldContent>
</SearchField>