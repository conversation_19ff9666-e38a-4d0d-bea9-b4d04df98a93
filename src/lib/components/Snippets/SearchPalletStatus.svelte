<script lang="ts">
	import { palletStatuses } from '$stores/palletStore';
	
	import SearchField from '$components/Snippets/SearchField.svelte';
	import SearchFieldTitle from '$components/Snippets/SearchFieldTitle.svelte';
	import SearchFieldContent from '$components/Snippets/SearchFieldContent.svelte';

	interface Props {
		palletStatusGroup: string;
		onUpdate: any;
	}

	let { palletStatusGroup, onUpdate }: Props = $props();
	
	function updatePalletStatus(event: Event) {
		const target = event.target as HTMLInputElement;

		onUpdate({
			detail: {
				palletStatusGroup: target.value
			}
		});
	}
</script>

<SearchField>
	<SearchFieldTitle title="팔레트 상태" />
	<SearchFieldContent>
		<label class="flex items-center gap-1 cursor-pointer hover:bg-base-100/50 px-1.5 py-0.5 rounded transition-colors {palletStatusGroup === '' ? 'bg-primary/10 text-primary font-semibold' : ''}">
			<input bind:group={palletStatusGroup}
						 class="radio radio-sm radio-primary"
						 onclick={updatePalletStatus}
						 type="radio"
						 value="" />
			<span class="text-sm {palletStatusGroup === '' ? 'font-bold' : ''}">전체</span>
		</label>
		
		{#each $palletStatuses as status}
			{#if status.text !== '등록' && status.text !== '삭제'}
				<label class="flex items-center gap-1 cursor-pointer hover:bg-base-100/50 px-1.5 py-0.5 rounded transition-colors {palletStatusGroup === status.value.toString() ? 'bg-primary/10 text-primary font-semibold' : ''}">
					<input bind:group={palletStatusGroup}
								 class="radio radio-sm radio-primary"
								 onclick={updatePalletStatus}
								 type="radio"
								 value={status.value.toString()} />
					<span class="text-sm {palletStatusGroup === status.value.toString() ? 'font-bold' : ''}">{status.text}</span>
				</label>
			{/if}
		{/each}
	</SearchFieldContent>
</SearchField>