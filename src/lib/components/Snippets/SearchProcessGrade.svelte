<script lang="ts">
	import { processGrade } from '$stores/processStore';

	interface Props {
		processGradeGroup: string;
		onUpdate: any;
	}

	let { processGradeGroup, onUpdate }: Props = $props();

	function updateProcessGrade(event: Event) {
		const target = event.target as HTMLInputElement;
		onUpdate({
			detail: {
				processGradeGroup: target.value
			}
		});
	}
</script>

<!-- 모던하고 컴팩트한 상태 선택 행 -->
<div class="flex flex-col sm:flex-row items-start sm:items-center gap-2">
	<!-- 라벨 영역 -->
	<div class="flex items-center w-32 sm:w-36 px-3 py-2 bg-base-200 text-base-content font-medium text-sm rounded-lg border border-base-300">
		점검(수리) 상태
	</div>
	
	<!-- 라디오 버튼 영역 -->
	<div class="flex flex-1 items-center gap-2 bg-base-200/50 rounded-lg">
		<label class="flex items-center gap-1 cursor-pointer hover:bg-base-100/50 px-1.5 py-0.5 rounded transition-colors {processGradeGroup === '' ? 'bg-primary/10 text-primary font-semibold' : ''}">
			<input bind:group={processGradeGroup} 
						 checked 
						 class="radio radio-sm radio-primary"
						 onclick={updateProcessGrade}
						 type="radio" 
						 value="" />
			<span class="text-sm {processGradeGroup === '' ? 'font-bold' : 'font-medium'}">전체</span>
		</label>
		
		{#each $processGrade as grade}
			<label class="flex items-center gap-1 cursor-pointer hover:bg-base-100/50 px-1.5 py-0.5 rounded transition-colors {processGradeGroup === grade.value ? 'bg-primary/10 text-primary font-semibold' : ''}">
				<input bind:group={processGradeGroup} 
							 class="radio radio-sm radio-primary"
							 onclick={updateProcessGrade}
							 type="radio" 
							 value={grade.value} />
				<span class="text-sm {processGradeGroup === grade.value ? 'font-bold' : ''}">{grade.text}</span>
			</label>
		{/each}
	</div>
</div>