<script lang="ts">
	import SearchField from '$components/Snippets/SearchField.svelte';
	import SearchFieldTitle from '$components/Snippets/SearchFieldTitle.svelte';
	import SearchFieldContent from '$components/Snippets/SearchFieldContent.svelte';

	interface Props {
		carryoutStatusGroup: string;
		onUpdate: any;
	}

	let { carryoutStatusGroup, onUpdate }: Props = $props();

	function updateCheckedStatus(event: Event) {
		const target = event.target as HTMLElement;

		onUpdate({
			detail: {
				carryoutStatusGroup
			}
		});
	}
</script>

<SearchField>
	<SearchFieldTitle title="상품 상태" />
	<SearchFieldContent>
		<label class="cursor-pointer pl-2">
			<input bind:group={carryoutStatusGroup} class="radio-success radio-sm"
						 onchange={updateCheckedStatus}
						 type="radio" value="" /> 전체
		</label>
		
		<label class="cursor-pointer pl-2">
			<input bind:group={carryoutStatusGroup} class="radio-success radio-sm"
						 onchange={updateCheckedStatus}
						 type="radio" value="N" /> 반출
		</label>
		
		<label class="cursor-pointer pl-2">
			<input bind:group={carryoutStatusGroup} class="radio-success radio-sm"
						 onchange={updateCheckedStatus}
						 type="radio" value="Y" /> 반입
		</label>
	</SearchFieldContent>
</SearchField>