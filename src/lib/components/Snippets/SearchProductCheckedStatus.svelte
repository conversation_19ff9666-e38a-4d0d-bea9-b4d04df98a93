<script lang="ts">
	import { productCheckedStatuses } from '$stores/productStore';

	interface Props {
		checkedStatusGroup: string;
		onUpdate: any;
		children?: import('svelte').Snippet;
	}

	let { checkedStatusGroup, onUpdate, children }: Props = $props();

	function updateCheckedStatus(event: Event) {
		const target = event.target as HTMLInputElement;
		onUpdate({
			detail: {
				checkedStatusGroup: target.value
			}
		});
	}
</script>

<!-- 모던하고 컴팩트한 상태 선택 행 -->
<div class="flex flex-col sm:flex-row items-start sm:items-center gap-2">
	<!-- 라벨 영역 -->
	<div class="flex items-center w-32 sm:w-36 px-3 py-2 bg-base-200 text-base-content font-medium text-sm rounded-lg border border-base-300">
		입고검수 여부
	</div>
	
	<!-- 라디오 버튼 영역 -->
	<div class="flex flex-1 items-center gap-2 bg-base-200/50 rounded-lg">
		<label class="flex items-center gap-1 cursor-pointer hover:bg-base-100/50 px-1.5 py-0.5 rounded transition-colors {checkedStatusGroup === '' ? 'bg-primary/10 text-primary font-semibold' : ''}">
			<input bind:group={checkedStatusGroup} 
						 checked 
						 class="radio radio-sm radio-primary" 
						 onchange={updateCheckedStatus}
						 type="radio" 
						 value="" />
			<span class="text-sm {checkedStatusGroup === '' ? 'font-bold' : 'font-medium'}">전체</span>
		</label>
		
		{#each $productCheckedStatuses as status}
			<label class="flex items-center gap-1 cursor-pointer hover:bg-base-100/50 px-1.5 py-0.5 rounded transition-colors {checkedStatusGroup === status.value.toString() ? 'bg-primary/10 text-primary font-semibold' : ''}">
				<input bind:group={checkedStatusGroup} 
							 class="radio radio-sm radio-primary"
							 onchange={updateCheckedStatus}
							 type="radio" 
							 value={status.value.toString()} />
				<span class="text-sm {checkedStatusGroup === status.value.toString() ? 'font-bold' : ''}">{status.text}</span>
			</label>
		{/each}
	</div>
	
	<!-- 추가 컨텐츠 영역 -->
	<div class="flex items-center gap-1">
		{@render children?.()}
	</div>
</div>