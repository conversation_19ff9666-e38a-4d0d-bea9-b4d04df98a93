<script lang="ts">
	import { productStatuses } from '$stores/productStore';
	
	import SearchField from '$components/Snippets/SearchField.svelte';
	import SearchFieldTitle from '$components/Snippets/SearchFieldTitle.svelte';
	import SearchFieldContent from '$components/Snippets/SearchFieldContent.svelte';

	interface Props {
		productStatusGroup: string;
		onUpdate: any;
	}

	let { productStatusGroup, onUpdate }: Props = $props();

	function updateParams(event: Event) {
		const target = event.target as HTMLInputElement;
		onUpdate({
			detail: {
				productStatusGroup: target.value
			}
		});
	}
</script>

<!-- 모던하고 컴팩트한 상태 선택 행 -->
<SearchField>
	<SearchFieldTitle title="입고처리 상태" />
	<SearchFieldContent>
		<label class="flex items-center gap-1 cursor-pointer hover:bg-base-100/50 px-1.5 py-0.5 rounded transition-colors {productStatusGroup === '' ? 'bg-primary/10 text-primary font-semibold' : ''}">
			<input bind:group={productStatusGroup}
						 checked
						 class="radio radio-sm radio-primary"
						 onclick={updateParams}
						 type="radio"
						 value="" />
			<span class="text-sm {productStatusGroup === '' ? 'font-bold' : 'font-medium'}">전체</span>
		</label>
		
		{#each $productStatuses as status}
			<label class="flex items-center gap-1 cursor-pointer hover:bg-base-100/50 px-1.5 py-0.5 rounded transition-colors {productStatusGroup === status.value.toString() ? 'bg-primary/10 text-primary font-semibold' : ''}">
				<input bind:group={productStatusGroup}
							 class="radio radio-sm radio-primary"
							 onclick={updateParams}
							 type="radio"
							 value={status.value.toString()} />
				<span class="text-sm {productStatusGroup === status.value.toString() ? 'font-bold' : ''}">{status.text}</span>
			</label>
		{/each}
	</SearchFieldContent>
</SearchField>