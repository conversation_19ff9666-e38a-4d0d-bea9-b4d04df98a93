<script lang="ts">
	import { onMount, tick } from 'svelte';
	
	import SearchFieldTitle from '$components/Snippets/SearchFieldTitle.svelte';
	import SearchField from '$components/Snippets/SearchField.svelte';
	import SearchFieldContent from '$components/Snippets/SearchFieldContent.svelte';
	import SearchButton from '$components/Button/Search.svelte';

	interface Props {
		option1?: string;
		option2?: string;
		searchType?: string;
		keyword: string;
		onUpdate: any;
		children?: import('svelte').Snippet;
	}

	let {
		option1 = 'QAID/바코드/로트번호',
		option2 = '작성자',
		searchType = $bindable('qaid'),
		keyword = $bindable(),
		onUpdate,
		children
	}: Props = $props();
	
	let focusSearchInput: HTMLInputElement;

	async function updateParams() {
		onUpdate({
			detail: {
				searchType: searchType,
				keyword: keyword
			}
		});

		keyword = '';
		focusSearchInput.value = '';

		await tick();
		focusSearchInput.focus();
	}
	
	onMount(() => {
		focusSearchInput.focus();
	})
</script>

<SearchField>
	<SearchFieldTitle title="검색 항목" />
	<SearchFieldContent>
		<input bind:value={keyword}
					 bind:this={focusSearchInput}
					 class="input input-sm input-bordered bg-base-100 border-base-300 focus:border-primary focus:ring-1 focus:ring-primary w-60 text-sm"
					 placeholder="QAID/바코드/로트번호 검색"
					 onkeydown={e => {
							 if (e.key === "Enter") {
								 updateParams()
							 }
						 }}
					 type="text"
		/>
		
		<SearchButton onclick={updateParams} tooltipData="검색" useTooltip={true} />
		
		<!-- 추가 버튼 영역 -->
		<div class="flex items-center gap-1">
			{@render children?.()}
		</div>
	</SearchFieldContent>
</SearchField>