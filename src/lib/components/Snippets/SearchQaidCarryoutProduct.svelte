<script lang="ts">
	import SearchButton from '$components/Button/Search.svelte';

	import Icon from 'svelte-awesome';
	import { faXmark } from '@fortawesome/free-solid-svg-icons/faXmark';
	import SearchField from '$components/Snippets/SearchField.svelte';
	import SearchFieldTitle from '$components/Snippets/SearchFieldTitle.svelte';
	import SearchFieldContent from '$components/Snippets/SearchFieldContent.svelte';

	interface Props {
		searchType?: string;
		keyword: string;
		onUpdate: any;
		children?: import('svelte').Snippet;
	}

	let { searchType, keyword, onUpdate, children }: Props = $props();

	async function updateParams() {
		if (keyword) {
			onUpdate({
				detail: {
					searchType: searchType,
					keyword: keyword
				}
			});
		} else {
			alert('검색어를 입력해 주세요.');
		}
	}
</script>
<SearchField>
	<SearchFieldTitle title="검색 항목" />
	<SearchFieldContent>
		<select bind:value={searchType}
						class="select select-sm select-bordered bg-base-100 border-base-300 focus:border-primary focus:ring-1 focus:ring-primary text-sm min-w-0"
		>
			<option value="qaid">QAID/바코드/상품명</option>
			<option value="token">수리키</option>
			<option value="memo">수리내용(메모)</option>
		</select>
		
		<label class="input input-bordered input-sm flex items-center justify-center gap-2 mx-2">
			<input bind:value={keyword}
						 class="grow bg-base-100"
						 onkeydown={e => {if (e.key === "Enter") { updateParams() }}}
						 type="text"
						 placeholder="QAID/바코드/상품명 검색"
			/>
			
			<span onclick={() => {keyword = ''}} role="presentation">
				<Icon class="cursor-pointer" data={faXmark} />
			</span>
		</label>
		
		<SearchButton onclick={updateParams} tooltipData="검색" useTooltip={true} />
		
		{@render children?.()}
	</SearchFieldContent>
</SearchField>