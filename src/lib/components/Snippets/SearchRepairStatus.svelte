<script lang="ts">
	import { carryoutProductStatuses } from '$stores/carryoutProductStore';
	
	import SearchField from '$components/Snippets/SearchField.svelte';
	import SearchFieldTitle from '$components/Snippets/SearchFieldTitle.svelte';
	import SearchFieldContent from '$components/Snippets/SearchFieldContent.svelte';

	interface Props {
		repairStatusGroup: string;
		onUpdate: any;
	}
	
	let { repairStatusGroup, onUpdate }: Props = $props();

	function updatePalletStatus(event: Event) {
		const target = event.target as HTMLElement;

		onUpdate({
			detail: {
				repairStatusGroup: target.value
			}
		});
	}
</script>

<SearchField>
	<SearchFieldTitle title="팔레트 상태" />
	<SearchFieldContent>
		<label class="cursor-pointer pl-2">
			<input bind:group={repairStatusGroup} class="radio-success radio-sm"
						 onclick={updatePalletStatus}
						 type="radio" value=""> 전체
		</label>
		{#each $carryoutProductStatuses as status}
			<label class="cursor-pointer pl-2">
				<input bind:group={repairStatusGroup} class="radio-success radio-sm"
							 onclick={updatePalletStatus}
							 type="radio" value={status.value.toString()}> {status.text}
			</label>
		{/each}
	</SearchFieldContent>
</SearchField>