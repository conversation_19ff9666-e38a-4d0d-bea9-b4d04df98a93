<script lang="ts">
	interface Props {
		isRg: string;
		onUpdate: any;
	}

	let { isRg, onUpdate }: Props = $props();
	let isChecked = $state(isRg === 'Y');

	async function updateParams() {
		onUpdate({
			detail: {
				isRg: isChecked ? 'Y' : 'N'
			}
		});
	}
</script>

<!-- 모던하고 컴팩트한 체크박스 -->
<div class="flex items-center gap-2 p-2 bg-error/10 rounded-lg hover:bg-error/20 transition-colors">
	<label class="flex items-center gap-2 cursor-pointer">
		<input
			bind:checked={isChecked}
			class="checkbox checkbox-sm checkbox-error"
			onchange={updateParams}
			type="checkbox"
		/>
		<span class="text-sm font-medium {isChecked ? 'text-error' : 'text-base-error/70'}">RG</span>
	</label>
</div>