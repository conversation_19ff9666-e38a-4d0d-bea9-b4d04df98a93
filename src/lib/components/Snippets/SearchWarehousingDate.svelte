<script lang="ts">
	import SearchField from '$components/Snippets/SearchField.svelte';
	import SearchFieldTitle from '$components/Snippets/SearchFieldTitle.svelte';
	import SearchFieldContent from '$components/Snippets/SearchFieldContent.svelte';

	interface Props {
		title?: string;
		beginAt: string;
		endAt: string;
		onUpdate: any;
		children?: import('svelte').Snippet;
	}

	let {
		title = '입고 날짜',
		beginAt,
		endAt,
		onUpdate,
		children
	}: Props = $props();

	// 날짜 업데이트 함수
	function updateDate(event: Event) {
		try {
			const beginDate = new Date(beginAt || '');
			const endDate = new Date(endAt || '');

			// 날짜가 유효한지 확인
			if (isNaN(beginDate.getTime()) || isNaN(endDate.getTime())) {
				return;
			}

			// 작은 값은 beginAt, 큰 값은 endAt으로 설정
			const formattedBeginDate = beginDate.toISOString().split('T')[0];
			const formattedEndDate = endDate.toISOString().split('T')[0];

			if (beginDate <= endDate) {
				onUpdate({
					detail: {
						beginAt: formattedBeginDate,
						endAt: formattedEndDate,
					}
				});
			} else {
				onUpdate({
					detail: {
						beginAt: formattedEndDate,
						endAt: formattedBeginDate,
					}
				});
			}
		} catch (error) {
			console.error('날짜 처리 중 오류가 발생했습니다:', error);
		}
	}
</script>

<SearchField>
	<SearchFieldTitle title={title} />
	<SearchFieldContent>
		<input bind:value={beginAt}
					 class="input input-sm input-bordered bg-base-100 border-base-300 focus:border-primary focus:ring-1 focus:ring-primary w-32 sm:w-36 text-sm"
					 name="beginAt"
					 onchange={updateDate}
					 type="date"
		>
		
		<span class="text-base-content/60 font-medium">~</span>
		
		<input bind:value={endAt}
					 class="input input-sm input-bordered bg-base-100 border-base-300 focus:border-primary focus:ring-1 focus:ring-primary w-32 sm:w-36 text-sm"
					 name="endAt"
					 onchange={updateDate}
					 type="date"
		>
		
		<div class="flex items-center gap-1">
			{@render children?.()}
		</div>
	</SearchFieldContent>
</SearchField>