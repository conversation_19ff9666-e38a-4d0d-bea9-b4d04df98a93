<!--
토큰 만료 경고 컴포넌트
토큰이 곧 만료될 때 사용자에게 알림을 표시합니다.
-->
<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { isTokenExpiringSoon, authActions } from '$lib/stores/authStore';
	import { tokenService } from '$lib/services/tokenService';

	let showWarning = $state(false);
	let remainingTime = $state(0);
	let warningTimer: NodeJS.Timeout | null = null;
	let countdownTimer: NodeJS.Timeout | null = null;

	// Props
	interface Props {
		warningMinutes?: number; // 몇 분 전에 경고할지 (기본값: 5분)
		onextend?: () => void; // 세션 연장 버튼 클릭 시 콜백
		ondismiss?: () => void; // 경고 닫기 버튼 클릭 시 콜백
	}

	let { warningMinutes = 5, onextend, ondismiss }: Props = $props();

	/**
	 * 남은 시간을 확인하고 경고를 표시합니다.
	 */
	async function checkTokenExpiry() {
		try {
			const status = await tokenService.getTokenStatus();

			if (status.isAccessTokenValid && status.accessTokenRemainingTime > 0) {
				const remainingSeconds = status.accessTokenRemainingTime;
				const warningThreshold = warningMinutes * 60; // 분을 초로 변환

				if (remainingSeconds <= warningThreshold && remainingSeconds > 0) {
					remainingTime = remainingSeconds;
					showWarning = true;
					startCountdown();
				} else {
					showWarning = false;
				}
			} else {
				showWarning = false;
			}
		} catch (error) {
			console.error('[Token Expiry Warning] 토큰 상태 확인 실패:', error);
		}
	}

	/**
	 * 카운트다운을 시작합니다.
	 */
	function startCountdown() {
		if (countdownTimer) {
			clearInterval(countdownTimer);
		}

		countdownTimer = setInterval(() => {
			remainingTime -= 1;

			if (remainingTime <= 0) {
				showWarning = false;
				if (countdownTimer) {
					clearInterval(countdownTimer);
					countdownTimer = null;
				}
			}
		}, 1000);
	}

	/**
	 * 세션을 연장합니다.
	 */
	async function extendSession() {
		try {
			// 토큰 갱신 시도
			const refreshed = await authActions.refreshAuth();

			if (refreshed) {
				showWarning = false;
				onextend?.();

				if (import.meta.env.DEV) {
					console.log('[Token Expiry Warning] 세션 연장 성공');
				}
			}
		} catch (error) {
			console.error('[Token Expiry Warning] 세션 연장 실패:', error);
		}
	}

	/**
	 * 경고를 닫습니다.
	 */
	function dismissWarning() {
		showWarning = false;
		ondismiss?.();
	}

	/**
	 * 시간을 MM:SS 형식으로 포맷합니다.
	 */
	function formatTime(seconds: number): string {
		const minutes = Math.floor(seconds / 60);
		const remainingSeconds = seconds % 60;
		return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
	}

	onMount(() => {
		// 30초마다 토큰 만료 시간 확인
		warningTimer = setInterval(checkTokenExpiry, 30000);

		// 초기 확인
		checkTokenExpiry();
	});

	onDestroy(() => {
		if (warningTimer) {
			clearInterval(warningTimer);
		}
		if (countdownTimer) {
			clearInterval(countdownTimer);
		}
	});
</script>

{#if showWarning}
	<div class="fixed top-4 right-4 z-50 max-w-sm">
		<div class="alert alert-warning shadow-lg">
			<div class="flex items-center">
				<svg
					xmlns="http://www.w3.org/2000/svg"
					class="stroke-current shrink-0 h-6 w-6"
					fill="none"
					viewBox="0 0 24 24"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
					/>
				</svg>
				<div class="flex-1">
					<h3 class="font-bold text-sm">세션 만료 경고</h3>
					<div class="text-xs">
						{formatTime(remainingTime)} 후 자동 로그아웃됩니다.
					</div>
				</div>
			</div>
			<div class="flex gap-2 mt-2">
				<button class="btn btn-sm btn-primary" onclick={extendSession}> 세션 연장 </button>
				<button class="btn btn-sm btn-ghost" onclick={dismissWarning}> 닫기 </button>
			</div>
		</div>
	</div>
{/if}

<style>
	.alert {
		animation: slideIn 0.3s ease-out;
	}

	@keyframes slideIn {
		from {
			transform: translateX(100%);
			opacity: 0;
		}
		to {
			transform: translateX(0);
			opacity: 1;
		}
	}
</style>
