<script lang="ts">
	import Icon from 'svelte-awesome';
	import { faXmark } from '@fortawesome/free-solid-svg-icons/faXmark';

	interface Props {
		title?: string;
		message?: string;
		type?: 'error' | 'warning' | 'success';
		onClose?: () => void;
	}

	let { type = 'error', title = '알림', message = '', onClose }: Props = $props();

	let modal: HTMLDialogElement;

	export function show() {
		modal?.showModal();
	}

	export function close() {
		modal?.close();
	}

	function handleClose() {
		if (onClose) {
			onClose();
		}
	}
</script>

<dialog bind:this={modal} class="modal" onclose={handleClose}>
	<div
		class="modal-box w-1/2 max-w-2xl custom-modal"
		class:modal-error={type === 'error'}
		class:modal-warning={type === 'warning'}
		class:modal-success={type === 'success'}
	>
		<form method="dialog">
			<button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button>
		</form>

		<!-- 모달 제목 -->
		<div class="py-3">
			<h3 class="text-2xl font-bold modal-title">
				{title}
			</h3>
		</div>

		<!-- 모달 내용 -->
		<div class="py-5">
			<span class="text-lg modal-message">
				{@html message}
			</span>
		</div>

		<div class="modal-action w-full flex items-center justify-center">
			<form method="dialog">
				<button
					class="btn tooltip tooltip-top modal-button"
					class:btn-error={type === 'error'}
					class:btn-warning={type === 'warning'}
					class:btn-success={type === 'success'}
					data-tip="키보드의 Escape(ESC)키를 누르면 닫힙니다."
					onclick={handleClose}
				>
					<Icon class="w-5 h-5" data={faXmark} />
					닫기
				</button>
			</form>
		</div>
	</div>
</dialog>

<style>
    .custom-modal {
        background: linear-gradient(135deg, #ffffff 0%, #f3f4f6 100%);
        border: 2px solid #d1d5db;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    .custom-modal.modal-error {
        border-color: #dc2626;
        background: linear-gradient(135deg, #fa9c9c 0%, #ffdede 100%);
        box-shadow: 0 10px 25px rgba(220, 38, 38, 0.2);
    }

    .custom-modal.modal-warning {
        border-color: #d97706;
        background: linear-gradient(135deg, #fff380 0%, #ffffff 100%);
        box-shadow: 0 10px 25px rgba(217, 119, 6, 0.2);
    }

    .custom-modal.modal-success {
        border-color: #059669;
        background: linear-gradient(135deg, #7fffaa 0%, #ffffff 100%);
        box-shadow: 0 10px 25px rgba(5, 150, 105, 0.2);
    }

    .modal-title {
        color: #111827;
        text-shadow: none;
    }

    .modal-message {
        color: #374151;
        line-height: 1.6;
        text-shadow: none;
    }

    .modal-button {
        font-weight: 600;
        text-shadow: none;
        transition: all 0.2s ease-in-out;
    }

    .modal-button:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    /* 다크 모드 스타일 */
    .dark .custom-modal {
        background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
        border: 2px solid #4b5563;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
    }

    .dark .custom-modal.modal-error {
        border-color: #dc2626;
        background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
        box-shadow: 0 10px 25px rgba(220, 38, 38, 0.3);
    }

    .dark .custom-modal.modal-warning {
        border-color: #d97706;
        background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
        box-shadow: 0 10px 25px rgba(217, 119, 6, 0.3);
    }

    .dark .custom-modal.modal-success {
        border-color: #059669;
        background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
        box-shadow: 0 10px 25px rgba(5, 150, 105, 0.3);
    }

    .dark .modal-title {
        color: #f9fafb;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    }

    .dark .modal-message {
        color: #e5e7eb;
        line-height: 1.6;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .dark .modal-button {
        font-weight: 600;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        transition: all 0.2s ease-in-out;
    }

    .dark .modal-button:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }
</style>
