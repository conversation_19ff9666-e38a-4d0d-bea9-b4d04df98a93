<script lang="ts">
	import MessageModal from '$components/UI/MessageModal.svelte';

	let modal: MessageModal;

	function showErrorModal() {
		modal.show();
	}

	function showWarningModal() {
		modal.show();
	}

	function showSuccessModal() {
		modal.show();
	}

	function handleModalClose() {
		console.log('모달이 닫혔습니다');
	}
</script>

<div class="p-4 space-y-4">
	<h2 class="text-2xl font-bold">Modal 컴포넌트 사용 예시</h2>

	<div class="space-x-4">
		<button class="btn btn-error" onclick={showErrorModal}> 에러 모달 표시 </button>

		<button class="btn btn-warning" onclick={showWarningModal}> 경고 모달 표시 </button>

		<button class="btn btn-success" onclick={showSuccessModal}> 성공 모달 표시 </button>
	</div>
</div>

<!-- 에러 모달 -->
<MessageModal
	bind:this={modal}
	title="에러 발생"
	message="오류가 발생했습니다. 다시 시도해 주세요."
	type="error"
	onClose={handleModalClose}
/>

<!-- 다른 타입의 모달들도 필요에 따라 추가 가능 -->
