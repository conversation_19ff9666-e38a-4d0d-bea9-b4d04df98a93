<script lang="ts">
	import { getNumberFormat, scrollToElement } from '$lib/Functions';
	import { generatePageLinks, getPaginationSummary } from '$lib/utils/pagination';
	import type { OptimizedPageData } from '$lib/utils/pagination';
	import type { PaginationStore } from '$lib/types/types';
	import type { Readable } from 'svelte/store';

	import Icon from 'svelte-awesome';
	import { faAngleLeft } from '@fortawesome/free-solid-svg-icons/faAngleLeft';
	import { faAngleRight } from '@fortawesome/free-solid-svg-icons/faAngleRight';
	import { faAnglesLeft } from '@fortawesome/free-solid-svg-icons/faAnglesLeft';
	import { faAnglesRight } from '@fortawesome/free-solid-svg-icons/faAnglesRight';

	interface Props {
		store: Readable<PaginationStore>; // 페이지네이션 데이터를 포함하는 스토어
		localUrl: string;
		onPageChange: (page: number) => void;
		searchParams?: string;
		maxLinks?: number;
	}

	let { store, localUrl, onPageChange, searchParams = '', maxLinks = 10 }: Props = $props();

	// 스토어에서 페이지네이션 데이터 추출
	const pageData = $derived({
		total: $store.pageTotal ?? 0,
		current_page: $store.pageCurrentPage ?? 1,
		last_page: $store.pageLastPage ?? 1,
		per_page: $store.pagePerPage ?? 15,
		from: $store.pageFrom ?? 1,
		to: $store.pageTo ?? 1,
		has_prev: $store.hasPrev ?? ($store.pageCurrentPage ?? 1) > 1,
		has_next: $store.hasNext ?? ($store.pageCurrentPage ?? 1) < ($store.pageLastPage ?? 1)
	});

	// 페이지네이션 정보 계산
	const pageLinks = $derived(generatePageLinks(pageData.current_page, pageData.last_page, maxLinks));
	const paginationSummary = $derived(getPaginationSummary(pageData));

	// 페이지 변경 핸들러
	function handlePageChange(page: number) {
		onPageChange(page);
		scrollToElement("search-box-bottom");
	}

	// URL 생성 함수
	function createPageUrl(page: number) {
		const params = searchParams ? `&${searchParams}` : '';
		return `${localUrl}?p=${page}${params}#search-box-bottom`;
	}
</script>

<!-- 페이지네이션 정보 표시 -->
<div class="flex flex-col px-2 sm:flex-row justify-between items-center gap-2">
	<div class="text-sm text-gray-600">
		{#if paginationSummary.hasData}
			<span class="font-semibold">{paginationSummary.showing}</span> 
			/ 전체 <span class="font-semibold">{getNumberFormat(paginationSummary.total)}</span>개
		{:else}
			검색 결과가 없습니다.
		{/if}
	</div>
	
	<div class="text-sm text-gray-600">
		{#if paginationSummary.hasData}
			페이지 <span class="font-semibold">{paginationSummary.page}</span>
		{/if}
	</div>
</div>

<!-- 페이지네이션 버튼 -->
{#if paginationSummary.hasData && pageData.last_page > 1}
	<nav aria-label="페이지 네비게이션" class="flex justify-center pb-5">
		<div class="join">
			<!-- 첫 페이지 버튼 -->
			{#if pageData.current_page > 1}
				<a 
					class="join-item btn btn-sm" 
					href={createPageUrl(1)}
					onclick={(e) => {
						e.preventDefault();
						handlePageChange(1);
					}}
					title="첫 페이지"
				>
					<Icon data={faAnglesLeft} />
				</a>
			{/if}

			<!-- 이전 페이지 버튼 -->
			{#if pageData.has_prev}
				<a 
					class="join-item btn btn-sm" 
					href={createPageUrl(pageData.current_page - 1)}
					onclick={(e) => {
						e.preventDefault();
						handlePageChange(pageData.current_page - 1);
					}}
					title="이전 페이지"
				>
					<Icon data={faAngleLeft} />
				</a>
			{/if}

			<!-- 페이지 번호 버튼들 -->
			{#each pageLinks as link}
				{#if link.label === '...'}
					<span class="join-item btn btn-sm btn-disabled">
						...
					</span>
				{:else if link.active}
					<span class="join-item btn btn-sm btn-accent cursor-default">
						{getNumberFormat(Number(link.label))}
					</span>
				{:else if link.page}
					<a 
						class="join-item btn btn-sm" 
						href={createPageUrl(link.page)}
						onclick={(e) => {
							e.preventDefault();
							handlePageChange(link.page);
						}}
						title={`${link.label}페이지로 이동`}
					>
						{getNumberFormat(Number(link.label))}
					</a>
				{/if}
			{/each}

			<!-- 다음 페이지 버튼 -->
			{#if pageData.has_next}
				<a 
					class="join-item btn btn-sm" 
					href={createPageUrl(pageData.current_page + 1)}
					onclick={(e) => {
						e.preventDefault();
						handlePageChange(pageData.current_page + 1);
					}}
					title="다음 페이지"
				>
					<Icon data={faAngleRight} />
				</a>
			{/if}

			<!-- 마지막 페이지 버튼 -->
			{#if pageData.current_page < pageData.last_page}
				<a 
					class="join-item btn btn-sm" 
					href={createPageUrl(pageData.last_page)}
					onclick={(e) => {
						e.preventDefault();
						handlePageChange(pageData.last_page);
					}}
					title="마지막 페이지"
				>
					<Icon data={faAnglesRight} />
				</a>
			{/if}
		</div>
	</nav>
{/if}

<!-- 페이지 크기 선택 (옵션) -->
<!-- 추후 필요시 추가 가능 --> 