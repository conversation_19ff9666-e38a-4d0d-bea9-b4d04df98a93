<script lang="ts">
	import { onMount } from 'svelte';

	import type { Printer } from 'tauri-plugin-printer-api/dist/types';
	import { printers } from 'tauri-plugin-printer-api';
	import { getDefaultPrinter, getLocalLabelPrintList, setDefaultPrinter } from '$lib/Functions';

	import Icon from 'svelte-awesome';
	import { faCheck } from '@fortawesome/free-solid-svg-icons/faCheck';
	import { faCaretRight } from '@fortawesome/free-solid-svg-icons/faCaretRight';
	import { faCaretDown } from '@fortawesome/free-solid-svg-icons/faCaretDown';

	interface Props {
		className?: string;
	}

	let { className = '' }: Props = $props();

	let visible = $state(true);

	let allPrinters: Printer[] = $state([]);
	// 사용 가능한 라벨 프린트 리스트
	let myPrinters: Printer[] | undefined = $state(undefined);
	// 사용할 라벨 프린트
	let defaultPrint: Printer | null = $state(null);

	// 전체 프린터 목록 로딩 상태
	let isLoadingAllPrinters = $state(false);

	// 기본 프린터를 찾지 못했는지 여부 (전체 프린터 확인 모드)
	let isDefaultPrinterMissing = $state(false);

	onMount(async () => {
		myPrinters = await getLocalLabelPrintList();
		defaultPrint = getDefaultPrinter();

		// 기본 프린터를 찾지 못했거나 사용 가능한 라벨 프린터가 없으면 전체 프린터 확인 모드
		isDefaultPrinterMissing = !defaultPrint || !myPrinters || myPrinters.length === 0;
	});

	// 기본 프린터 설정 함수 (상태 업데이트 포함)
	function handleSetDefaultPrinter(printer: Printer) {
		setDefaultPrinter(printer);
		defaultPrint = printer;
		// 기본 프린터가 설정되면 일반 모드로 전환
		isDefaultPrinterMissing = false;
	}

	// 전체 프린터 목록을 가져오는 함수
	async function loadAllPrinters() {
		if (allPrinters.length === 0 && !isLoadingAllPrinters) {
			isLoadingAllPrinters = true;
			try {
				allPrinters = await printers();
			} catch (error) {
				console.error('프린터 목록을 가져오는 중 오류 발생:', error);
			} finally {
				isLoadingAllPrinters = false;
			}
		}
		visible = !visible;
	}
</script>

<div class={className}>
{#if isDefaultPrinterMissing}
	<p class="text-xl font-bold">전체 프린터 리스트</p>
	<p>라벨 프린터를 찾지 못할 경우 버튼을 클릭하고 아래 리스트를 프로그래머에게 전달해 주세요.</p>
	<button class="btn btn-accent text-xl font-bold" onclick={loadAllPrinters} disabled={isLoadingAllPrinters}>
		{#if isLoadingAllPrinters}
			<span class="loading loading-spinner loading-sm"></span>
			로딩 중...
		{:else if visible}
			<Icon data={faCaretRight} />
			전체 프린트 확인
		{:else}
			<Icon data={faCaretDown} />
			전체 프린트 확인
		{/if}
	</button>

	{#if visible === false && allPrinters.length > 0}
		<div class="border border-gray-400 rounded p-2">
			{#each allPrinters as printer}
				<p>{printer.name} : {printer.id}</p>
			{/each}
		</div>
	{/if}
{:else}
	<span class="text-xl font-bold">기본 라벨 프린터</span>
	{#if myPrinters && myPrinters.length > 0}
		<div class="flex flex-col md:flex-row md:space-y-0 md:space-x-3">
			{#each myPrinters as printer}
				{#if defaultPrint && printer.id === defaultPrint.id}
					<button
						type="button"
						onclick={() => handleSetDefaultPrinter(printer)}
						class="btn btn-success font-bold p-4"
					>
						<Icon data={faCheck} />
						{printer.name}
					</button>
				{:else}
					<button type="button" onclick={() => handleSetDefaultPrinter(printer)} class="btn p-4">
						{printer.name}
					</button>
				{/if}
			{/each}
		</div>
	{:else if myPrinters && myPrinters.length === 0}
		<p class="text-warning">사용 가능한 라벨 프린터가 없습니다.</p>
	{:else}
		<p class="text-info">프린터 목록을 불러오는 중...</p>
	{/if}
{/if}
</div>
