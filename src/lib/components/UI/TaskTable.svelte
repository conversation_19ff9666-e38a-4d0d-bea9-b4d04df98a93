<script lang="ts">
	import {
		copyToClipboard,
		formatDateTimeToFullString,
		formatDateTimeToString,
		getNumberFormat, displayRepairUsername,
		isOverDueDate, displayRepairTime, formattedReturnReason
	} from '$lib/Functions.js';
	import { getPalletNo, getPalletStatusName } from '$stores/palletStore.js';
	import { getProcessGradeColorButton } from '$stores/processStore.js';
	import {
		getProductCheckedStatusName,
		PRODUCT_STATUS_CARRIED_OUT,
		PRODUCT_STATUS_CARRIED_OUT_REPAIRED,
		PRODUCT_STATUS_CARRIED_OUT_WAITING
	} from '$stores/productStore.js';

	import Icon from 'svelte-awesome';
	import { faRocket } from '@fortawesome/free-solid-svg-icons/faRocket';
	import { faQuestion } from '@fortawesome/free-solid-svg-icons/faQuestion';
	
	interface Props {
		ids: string[];
		startNo: number;
		products: any[];
	}
	
	let { ids = $bindable([]), startNo, products }: Props = $props();

	let allChecked = $state(false); // 전체 선택: 체크박스
	let idChecked = $state([]); // 전체 선택: 모든 체크박스의 상태를 참조하는 reactive 변수
	
	// 전체 선택
	const toggleAllCheck = (items: any) => {
		allChecked = !allChecked;
		idChecked = items.map(() => allChecked);
		ids = allChecked ? items.map((item: any) => item.id) : [];
	};
	
	function isCarryedOut(status: number): boolean {
		return [PRODUCT_STATUS_CARRIED_OUT, PRODUCT_STATUS_CARRIED_OUT_WAITING, PRODUCT_STATUS_CARRIED_OUT_REPAIRED].includes(status);
	}
</script>

{#snippet tableHeader()}
<tr class="table-header-gradient">
	<th class="p-2 font-bold text-xs tracking-wide whitespace-nowrap">
		<div class="flex items-center justify-center">
			<input checked={allChecked}
					 onchange={() => toggleAllCheck(products)}
					 type="checkbox"
					 class="checkbox checkbox-sm checkbox-accent"
			/>
		</div>
	</th>
	<th class="p-2 font-bold text-xs tracking-wide whitespace-nowrap">번호</th>
	<th class="p-2 font-bold text-xs tracking-wide whitespace-nowrap">입고날짜</th>
	<th class="p-2 font-bold text-xs tracking-wide whitespace-nowrap">로트번호</th>
	<th class="p-2 font-bold text-xs tracking-wide whitespace-nowrap">카테고리</th>
	<th class="p-2 font-bold text-xs tracking-wide whitespace-nowrap">QAID</th>
	<th class="p-2 font-bold text-xs tracking-wide w-[200px] min-w-[200px] max-w-[200px]">상품명<br>바코드</th>
	<th class="p-2 font-bold text-xs tracking-wide whitespace-nowrap">중복</th>
	<th class="p-2 font-bold text-xs tracking-wide whitespace-nowrap">단가</th>
	<th class="p-2 font-bold text-xs tracking-wide whitespace-nowrap">검수상태</th>
	<th class="p-2 font-bold text-xs tracking-wide whitespace-nowrap">입고검수<br>일자</th>
	<th class="p-2 font-bold text-xs tracking-wide whitespace-nowrap">컨디션</th>
	<th class="p-2 font-bold text-xs tracking-wide whitespace-nowrap">점검자</th>
	<th class="p-2 font-bold text-xs tracking-wide whitespace-nowrap">점검/수리<br>일자</th>
	<th class="p-2 font-bold text-xs tracking-wide whitespace-nowrap">출고검사</th>
	<th class="p-2 font-bold text-xs tracking-wide whitespace-nowrap">출고일자</th>
</tr>
{/snippet}

<!-- 가로 스크롤을 위한 컨테이너 -->
<div class="overflow-x-auto">
	<table class="table text-xs table-zebra min-w-full">
		<thead class="uppercase relative">
			{@render tableHeader()}
		</thead>
		
		<tfoot class="uppercase relative">
			{@render tableHeader()}
		</tfoot>
		
		<tbody>
		{#if products}
			{#each products as item, index}
				<tr class="h-[54px] hover:bg-base-content/10">
					<td class="w-[20px] min-w-[20px] max-w-[20px] p-0.5 text-center whitespace-nowrap">
						<input bind:checked={idChecked[index]}
									 bind:group={ids}
									 value={item.id}
									 type="checkbox"
						/>
					</td>
					<td class="w-[50px] min-w-[50px] max-w-[50px] p-0.5 text-center whitespace-nowrap">
						{getNumberFormat(startNo - index)}
					</td>
					<td class="w-[80px] min-w-[80px] max-w-[80px] p-0.5 text-center whitespace-nowrap">
						<span class={item.status === 10 && isOverDueDate(item.req.req_at, item.rg)
							? 'font-bold text-red-700'
							: ''}>
							{item.req.req_at}
						</span>
					</td>
					<td class="w-[110px] min-w-[70px] max-w-[110px] p-0.5 text-center">
						<span class="cursor-copy" ondblclick={() => copyToClipboard(item.lot.name)} role="presentation">
							{item.lot.name}
						</span>
					</td>
					<td class="w-[180px] min-w-[120px] max-w-[180px] p-0.5 text-center">
						<div class="flex items-center">
							<div class="w-1/2 p-0">{item.cate4.name}</div>
							{#if item.cate5}
								<div class="w-1/2 p-0">{item.cate5.name}</div>
							{/if}
						</div>
					</td>
					<td class="w-[100px] min-w-[100px] max-w-[100px] p-0.5 text-center whitespace-nowrap">
						<span class="flex items-center justify-center">
							<span class="cursor-copy" ondblclick={() => copyToClipboard(item.qaid)} role="presentation">{item.qaid}</span>
							
							{#if item.rg === 'Y'}
								<Icon data={faRocket} class="mx-0.5 text-red-700" />
							{/if}
						</span>
					</td>
					<td class="w-[200px] min-w-[200px] max-w-[200px] p-0.5">
						<p class="truncate" title={item.name}>{item.name}</p>
						<p>
							<span class="text-base-content/50" title={item.barcode}>{item.barcode}</span>
							{#if item?.return_reason !== null}
								<span class="tooltip tooltip-info tooltip-right" data-tip={formattedReturnReason(item?.return_reason?.reason)}>
									<Icon data={faQuestion} class="text-primary-700 cursor-help" />
								</span>
							{/if}
						</p>
					</td>
					<td class="w-[30px] min-w-[30px] max-w-[30px] p-0.5 text-center whitespace-nowrap">
						{#if item.duplicated === "N"}
							-
						{:else}
							중복
						{/if}
					</td>
					<td class="w-[60px] min-w-[60px] max-w-[60px] p-0.5 text-right whitespace-nowrap">
						{getNumberFormat(item.amount)}
					</td>
					<td class="w-[76px] min-w-[76px] max-w-[76px] p-0.5 text-center whitespace-nowrap">
						<p>{getProductCheckedStatusName(item.checked_status)}</p>
						{#if isCarryedOut(item.status)}
							<p class="text-xs text-error">반출중</p>
						{/if}
					</td>
					<td class="w-[90px] min-w-[86px] max-w-[90px] p-0.5 text-center">
						{#if item.checked_at}
							{formatDateTimeToFullString(item.checked_at)}
						{:else}
							-
						{/if}
					</td>
					<td class="w-[50px] min-w-[50px] max-w-[50px] p-0.5 text-center whitespace-nowrap">
						{@html getProcessGradeColorButton(item)}
					</td>
					<td class="w-[50px] min-w-[50px] max-w-[50px] p-0.5 text-center whitespace-nowrap">
						{displayRepairUsername(item)}
					</td>
					<td class="w-[90px] min-w-[86px] max-w-[90px] p-0.5 text-center">
						{displayRepairTime(item)}
					</td>
					<td class="w-[90px] min-w-[86px] max-w-[90px] p-0.5 text-center">
						{#if item.pallet_products.length > 0}
							<p>{getPalletStatusName(item.pallet_products[0].pallet.status)}</p>
							<p>{formatDateTimeToFullString(item.pallet_products[0].pallet.registered_at)}</p>
						{:else}
							-
						{/if}
					</td>
					<td class="w-[100px] min-w-[86px] max-w-[100px] p-0.5 text-center whitespace-nowrap">
						{#if item.pallet_products.length > 0}
							{#if item.pallet_products[0].pallet.registered_at}
								<p class="badge badge-info badge-sm">
									<a href="/pallets/products?id={item.pallet_products[0].pallet_id}">
										{getPalletNo(item.pallet_products[0].pallet.location)}
									</a>
								</p>
							{/if}
							
							{#if item.pallet_products[0].pallet.exported_at}
								<p>{formatDateTimeToString(item.pallet_products[0].pallet.exported_at)}</p>
							{:else}
								<p>-</p>
							{/if}
						{:else}
							-
						{/if}
					</td>
				</tr>
			{/each}
				{/if}
		</tbody>
	</table>
</div>