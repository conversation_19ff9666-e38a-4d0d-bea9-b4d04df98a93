<script lang="ts">
	import type { Breadcrumb } from '$lib/types/types';

	import Icon from 'svelte-awesome';
	import { faFolder } from '@fortawesome/free-regular-svg-icons/faFolder';
	import { faFolderOpen } from '@fortawesome/free-regular-svg-icons/faFolderOpen';

	interface Props {
		breadcrumbs: Breadcrumb[];
	}

	let { breadcrumbs }: Props = $props();
</script>

<section class="pl-4">
	<div class="breadcrumbs">
		<ul>
			{#each breadcrumbs as crumb, index}
				<li>
					<a href={crumb.url}>
						{#if index === breadcrumbs.length - 1}
							<Icon data={faFolderOpen} />
						{:else}
							<Icon data={faFolder} />
						{/if}
						{crumb.title}
					</a>
				</li>
			{/each}
		</ul>
	</div>
</section>
