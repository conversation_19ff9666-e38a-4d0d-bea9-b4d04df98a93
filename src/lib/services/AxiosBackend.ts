import axios, { type AxiosInstance } from 'axios';
import { tokenService } from './tokenService';
import { tokenRefreshService } from './tokenRefreshService';
import type { TokenRefreshError } from '$lib/types/auth';

/**
 * JWT 인증 기반 HTTP 클라이언트
 * 자동 토큰 관리, 토큰 갱신, 에러 처리 기능을 제공합니다.
 */

/**
 * JWT 인증 기반 Axios 인스턴스
 */
export const authClient: AxiosInstance = axios.create({
	baseURL: import.meta.env.VITE_HOME_URL as string,
	headers: {
		Accept: 'application/json',
		'Content-Type': 'application/json'
	},
	timeout: 30000 // 30초 타임아웃
});

/**
 * 요청 인터셉터 설정
 * 모든 요청에 Authorization 헤더를 자동으로 추가합니다.
 */
authClient.interceptors.request.use(
	async (config) => {
		try {
			const token = await tokenService.getAccessToken();
			if (token) {
				config.headers.Authorization = `Bearer ${token}`;
			}

			if (import.meta.env.DEV) {
				console.log(`[Auth Client] ${config.method?.toUpperCase()} ${config.url}`, {
					hasToken: !!token,
					headers: config.headers
				});
			}

			return config;
		} catch (error) {
			console.error('[Auth Client] 요청 인터셉터 에러:', error);
			return config;
		}
	},
	(error) => {
		console.error('[Auth Client] 요청 설정 에러:', error);
		return Promise.reject(error);
	}
);

/**
 * 응답 인터셉터 설정
 * 401 에러 시 토큰 갱신을 시도하고, 실패 시 로그아웃 처리합니다.
 */
authClient.interceptors.response.use(
	(response) => {
		if (import.meta.env.DEV) {
			console.log(`[Auth Client] ${response.status} ${response.config.url}`, {
				data: response.data
			});
		}
		return response;
	},
	async (error) => {
		const originalRequest = error.config;

		// 401 Unauthorized 에러 처리
		if (error.response?.status === 401 && !originalRequest._retry) {
			originalRequest._retry = true;

			try {
				// 토큰 갱신 서비스를 사용하여 토큰 갱신
				const newToken = await tokenRefreshService.waitForTokenRefresh();

				if (newToken) {
					// 새로운 토큰으로 원래 요청 재시도
					originalRequest.headers.Authorization = `Bearer ${newToken}`;

					if (import.meta.env.DEV) {
						console.log('[Auth Client] 토큰 갱신 후 요청 재시도:', originalRequest.url);
					}

					return authClient(originalRequest);
				} else {
					// 토큰 갱신 실패 시 에러 반환 (로그아웃은 하지 않음)
					if (import.meta.env.DEV) {
						console.log('[Auth Client] 토큰 갱신 실패, 원본 에러 반환');
					}
					return Promise.reject(error);
				}
			} catch (refreshError) {
				// 토큰 갱신 중 에러 발생
				if (import.meta.env.DEV) {
					console.error('[Auth Client] 토큰 갱신 중 에러:', refreshError);
				}

				// TokenRefreshError 타입인 경우에만 자동 로그아웃 처리
				if (refreshError && typeof refreshError === 'object' && 'type' in refreshError) {
					const tokenError = refreshError as TokenRefreshError;

					// 네트워크 에러가 아닌 경우에만 로그아웃 처리
					if (tokenError.type !== 'NETWORK_ERROR') {
						await tokenRefreshService.handleRefreshFailure(tokenError);
					} else {
						// 네트워크 에러인 경우 로그아웃하지 않고 원본 에러 반환
						if (import.meta.env.DEV) {
							console.log('[Auth Client] 네트워크 에러로 인한 토큰 갱신 실패, 로그아웃하지 않음');
						}
					}
				}

				return Promise.reject(refreshError);
			}
		}

		// 기타 에러 처리
		if (import.meta.env.DEV) {
			console.error(`[Auth Client] ${error.response?.status || 'Network'} Error:`, {
				url: error.config?.url,
				method: error.config?.method,
				status: error.response?.status,
				data: error.response?.data,
				message: error.message
			});
		}

		return Promise.reject(error);
	}
);

/**
 * 토큰 서비스 초기화
 * 앱 시작 시 호출하여 토큰 서비스를 초기화합니다.
 */
export async function initializeAuthClient(): Promise<void> {
	try {
		await tokenService.initialize();

		if (import.meta.env.DEV) {
			console.log('[Auth Client] 초기화 완료');
		}
	} catch (error) {
		console.error('[Auth Client] 초기화 실패:', error);
		throw error;
	}
}
