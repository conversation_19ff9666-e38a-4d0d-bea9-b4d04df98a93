/**
 * 배치 상품 관리 서비스 테스트
 *
 * 이 파일은 배치 상품 관리 서비스의 기능을 테스트합니다.
 * 특히 일괄 처리 기능에 대한 테스트를 포함합니다.
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
	addProduct,
	submitAllProducts,
	retryFailedProducts,
	updateProductStatus,
	submitSingleProduct,
	getProductsByStatus, updateProduct
} from '../batchProductService';
import * as batchStorageUtils from '../../utils/batchStorageUtils';

// 모의 로컬스토리지 구현
const mockLocalStorage = (() => {
	let store: Record<string, string> = {};
	return {
		getItem: (key: string) => store[key] || null,
		setItem: (key: string, value: string) => {
			store[key] = value;
		},
		removeItem: (key: string) => {
			delete store[key];
		},
		clear: () => {
			store = {};
		},
		getAllKeys: () => Object.keys(store)
	};
})();

// palletApiService 모듈 모킹
vi.mock('../palletApiService', () => {
	return {
		saveProducts: vi.fn().mockImplementation(async (products) => {
			// 모의 API 응답 생성
			const successIds: string[] = [];
			const failedItems: Array<{ id: string; qaid: string; error: string }> = [];

			products.forEach((product: any) => {
				// 테스트를 위해 QAID가 'fail'로 시작하는 상품은 실패 처리
				if (product.qaid.startsWith('fail')) {
					failedItems.push({
						id: product.id,
						qaid: product.qaid,
						error: '테스트 오류'
					});
				} else {
					successIds.push(product.id);
				}
			});

			return {
				success: failedItems.length === 0,
				message:
					failedItems.length === 0
						? `${successIds.length}개 상품이 성공적으로 저장되었습니다.`
						: `${successIds.length}개 성공, ${failedItems.length}개 실패`,
				successIds,
				failedItems
			};
		}),
		saveProduct: vi.fn().mockImplementation(async (product) => {
			// 테스트를 위해 QAID가 'fail'로 시작하는 상품은 실패 처리
			if (product.qaid.startsWith('fail')) {
				return {
					success: false,
					message: '상품 저장 실패',
					error: '테스트 오류'
				};
			} else {
				return {
					success: true,
					message: '상품이 성공적으로 저장되었습니다.'
				};
			}
		})
	};
});

describe('배치 상품 관리 서비스 - 일괄 처리 기능', () => {
	beforeEach(() => {
		// 테스트 전 로컬스토리지 초기화
		mockLocalStorage.clear();

		// 로컬스토리지 모킹
		Object.defineProperty(window, 'localStorage', {
			value: mockLocalStorage,
			writable: true
		});

		// 배치 스토리지 초기화
		batchStorageUtils.initBatchStorage(true);
	});

	afterEach(() => {
		// 모든 모킹 초기화
		vi.clearAllMocks();
	});

	it('모든 상품을 서버로 전송할 수 있어야 함', async () => {
		// 테스트 데이터 준비
		const palletId = 'test-pallet-1';
		await addProduct('test-qaid-1', { name: '테스트 상품 1' }, palletId);
		await addProduct('test-qaid-2', { name: '테스트 상품 2' }, palletId);
		await addProduct('test-qaid-3', { name: '테스트 상품 3' }, palletId);

		// 일괄 전송 실행
		const result = await submitAllProducts(palletId);

		// 결과 검증
		expect(result.success).toBe(true);
		expect(result.successIds.length).toBe(3);
		expect(result.failedItems.length).toBe(0);

		// 상품 상태 검증
		const successProducts = getProductsByStatus('success', palletId);
		expect(successProducts.length).toBe(3);
	});

	it('일부 상품이 실패하는 경우를 처리할 수 있어야 함', async () => {
		// 테스트 데이터 준비
		const palletId = 'test-pallet-2';
		await addProduct('test-qaid-4', { name: '테스트 상품 4' }, palletId);
		await addProduct('fail-qaid-1', { name: '실패 테스트 상품 1' }, palletId);
		await addProduct('test-qaid-5', { name: '테스트 상품 5' }, palletId);

		// 일괄 전송 실행
		const result = await submitAllProducts(palletId);

		// 결과 검증
		expect(result.success).toBe(false);
		expect(result.successIds.length).toBe(2);
		expect(result.failedItems.length).toBe(1);

		// 상품 상태 검증
		const successProducts = getProductsByStatus('success', palletId);
		const failedProducts = getProductsByStatus('failed', palletId);
		expect(successProducts.length).toBe(2);
		expect(failedProducts.length).toBe(1);
		expect(failedProducts[0].qaid).toBe('fail-qaid-1');
		expect(failedProducts[0].errorMessage).toBeDefined();
	});

	it('실패한 상품을 재시도할 수 있어야 함', async () => {
		// 테스트 데이터 준비
		const palletId = 'test-pallet-3';
		await addProduct('test-qaid-6', { name: '테스트 상품 6' }, palletId);
		await addProduct('fail-qaid-2', { name: '실패 테스트 상품 2' }, palletId);

		// 일괄 전송 실행
		await submitAllProducts(palletId);

		// 실패한 상품의 QAID를 성공하는 값으로 변경 (재시도 성공 시나리오)
		const failedProducts = getProductsByStatus('failed', palletId);
		expect(failedProducts.length).toBe(1);

		// 실패한 상품 ID 가져오기
		const failedProductId = failedProducts[0].id;

		// 모킹된 API 응답 변경을 위해 QAID 변경
		// updateProductStatus만 호출하면 상태만 변경되고 QAID는 변경되지 않음
		// 따라서 updateProduct를 사용하여 QAID를 변경해야 함
		await updateProduct(failedProductId, { qaid: 'success-qaid', status: 'pending' });


		// 재시도 실행
		const retryResult = await retryFailedProducts(palletId);

		// 결과 검증
		expect(retryResult.success).toBe(true);
		expect(retryResult.failedItems.length).toBe(0);
	});

	it('개별 상품을 전송할 수 있어야 함', async () => {
		// 테스트 데이터 준비
		const palletId = 'test-pallet-4';
		await addProduct('test-qaid-7', { name: '테스트 상품 7' }, palletId);

		// 상품 ID 가져오기
		const products = getProductsByStatus('pending', palletId);
		expect(products.length).toBe(1);
		const productId = products[0].id;

		// 개별 전송 실행
		const result = await submitSingleProduct(productId);

		// 결과 검증
		expect(result.success).toBe(true);

		// 상품 상태 검증
		const successProducts = getProductsByStatus('success', palletId);
		expect(successProducts.length).toBe(1);
	});

	it('상품 상태를 업데이트할 수 있어야 함', async () => {
		// 테스트 데이터 준비
		const palletId = 'test-pallet-5';
		await addProduct('test-qaid-8', { name: '테스트 상품 8' }, palletId);

		// 상품 ID 가져오기
		const products = getProductsByStatus('pending', palletId);
		expect(products.length).toBe(1);
		const productId = products[0].id;

		// 상태 업데이트 실행
		const result = updateProductStatus(productId, 'submitting');
		expect(result).toBe(true);

		// 상태 검증
		const submittingProducts = getProductsByStatus('submitting', palletId);
		expect(submittingProducts.length).toBe(1);

		// 실패 상태로 업데이트
		updateProductStatus(productId, 'failed', '테스트 오류');

		// 상태 검증
		const failedProducts = getProductsByStatus('failed', palletId);
		expect(failedProducts.length).toBe(1);
		expect(failedProducts[0].errorMessage).toBe('테스트 오류');
	});
});
