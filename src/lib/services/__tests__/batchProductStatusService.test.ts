/**
 * 배치 상품 상태 관리 서비스 테스트
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import * as batchProductStatusService from '../batchProductStatusService';
import * as batchProductService from '../batchProductService';
import * as batchStorageUtils from '../../utils/batchStorageUtils';
import type { BatchStorageState, BatchProductData } from '../../types/batchTypes';

// 모의 로컬스토리지 구현
const mockLocalStorage = (() => {
	let store: Record<string, string> = {};
	return {
		getItem: (key: string) => store[key] || null,
		setItem: (key: string, value: string) => {
			store[key] = value;
		},
		removeItem: (key: string) => {
			delete store[key];
		},
		clear: () => {
			store = {};
		},
		getAllKeys: () => Object.keys(store)
	};
})();

describe('배치 상품 상태 관리 서비스', () => {
	// 테스트 전 설정
	beforeEach(() => {
		// 로컬스토리지 모의 구현
		Object.defineProperty(window, 'localStorage', {
			value: mockLocalStorage,
			writable: true
		});

		// 초기 상태 설정
		const initialState: BatchStorageState = {
			pallets: {
				'pallet-1': {
					products: [
						{
							id: 'product-1',
							qaid: 'QAID-001',
							timestamp: Date.now(),
							status: 'pending',
							palletId: 'pallet-1',
							productInfo: { name: '테스트 상품 1' }
						},
						{
							id: 'product-2',
							qaid: 'QAID-002',
							timestamp: Date.now(),
							status: 'success',
							palletId: 'pallet-1',
							productInfo: { name: '테스트 상품 2' }
						},
						{
							id: 'product-3',
							qaid: 'QAID-003',
							timestamp: Date.now(),
							status: 'failed',
							errorMessage: '테스트 오류',
							palletId: 'pallet-1',
							productInfo: { name: '테스트 상품 3' }
						}
					],
					palletInfo: { name: '테스트 팔레트 1' }
				},
				'pallet-2': {
					products: [
						{
							id: 'product-4',
							qaid: 'QAID-004',
							timestamp: Date.now(),
							status: 'pending',
							palletId: 'pallet-2',
							productInfo: { name: '테스트 상품 4' }
						}
					],
					palletInfo: { name: '테스트 팔레트 2' }
				}
			},
			totalCount: 4,
			pendingCount: 2,
			successCount: 1,
			failedCount: 1,
			lastUpdated: Date.now(),
			currentPalletId: 'pallet-1'
		};

		// getBatchState 모의 구현
		vi.spyOn(batchStorageUtils, 'getBatchState').mockImplementation(() => {
			return JSON.parse(JSON.stringify(initialState));
		});

		// saveBatchState 모의 구현
		vi.spyOn(batchStorageUtils, 'saveBatchState').mockImplementation((state) => {
			// 상태 저장 로직 모의
		});

		// getProductById 모의 구현
		vi.spyOn(batchProductService, 'getProductById').mockImplementation((productId) => {
			for (const palletId in initialState.pallets) {
				const product = initialState.pallets[palletId].products.find((p) => p.id === productId);
				if (product) {
					return JSON.parse(JSON.stringify(product));
				}
			}
			return null;
		});

		// getAllProducts 모의 구현
		vi.spyOn(batchProductService, 'getAllProducts').mockImplementation((filter) => {
			let allProducts: BatchProductData[] = [];

			// 모든 팔레트의 상품을 하나의 배열로 합치기
			for (const palletId in initialState.pallets) {
				// 팔레트 ID 필터링
				if (filter?.palletId && palletId !== filter.palletId) {
					continue;
				}

				allProducts = allProducts.concat(initialState.pallets[palletId].products);
			}

			// 상태 필터링
			if (filter?.status && filter.status !== 'all') {
				allProducts = allProducts.filter((p) => p.status === filter.status);
			}

			// 검색어 필터링
			if (filter?.searchText) {
				const searchLower = filter.searchText.toLowerCase();
				allProducts = allProducts.filter(
					(p) =>
						p.qaid.toLowerCase().includes(searchLower) ||
						(p.productInfo && JSON.stringify(p.productInfo).toLowerCase().includes(searchLower))
				);
			}

			return allProducts;
		});

		// 이벤트 리스너 모의 구현
		vi.spyOn(window, 'addEventListener').mockImplementation(() => {});
		vi.spyOn(window, 'removeEventListener').mockImplementation(() => {});
		vi.spyOn(window, 'dispatchEvent').mockImplementation(() => true);
	});

	// 테스트 후 정리
	afterEach(() => {
		vi.restoreAllMocks();
		mockLocalStorage.clear();
	});

	// 테스트 케이스
	describe('상품 상태 변경 기능', () => {
		it('ID로 상품 상태를 변경할 수 있어야 함', async () => {
			const result = await batchProductStatusService.updateProductStatus('product-1', 'success');
			expect(result).toBe(true);
			expect(batchStorageUtils.saveBatchState).toHaveBeenCalled();
		});

		it('QAID로 상품 상태를 변경할 수 있어야 함', async () => {
			const result = await batchProductStatusService.updateProductStatusByQaid(
				'QAID-001',
				'success'
			);
			expect(result).toBe(true);
			expect(batchStorageUtils.saveBatchState).toHaveBeenCalled();
		});

		it('실패 상태로 변경 시 오류 메시지를 설정할 수 있어야 함', async () => {
			const result = await batchProductStatusService.updateProductStatus(
				'product-1',
				'failed',
				'테스트 오류 메시지'
			);
			expect(result).toBe(true);
			expect(batchStorageUtils.saveBatchState).toHaveBeenCalled();
		});

		it('존재하지 않는 ID로 상태 변경 시 실패해야 함', async () => {
			const result = await batchProductStatusService.updateProductStatus(
				'non-existent-id',
				'success'
			);
			expect(result).toBe(false);
		});

		it('존재하지 않는 QAID로 상태 변경 시 실패해야 함', async () => {
			const result = await batchProductStatusService.updateProductStatusByQaid(
				'non-existent-qaid',
				'success'
			);
			expect(result).toBe(false);
		});
	});

	describe('여러 상품 상태 일괄 변경 기능', () => {
		it('여러 상품의 상태를 일괄 변경할 수 있어야 함', async () => {
			const result = await batchProductStatusService.updateMultipleProductStatus(
				['product-1', 'product-2'],
				'success'
			);
			expect(result).toBe(true);
			expect(batchStorageUtils.saveBatchState).toHaveBeenCalled();
		});

		it('실패 상태로 일괄 변경 시 각 상품별 오류 메시지를 설정할 수 있어야 함', async () => {
			const result = await batchProductStatusService.updateMultipleProductStatus(
				['product-1', 'product-2'],
				'failed',
				{
					'product-1': '테스트 오류 1',
					'product-2': '테스트 오류 2'
				}
			);
			expect(result).toBe(true);
			expect(batchStorageUtils.saveBatchState).toHaveBeenCalled();
		});

		it('존재하지 않는 ID만 포함된 경우 실패해야 함', async () => {
			const result = await batchProductStatusService.updateMultipleProductStatus(
				['non-existent-id-1', 'non-existent-id-2'],
				'success'
			);
			expect(result).toBe(false);
		});
	});

	describe('팔레트 내 모든 상품 상태 변경 기능', () => {
		it('팔레트 내 모든 상품의 상태를 변경할 수 있어야 함', async () => {
			const result = await batchProductStatusService.updateAllProductStatusInPallet(
				'pallet-1',
				'success'
			);
			expect(result).toBe(true);
			expect(batchStorageUtils.saveBatchState).toHaveBeenCalled();
		});

		it('특정 상태의 상품만 필터링하여 변경할 수 있어야 함', async () => {
			const result = await batchProductStatusService.updateAllProductStatusInPallet(
				'pallet-1',
				'success',
				{ currentStatus: 'pending' }
			);
			expect(result).toBe(true);
			expect(batchStorageUtils.saveBatchState).toHaveBeenCalled();
		});

		it('존재하지 않는 팔레트 ID로 상태 변경 시 실패해야 함', async () => {
			const result = await batchProductStatusService.updateAllProductStatusInPallet(
				'non-existent-pallet',
				'success'
			);
			expect(result).toBe(false);
		});
	});

	describe('모든 상품 상태 변경 기능', () => {
		it('모든 상품의 상태를 변경할 수 있어야 함', async () => {
			const result = await batchProductStatusService.updateAllProductStatus('success');
			expect(result).toBe(true);
			expect(batchStorageUtils.saveBatchState).toHaveBeenCalled();
		});

		it('특정 상태의 상품만 필터링하여 변경할 수 있어야 함', async () => {
			const result = await batchProductStatusService.updateAllProductStatus('success', {
				currentStatus: 'pending'
			});
			expect(result).toBe(true);
			expect(batchStorageUtils.saveBatchState).toHaveBeenCalled();
		});
	});

	describe('상태별 상품 조회 기능', () => {
		it('특정 상태의 상품을 조회할 수 있어야 함', () => {
			const pendingProducts = batchProductStatusService.getProductsByStatusExtended('pending');
			expect(pendingProducts.length).toBeGreaterThan(0);
			expect(pendingProducts.every((p) => p.status === 'pending')).toBe(true);

			const successProducts = batchProductStatusService.getProductsByStatusExtended('success');
			expect(successProducts.length).toBeGreaterThan(0);
			expect(successProducts.every((p) => p.status === 'success')).toBe(true);

			const failedProducts = batchProductStatusService.getProductsByStatusExtended('failed');
			expect(failedProducts.length).toBeGreaterThan(0);
			expect(failedProducts.every((p) => p.status === 'failed')).toBe(true);
		});

		it('모든 상태의 상품을 조회할 수 있어야 함', () => {
			const allProducts = batchProductStatusService.getProductsByStatusExtended('all');
			expect(allProducts.length).toBe(4);
		});

		it('팔레트 ID로 필터링하여 조회할 수 있어야 함', () => {
			const pallet1Products = batchProductStatusService.getProductsByStatusExtended('all', {
				palletId: 'pallet-1'
			});
			expect(pallet1Products.length).toBe(3);

			const pallet2Products = batchProductStatusService.getProductsByStatusExtended('all', {
				palletId: 'pallet-2'
			});
			expect(pallet2Products.length).toBe(1);
		});

		it('정렬 옵션을 적용하여 조회할 수 있어야 함', () => {
			const sortedByQaidAsc = batchProductStatusService.getProductsByStatusExtended('all', {
				sortBy: 'qaid',
				sortOrder: 'asc'
			});
			expect(sortedByQaidAsc[0].qaid).toBe('QAID-001');

			const sortedByQaidDesc = batchProductStatusService.getProductsByStatusExtended('all', {
				sortBy: 'qaid',
				sortOrder: 'desc'
			});
			expect(sortedByQaidDesc[0].qaid).toBe('QAID-004');
		});

		it('페이지네이션 옵션을 적용하여 조회할 수 있어야 함', () => {
			const page1 = batchProductStatusService.getProductsByStatusExtended('all', {
				limit: 2,
				offset: 0
			});
			expect(page1.length).toBe(2);

			const page2 = batchProductStatusService.getProductsByStatusExtended('all', {
				limit: 2,
				offset: 2
			});
			expect(page2.length).toBe(2);
		});
	});

	describe('상태 변경 이벤트 기능', () => {
		it('상태 변경 이벤트 리스너를 등록할 수 있어야 함', () => {
			const removeListener = batchProductStatusService.addStatusChangeListener(() => {});
			expect(typeof removeListener).toBe('function');
			expect(window.addEventListener).toHaveBeenCalled();
		});

		it('상태 변경 이벤트를 발생시킬 수 있어야 함', () => {
			batchProductStatusService.dispatchStatusChangeEvent('product-1', 'pending', 'success');
			expect(window.dispatchEvent).toHaveBeenCalled();
		});
	});

	describe('상태별 상품 통계 기능', () => {
		it('전체 상품의 상태별 통계를 조회할 수 있어야 함', () => {
			const stats = batchProductStatusService.getProductStatusStats();
			expect(stats.counts.total).toBe(4);
			expect(stats.counts.pending).toBe(2);
			expect(stats.counts.success).toBe(1);
			expect(stats.counts.failed).toBe(1);
			expect(stats.percentages.pending).toBe(50);
			expect(stats.percentages.success).toBe(25);
			expect(stats.percentages.failed).toBe(25);
		});

		it('팔레트별 상품의 상태별 통계를 조회할 수 있어야 함', () => {
			const pallet1Stats = batchProductStatusService.getProductStatusStats('pallet-1');
			expect(pallet1Stats.counts.total).toBe(3);
			expect(pallet1Stats.counts.pending).toBe(1);
			expect(pallet1Stats.counts.success).toBe(1);
			expect(pallet1Stats.counts.failed).toBe(1);

			const pallet2Stats = batchProductStatusService.getProductStatusStats('pallet-2');
			expect(pallet2Stats.counts.total).toBe(1);
			expect(pallet2Stats.counts.pending).toBe(1);
			expect(pallet2Stats.counts.success).toBe(0);
			expect(pallet2Stats.counts.failed).toBe(0);
		});

		it('존재하지 않는 팔레트 ID로 통계 조회 시 0을 반환해야 함', () => {
			const nonExistentPalletStats =
				batchProductStatusService.getProductStatusStats('non-existent-pallet');
			expect(nonExistentPalletStats.counts.total).toBe(0);
			expect(nonExistentPalletStats.percentages.pending).toBe(0);
		});
	});

	describe('실패한 상품 그룹화 기능', () => {
		it('실패한 상품을 오류 메시지별로 그룹화할 수 있어야 함', () => {
			const groups = batchProductStatusService.groupFailedProductsByError();
			expect(Object.keys(groups).length).toBeGreaterThan(0);
			expect(groups['테스트 오류']).toBeDefined();
			expect(groups['테스트 오류'].length).toBeGreaterThan(0);
		});

		it('팔레트별로 실패한 상품을 그룹화할 수 있어야 함', () => {
			const pallet1Groups = batchProductStatusService.groupFailedProductsByError('pallet-1');
			expect(Object.keys(pallet1Groups).length).toBeGreaterThan(0);

			const pallet2Groups = batchProductStatusService.groupFailedProductsByError('pallet-2');
			expect(Object.keys(pallet2Groups).length).toBe(0);
		});
	});

	describe('상태 변경 히스토리 기능', () => {
		it('상태 변경 히스토리를 추적할 수 있어야 함', () => {
			// localStorage.setItem에 대한 스파이 설정
			const setItemSpy = vi.spyOn(localStorage, 'setItem');

			batchProductStatusService.trackStatusChange('product-1', 'pending', 'success', '테스트 이유');
			expect(setItemSpy).toHaveBeenCalled();

			// 스파이 복원
			setItemSpy.mockRestore();
		});

		it('상태 변경 히스토리를 조회할 수 있어야 함', () => {
			// 먼저 히스토리 추가
			batchProductStatusService.trackStatusChange('product-1', 'pending', 'success', '테스트 이유');

			// 히스토리 조회 모의 구현
			vi.spyOn(mockLocalStorage, 'getItem').mockImplementation((key) => {
				if (key === 'batch_pallet_status_history') {
					return JSON.stringify([
						{
							productId: 'product-1',
							oldStatus: 'pending',
							newStatus: 'success',
							timestamp: Date.now(),
							reason: '테스트 이유'
						}
					]);
				}
				return null;
			});

			const history = batchProductStatusService.getStatusChangeHistory();
			expect(history.length).toBeGreaterThan(0);
			expect(history[0].productId).toBe('product-1');
			expect(history[0].oldStatus).toBe('pending');
			expect(history[0].newStatus).toBe('success');
			expect(history[0].reason).toBe('테스트 이유');
		});

		it('특정 상품의 히스토리만 조회할 수 있어야 함', () => {
			// 히스토리 조회 모의 구현
			vi.spyOn(mockLocalStorage, 'getItem').mockImplementation((key) => {
				if (key === 'batch_pallet_status_history') {
					return JSON.stringify([
						{
							productId: 'product-1',
							oldStatus: 'pending',
							newStatus: 'success',
							timestamp: Date.now(),
							reason: '테스트 이유 1'
						},
						{
							productId: 'product-2',
							oldStatus: 'pending',
							newStatus: 'failed',
							timestamp: Date.now(),
							reason: '테스트 이유 2'
						}
					]);
				}
				return null;
			});

			const product1History = batchProductStatusService.getStatusChangeHistory('product-1');
			expect(product1History.length).toBe(1);
			expect(product1History[0].productId).toBe('product-1');

			const product2History = batchProductStatusService.getStatusChangeHistory('product-2');
			expect(product2History.length).toBe(1);
			expect(product2History[0].productId).toBe('product-2');
		});
	});

	describe('상태 변경 알림 기능', () => {
		it('상태 변경 알림 설정을 등록할 수 있어야 함', () => {
			const removeNotifications = batchProductStatusService.setupStatusChangeNotifications();
			expect(typeof removeNotifications).toBe('function');
			expect(window.addEventListener).toHaveBeenCalled();
		});

		it('알림 옵션을 지정할 수 있어야 함', () => {
			const removeNotifications = batchProductStatusService.setupStatusChangeNotifications({
				showSuccess: true,
				showFailure: false,
				showPending: true
			});
			expect(typeof removeNotifications).toBe('function');
			expect(window.addEventListener).toHaveBeenCalled();
		});
	});
});
