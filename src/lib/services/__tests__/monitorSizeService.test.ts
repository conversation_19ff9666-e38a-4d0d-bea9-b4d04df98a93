import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

vi.mock('../AxiosBackend', () => {
  return {
    authClient: {
      get: vi.fn(),
      put: vi.fn()
    }
  };
});

const { authClient } = await import('../AxiosBackend');
const { fetchMonitorSizes, updateMonitorSizeItem } = await import('../monitorSizeService');

describe('monitorSizeService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });
  afterEach(() => vi.restoreAllMocks());

  it('fetchMonitorSizes: 성공 응답을 파싱한다', async () => {
    (authClient.get as any).mockResolvedValue({
      data: {
        success: true,
        message: 'Success',
        data: {
          items: [
            { id: 1, name: '삼성 32인치', brand: 'brand', size: 32, unit: 'INCH', name_hash: 'x', created_at: '', updated_at: '' }
          ],
          pagination: { total: 1, per_page: 20, current_page: 1, last_page: 1 }
        }
      }
    });

    const res = await fetchMonitorSizes({ brand: 'brand', unit: 'INCH', min_size: 27, max_size: 32, pageSize: 20 });
    expect(res.items.length).toBe(1);
    expect(res.pagination.total).toBe(1);
  });

  it('updateMonitorSizeItem: 성공 응답을 파싱한다', async () => {
    (authClient.put as any).mockResolvedValue({
      data: {
        success: true,
        message: '모니터 사이즈가 수정되었습니다.',
        data: {
          item: { id: 1, name: '삼성 32인치', brand: 'brand', size: 32.5, unit: 'INCH', name_hash: 'x', created_at: '', updated_at: '' }
        }
      }
    });

    const item = await updateMonitorSizeItem(1, { brand: 'brand', size: 32.5, unit: 'INCH' });
    expect(item.size).toBe(32.5);
  });
});


