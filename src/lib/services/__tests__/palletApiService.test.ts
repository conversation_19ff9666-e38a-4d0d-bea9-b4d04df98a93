/**
 * 팔레트 API 서비스 단위 테스트
 *
 * 이 파일은 palletApiService.ts의 모든 함수에 대한 단위 테스트를 구현합니다.
 * API 호출, 응답 처리, 오류 처리 등의 기능을 테스트합니다.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import type { BatchProductData } from '../../types/batchTypes';

// authClient 모킹
const mockPost = vi.fn();
const mockGet = vi.fn();

vi.mock('../AxiosBackend', () => ({
	authClient: {
		post: mockPost,
		get: mockGet
	}
}));

// 모킹된 모듈 가져오기
const { saveProducts, saveProduct, getPalletInfo } = await import('../palletApiService');

describe('팔레트 API 서비스', () => {
	beforeEach(() => {
		vi.clearAllMocks();
	});

	afterEach(() => {
		vi.restoreAllMocks();
	});

	describe('saveProducts', () => {
		const testProducts: BatchProductData[] = [
			{
				id: 'product-1',
				qaid: 'QA001',
				status: 'pending',
				timestamp: Date.now(),
				palletId: 'pallet-1',
				productInfo: { name: '테스트 상품 1' }
			},
			{
				id: 'product-2',
				qaid: 'QA002',
				status: 'pending',
				timestamp: Date.now(),
				palletId: 'pallet-1',
				productInfo: { name: '테스트 상품 2' }
			}
		];

		it('모든 상품이 성공적으로 저장되는 경우를 처리할 수 있어야 함', async () => {
			// 성공 응답 모킹
			mockPost.mockResolvedValue({
				data: {
					message: '모든 상품이 성공적으로 저장되었습니다.',
					results: [
						{ qaid: 'QA001', success: true },
						{ qaid: 'QA002', success: true }
					]
				}
			});

			const result = await saveProducts(testProducts);

			expect(result.success).toBe(true);
			expect(result.successIds).toHaveLength(2);
			expect(result.failedItems).toHaveLength(0);
			expect(result.message).toContain('성공적으로 저장');

			// API 호출 확인
			expect(mockPost).toHaveBeenCalledWith('/api/pallets/products/batch', {
				products: [
					{
						qaid: 'QA001',
						palletId: 'pallet-1',
						productInfo: { name: '테스트 상품 1' }
					},
					{
						qaid: 'QA002',
						palletId: 'pallet-1',
						productInfo: { name: '테스트 상품 2' }
					}
				]
			});
		});

		it('일부 상품이 실패하는 경우를 처리할 수 있어야 함', async () => {
			// 부분 실패 응답 모킹
			mockPost.mockResolvedValue({
				data: {
					message: '일부 상품 저장 실패',
					results: [
						{ qaid: 'QA001', success: true },
						{ qaid: 'QA002', success: false, error: '중복된 QAID' }
					]
				}
			});

			const result = await saveProducts(testProducts);

			expect(result.success).toBe(false);
			expect(result.successIds).toHaveLength(1);
			expect(result.failedItems).toHaveLength(1);
			expect(result.failedItems[0].qaid).toBe('QA002');
			expect(result.failedItems[0].error).toBe('중복된 QAID');
			expect(result.message).toBe('일부 상품 저장 실패');
		});

		it('네트워크 오류를 처리할 수 있어야 함', async () => {
			// 네트워크 오류 모킹
			mockPost.mockRejectedValue(new Error('네트워크 연결 실패'));

			const result = await saveProducts(testProducts);

			expect(result.success).toBe(false);
			expect(result.successIds).toHaveLength(0);
			expect(result.failedItems).toHaveLength(2);
			expect(result.message).toContain('서버 통신 오류');
			expect(result.failedItems.every((item) => item.error === '네트워크 연결 실패')).toBe(true);
		});
	});

	describe('saveProduct', () => {
		const testProduct: BatchProductData = {
			id: 'product-1',
			qaid: 'QA001',
			status: 'pending',
			timestamp: Date.now(),
			palletId: 'pallet-1',
			productInfo: { name: '테스트 상품' }
		};

		it('상품을 성공적으로 저장할 수 있어야 함', async () => {
			// 성공 응답 모킹
			mockPost.mockResolvedValue({
				data: {
					message: '상품이 성공적으로 저장되었습니다.'
				}
			});

			const result = await saveProduct(testProduct);

			expect(result.success).toBe(true);
			expect(result.message).toBe('상품이 성공적으로 저장되었습니다.');
			expect(result.error).toBeUndefined();

			// API 호출 확인
			expect(mockPost).toHaveBeenCalledWith('/api/pallets/products', {
				qaid: 'QA001',
				palletId: 'pallet-1',
				productInfo: { name: '테스트 상품' }
			});
		});

		it('상품 저장 실패를 처리할 수 있어야 함', async () => {
			// 실패 응답 모킹
			mockPost.mockRejectedValue(new Error('중복된 QAID'));

			const result = await saveProduct(testProduct);

			expect(result.success).toBe(false);
			expect(result.message).toBe('상품 저장 실패');
			expect(result.error).toBe('중복된 QAID');
		});
	});

	describe('getPalletInfo', () => {
		const testPalletId = 'pallet-1';

		it('팔레트 정보를 성공적으로 조회할 수 있어야 함', async () => {
			const mockPalletData = {
				id: 'pallet-1',
				name: '테스트 팔레트',
				status: 'active',
				products: []
			};

			// 성공 응답 모킹
			mockGet.mockResolvedValue({
				data: mockPalletData
			});

			const result = await getPalletInfo(testPalletId);

			expect(result.success).toBe(true);
			expect(result.data).toEqual(mockPalletData);
			expect(result.error).toBeUndefined();

			// API 호출 확인
			expect(mockGet).toHaveBeenCalledWith('/api/pallets/pallet-1');
		});

		it('팔레트 정보 조회 실패를 처리할 수 있어야 함', async () => {
			// 실패 응답 모킹
			mockGet.mockRejectedValue(new Error('팔레트를 찾을 수 없습니다'));

			const result = await getPalletInfo(testPalletId);

			expect(result.success).toBe(false);
			expect(result.data).toBeUndefined();
			expect(result.error).toBe('팔레트를 찾을 수 없습니다');
		});
	});
});
