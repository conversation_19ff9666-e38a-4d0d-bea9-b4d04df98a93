/**
 * 배치 상품 관리 서비스
 *
 * 이 파일은 배치 상품 데이터의 CRUD 작업과 관련된 함수들을 제공합니다.
 * 로컬스토리지에 저장된 상품 데이터를 추가, 수정, 삭제, 조회하는 기능을 구현합니다.
 */

import { v4 as uuidv4 } from 'uuid';
import type { BatchProductData, BatchProductFilter, BatchSaveResult } from '../types/batchTypes';
import { getBatchState, saveBatchState } from '../utils/batchStorageUtils';

/**
 * 상품 추가
 * QAID 바코드를 기반으로 새 상품을 로컬스토리지에 추가합니다.
 * @param qaid QAID 바코드 값
 * @param productInfo 상품 정보 (선택 사항)
 * @param palletId 팔레트 ID (선택 사항, 기본값: 현재 선택된 팔레트)
 * @returns 성공 여부를 담은 Promise
 */
export async function addProduct(
	qaid: string,
	productInfo?: any,
	palletId?: string
): Promise<boolean> {
	try {
		// 현재 상태 가져오기
		const state = getBatchState();

		// 팔레트 ID가 제공되지 않은 경우 현재 선택된 팔레트 사용
		const targetPalletId = palletId || state.currentPalletId;

		// 팔레트 ID가 없는 경우 오류 반환
		if (!targetPalletId) {
			console.error('상품을 추가할 팔레트 ID가 지정되지 않았습니다.');
			return false;
		}

		// 해당 팔레트가 존재하지 않으면 생성
		if (!state.pallets[targetPalletId]) {
			state.pallets[targetPalletId] = {
				products: [],
				palletInfo: {}
			};
		}

		// 이미 동일한 QAID를 가진 상품이 있는지 확인
		const existingProductIndex = state.pallets[targetPalletId].products.findIndex(
			(p) => p.qaid === qaid
		);

		// 새 상품 데이터 생성
		const newProduct: BatchProductData = {
			id: uuidv4(), // 고유 ID 생성
			qaid,
			timestamp: Date.now(),
			status: 'pending',
			palletId: targetPalletId,
			productInfo: productInfo || {}
		};

		// 이미 존재하는 경우 업데이트, 아니면 추가
		if (existingProductIndex >= 0) {
			state.pallets[targetPalletId].products[existingProductIndex] = newProduct;
		} else {
			state.pallets[targetPalletId].products.push(newProduct);
		}

		// 상태 저장
		saveBatchState(state);
		return true;
	} catch (error) {
		console.error('상품 추가 중 오류 발생:', error);
		return false;
	}
}

/**
 * 상품 업데이트
 * 상품 ID를 기반으로 상품 정보를 업데이트합니다.
 * @param productId 상품 ID
 * @param updates 업데이트할 속성들
 * @returns 성공 여부를 담은 Promise
 */
export async function updateProduct(
	productId: string,
	updates: Partial<BatchProductData>
): Promise<boolean> {
	try {
		// 현재 상태 가져오기
		const state = getBatchState();

		// 모든 팔레트를 순회하며 상품 찾기
		let found = false;
		for (const palletId in state.pallets) {
			const productIndex = state.pallets[palletId].products.findIndex((p) => p.id === productId);
			if (productIndex >= 0) {
				// 상품 찾음, 업데이트 적용
				state.pallets[palletId].products[productIndex] = {
					...state.pallets[palletId].products[productIndex],
					...updates,
					// ID와 QAID는 변경 불가
					id: productId,
					qaid: state.pallets[palletId].products[productIndex].qaid,
					// 타임스탬프 업데이트
					timestamp: Date.now()
				};
				found = true;
				break;
			}
		}

		if (!found) {
			console.error(`ID가 ${productId}인 상품을 찾을 수 없습니다.`);
			return false;
		}

		// 상태 저장
		saveBatchState(state);
		return true;
	} catch (error) {
		console.error('상품 업데이트 중 오류 발생:', error);
		return false;
	}
}

/**
 * QAID로 상품 업데이트
 * QAID를 기반으로 상품 정보를 업데이트합니다.
 * @param qaid QAID 바코드 값
 * @param updates 업데이트할 속성들
 * @returns 성공 여부를 담은 Promise
 */
export async function updateProductByQaid(
	qaid: string,
	updates: Partial<BatchProductData>
): Promise<boolean> {
	try {
		// 현재 상태 가져오기
		const state = getBatchState();

		// 모든 팔레트를 순회하며 상품 찾기
		let found = false;
		for (const palletId in state.pallets) {
			const productIndex = state.pallets[palletId].products.findIndex((p) => p.qaid === qaid);
			if (productIndex >= 0) {
				// 상품 찾음, 업데이트 적용
				state.pallets[palletId].products[productIndex] = {
					...state.pallets[palletId].products[productIndex],
					...updates,
					// ID와 QAID는 변경 불가
					id: state.pallets[palletId].products[productIndex].id,
					qaid: qaid,
					// 타임스탬프 업데이트
					timestamp: Date.now()
				};
				found = true;
				break;
			}
		}

		if (!found) {
			console.error(`QAID가 ${qaid}인 상품을 찾을 수 없습니다.`);
			return false;
		}

		// 상태 저장
		saveBatchState(state);
		return true;
	} catch (error) {
		console.error('상품 업데이트 중 오류 발생:', error);
		return false;
	}
}

/**
 * 상품 삭제
 * 상품 ID를 기반으로 상품을 삭제합니다.
 * @param productId 상품 ID
 * @returns 성공 여부를 담은 Promise
 */
export async function removeProduct(productId: string): Promise<boolean> {
	try {
		// 현재 상태 가져오기
		const state = getBatchState();

		// 모든 팔레트를 순회하며 상품 찾기
		let found = false;
		for (const palletId in state.pallets) {
			const originalLength = state.pallets[palletId].products.length;
			state.pallets[palletId].products = state.pallets[palletId].products.filter(
				(p) => p.id !== productId
			);

			// 상품이 제거되었는지 확인
			if (state.pallets[palletId].products.length < originalLength) {
				found = true;
				break;
			}
		}

		if (!found) {
			console.error(`ID가 ${productId}인 상품을 찾을 수 없습니다.`);
			return false;
		}

		// 상태 저장
		saveBatchState(state);
		return true;
	} catch (error) {
		console.error('상품 삭제 중 오류 발생:', error);
		return false;
	}
}

/**
 * QAID로 상품 삭제
 * QAID를 기반으로 상품을 삭제합니다.
 * @param qaid QAID 바코드 값
 * @returns 성공 여부를 담은 Promise
 */
export async function removeProductByQaid(qaid: string): Promise<boolean> {
	try {
		// 현재 상태 가져오기
		const state = getBatchState();

		// 모든 팔레트를 순회하며 상품 찾기
		let found = false;
		for (const palletId in state.pallets) {
			const originalLength = state.pallets[palletId].products.length;
			state.pallets[palletId].products = state.pallets[palletId].products.filter(
				(p) => p.qaid !== qaid
			);

			// 상품이 제거되었는지 확인
			if (state.pallets[palletId].products.length < originalLength) {
				found = true;
				break;
			}
		}

		if (!found) {
			console.error(`QAID가 ${qaid}인 상품을 찾을 수 없습니다.`);
			return false;
		}

		// 상태 저장
		saveBatchState(state);
		return true;
	} catch (error) {
		console.error('상품 삭제 중 오류 발생:', error);
		return false;
	}
}

/**
 * 모든 상품 조회
 * 로컬스토리지에 저장된 모든 상품을 조회합니다.
 * @param filter 필터 옵션 (선택 사항)
 * @returns 상품 목록
 */
export function getAllProducts(filter?: BatchProductFilter): BatchProductData[] {
	try {
		// 현재 상태 가져오기
		const state = getBatchState();
		let allProducts: BatchProductData[] = [];

		// 모든 팔레트의 상품을 하나의 배열로 합치기
		for (const palletId in state.pallets) {
			// 팔레트 ID 필터링
			if (filter?.palletId && palletId !== filter.palletId) {
				continue;
			}

			allProducts = allProducts.concat(state.pallets[palletId].products);
		}

		// 상태 필터링
		if (filter?.status && filter.status !== 'all') {
			allProducts = allProducts.filter((p) => p.status === filter.status);
		}

		// 검색어 필터링
		if (filter?.searchText) {
			const searchLower = filter.searchText.toLowerCase();
			allProducts = allProducts.filter(
				(p) =>
					p.qaid.toLowerCase().includes(searchLower) ||
					(p.productInfo && JSON.stringify(p.productInfo).toLowerCase().includes(searchLower))
			);
		}

		// 최신순으로 정렬 (타임스탬프 기준 내림차순)
		return allProducts.sort((a, b) => b.timestamp - a.timestamp);
	} catch (error) {
		console.error('상품 조회 중 오류 발생:', error);
		return [];
	}
}

/**
 * 상태별 상품 조회
 * 특정 상태의 상품만 조회합니다.
 * @param status 상품 상태 ('pending', 'success', 'failed')
 * @param palletId 팔레트 ID (선택 사항)
 * @returns 상품 목록
 */
export function getProductsByStatus(
	status: 'pending' | 'submitting' | 'success' | 'failed',
	palletId?: string
): BatchProductData[] {
	return getAllProducts({ status, palletId });
}

/**
 * ID로 상품 조회
 * 상품 ID를 기반으로 상품을 조회합니다.
 * @param productId 상품 ID
 * @returns 상품 데이터 또는 null
 */
export function getProductById(productId: string): BatchProductData | null {
	try {
		// 현재 상태 가져오기
		const state = getBatchState();

		// 모든 팔레트를 순회하며 상품 찾기
		for (const palletId in state.pallets) {
			const product = state.pallets[palletId].products.find((p) => p.id === productId);
			if (product) {
				return product;
			}
		}

		return null;
	} catch (error) {
		console.error('상품 조회 중 오류 발생:', error);
		return null;
	}
}

/**
 * QAID로 상품 조회
 * QAID를 기반으로 상품을 조회합니다.
 * @param qaid QAID 바코드 값
 * @returns 상품 데이터 또는 null
 */
export function getProductByQaid(qaid: string): BatchProductData | null {
	try {
		// 현재 상태 가져오기
		const state = getBatchState();

		// 모든 팔레트를 순회하며 상품 찾기
		for (const palletId in state.pallets) {
			const product = state.pallets[palletId].products.find((p) => p.qaid === qaid);
			if (product) {
				return product;
			}
		}

		return null;
	} catch (error) {
		console.error('상품 조회 중 오류 발생:', error);
		return null;
	}
}

/**
 * 상품 수 조회
 * 상태별 상품 수를 조회합니다.
 * @param palletId 팔레트 ID (선택 사항)
 * @returns 상태별 상품 수
 */
export function getProductCount(palletId?: string): {
	total: number;
	pending: number;
	submitting: number;
	success: number;
	failed: number;
} {
	try {
		// 현재 상태 가져오기
		const state = getBatchState();

		// 팔레트 ID가 지정된 경우 해당 팔레트의 상품만 계산
		if (palletId) {
			if (!state.pallets[palletId]) {
				return { total: 0, pending: 0, submitting: 0, success: 0, failed: 0 };
			}

			const products = state.pallets[palletId].products;
			return {
				total: products.length,
				pending: products.filter((p) => p.status === 'pending').length,
				submitting: products.filter((p) => p.status === 'submitting').length,
				success: products.filter((p) => p.status === 'success').length,
				failed: products.filter((p) => p.status === 'failed').length
			};
		}

		// 전체 상품 수 반환
		return {
			total: state.totalCount,
			pending: state.pendingCount,
			submitting: 0, // 현재 스토리지에는 submitting 카운트가 없으므로 0으로 설정
			success: state.successCount,
			failed: state.failedCount
		};
	} catch (error) {
		console.error('상품 수 조회 중 오류 발생:', error);
		return { total: 0, pending: 0, submitting: 0, success: 0, failed: 0 };
	}
}

/**
 * 상품 존재 여부 확인
 * 로컬스토리지에 상품이 있는지 확인합니다.
 * @param palletId 팔레트 ID (선택 사항)
 * @returns 상품 존재 여부
 */
export function hasProducts(palletId?: string): boolean {
	const counts = getProductCount(palletId);
	return counts.total > 0;
}

/**
 * 대기 중인 상품 존재 여부 확인
 * 대기 중인 상품이 있는지 확인합니다.
 * @param palletId 팔레트 ID (선택 사항)
 * @returns 대기 중인 상품 존재 여부
 */
export async function hasPendingProducts(palletId?: string): Promise<boolean> {
	if (palletId) {
		const counts = await getPalletProductCount(palletId);
		return counts.pending > 0;
	} else {
		const counts = getProductCount(palletId);
		return counts.pending > 0;
	}
}

/**
 * 실패한 상품 존재 여부 확인
 * 실패한 상품이 있는지 확인합니다.
 * @param palletId 팔레트 ID (선택 사항)
 * @returns 실패한 상품 존재 여부
 */
export function hasFailedProducts(palletId?: string): boolean {
	const counts = getProductCount(palletId);
	return counts.failed > 0;
}

/**
 * 모든 상품 서버 전송
 * 로컬스토리지의 모든 대기 중인 상품을 서버로 전송합니다.
 * @param palletId 팔레트 ID (선택 사항, 지정 시 해당 팔레트만 전송)
 * @returns 전송 결과를 담은 Promise
 */
export async function submitAllProducts(palletId?: string): Promise<BatchSaveResult> {
	try {
		// 현재 상태 가져오기
		const state = getBatchState();

		// 전송할 팔레트 ID 목록 결정
		const palletIds = palletId ? [palletId] : Object.keys(state.pallets);

		// 전송할 상품 목록 수집
		const productsToSubmit: BatchProductData[] = [];
		palletIds.forEach((pid) => {
			if (state.pallets[pid]) {
				const pendingProducts = state.pallets[pid].products.filter((p) => p.status === 'pending');
				productsToSubmit.push(...pendingProducts);
			}
		});

		// 전송할 상품이 없는 경우
		if (productsToSubmit.length === 0) {
			return {
				success: true,
				message: '전송할 상품이 없습니다.',
				successIds: [],
				failedItems: []
			};
		}

		// 전송 중인 상품 상태 업데이트
		productsToSubmit.forEach((product) => {
			updateProductStatus(product.id, 'submitting');
		});

		// 실제 서버 API 호출
		let result: BatchSaveResult;

		try {
			// palletApiService에서 saveProducts 함수 가져오기
			const { saveProducts } = await import('./palletApiService');
			result = await saveProducts(productsToSubmit);
		} catch (apiError) {
			console.error('API 호출 중 오류 발생:', apiError);

			// API 호출 실패 시 모든 상품을 실패 상태로 표시
			result = {
				success: false,
				message: `서버 통신 오류: ${apiError}`,
				successIds: [],
				failedItems: productsToSubmit.map((product) => ({
					id: product.id,
					qaid: product.qaid,
					error: '서버 연결 실패'
				}))
			};
		}

		// 전송 결과에 따라 상품 상태 업데이트
		if (result) {
			updateProductStatusAfterSubmit(result);
		}

		return result;
	} catch (error) {
		console.error('상품 일괄 전송 중 오류 발생:', error);
		return {
			success: false,
			message: `상품 일괄 전송 중 오류 발생: ${error}`,
			successIds: [],
			failedItems: []
		};
	}
}

/**
 * 실패한 상품 재시도
 * 실패한 상품들을 다시 서버로 전송합니다.
 * @param palletId 팔레트 ID (선택 사항, 지정 시 해당 팔레트만 전송)
 * @param productIds 특정 상품 ID 목록 (선택 사항, 지정 시 해당 상품만 재시도)
 * @returns 전송 결과를 담은 Promise
 */
export async function retryFailedProducts(
	palletId?: string,
	productIds?: string[]
): Promise<BatchSaveResult> {
	try {
		// 현재 상태 가져오기
		const state = getBatchState();

		// 전송할 팔레트 ID 목록 결정
		const palletIds = palletId ? [palletId] : Object.keys(state.pallets);

		// 전송할 상품 목록 수집
		const productsToRetry: BatchProductData[] = [];

		palletIds.forEach((pid) => {
			if (state.pallets[pid]) {
				let failedProducts = state.pallets[pid].products.filter((p) => p.status === 'failed');

				// 특정 상품 ID가 지정된 경우 필터링
				if (productIds && productIds.length > 0) {
					failedProducts = failedProducts.filter((p) => productIds.includes(p.id));
				}

				productsToRetry.push(...failedProducts);
			}
		});

		// 재시도할 상품이 없는 경우
		if (productsToRetry.length === 0) {
			return {
				success: true,
				message: '재시도할 실패 상품이 없습니다.',
				successIds: [],
				failedItems: []
			};
		}

		// 상태를 'pending'으로 변경
		productsToRetry.forEach((product) => {
			updateProduct(product.id, { status: 'pending', errorMessage: undefined });
		});

		// 서버 전송 실행
		return await submitAllProducts(palletId);
	} catch (error) {
		console.error('실패 상품 재시도 중 오류 발생:', error);
		return {
			success: false,
			message: `실패 상품 재시도 중 오류 발생: ${error}`,
			successIds: [],
			failedItems: []
		};
	}
}

/**
 * 모든 상품 초기화
 * 로컬스토리지의 모든 상품을 초기화합니다.
 * @param palletId 팔레트 ID (선택 사항, 지정 시 해당 팔레트만 초기화)
 */
export function clearAllProducts(palletId?: string): void {
	try {
		// 현재 상태 가져오기
		const state = getBatchState();

		// 팔레트 ID가 지정된 경우 해당 팔레트만 초기화
		if (palletId) {
			if (state.pallets[palletId]) {
				state.pallets[palletId].products = [];
			}
		} else {
			// 모든 팔레트 초기화
			for (const pid in state.pallets) {
				state.pallets[pid].products = [];
			}
		}

		// 상태 저장
		saveBatchState(state);
	} catch (error) {
		console.error('상품 초기화 중 오류 발생:', error);
	}
}

/**
 * 성공한 상품 초기화
 * 성공한 상품만 초기화합니다.
 * @param palletId 팔레트 ID (선택 사항, 지정 시 해당 팔레트만 초기화)
 */
export function clearSuccessfulProducts(palletId?: string): void {
	try {
		// 현재 상태 가져오기
		const state = getBatchState();

		// 팔레트 ID가 지정된 경우 해당 팔레트만 처리
		if (palletId) {
			if (state.pallets[palletId]) {
				state.pallets[palletId].products = state.pallets[palletId].products.filter(
					(p) => p.status !== 'success'
				);
			}
		} else {
			// 모든 팔레트 처리
			for (const pid in state.pallets) {
				state.pallets[pid].products = state.pallets[pid].products.filter(
					(p) => p.status !== 'success'
				);
			}
		}

		// 상태 저장
		saveBatchState(state);
	} catch (error) {
		console.error('성공 상품 초기화 중 오류 발생:', error);
	}
}

/**
 * 실패한 상품 상태 초기화
 * 실패한 상품의 상태를 대기 중으로 변경합니다.
 * @param palletId 팔레트 ID (선택 사항, 지정 시 해당 팔레트만 처리)
 */
export function resetFailedProducts(palletId?: string): void {
	try {
		// 현재 상태 가져오기
		const state = getBatchState();

		// 팔레트 ID가 지정된 경우 해당 팔레트만 처리
		if (palletId) {
			if (state.pallets[palletId]) {
				state.pallets[palletId].products.forEach((product) => {
					if (product.status === 'failed') {
						product.status = 'pending';
						product.errorMessage = undefined;
					}
				});
			}
		} else {
			// 모든 팔레트 처리
			for (const pid in state.pallets) {
				state.pallets[pid].products.forEach((product) => {
					if (product.status === 'failed') {
						product.status = 'pending';
						product.errorMessage = undefined;
					}
				});
			}
		}

		// 상태 저장
		saveBatchState(state);
	} catch (error) {
		console.error('실패 상품 상태 초기화 중 오류 발생:', error);
	}
}

/**
 * 서버 전송 후 상품 상태 업데이트
 * 서버 전송 결과에 따라 상품 상태를 업데이트합니다.
 * @param result 서버 전송 결과
 */
function updateProductStatusAfterSubmit(result: BatchSaveResult): void {
	try {
		// result가 유효하지 않으면 함수 종료
		if (!result || !result.successIds || !result.failedItems) {
			console.error('유효하지 않은 결과 객체:', result);
			return;
		}

		// 현재 상태 가져오기
		const state = getBatchState();

		// 성공한 상품 상태 업데이트
		result.successIds.forEach((productId) => {
			for (const palletId in state.pallets) {
				const productIndex = state.pallets[palletId].products.findIndex((p) => p.id === productId);
				if (productIndex >= 0) {
					state.pallets[palletId].products[productIndex].status = 'success';
					state.pallets[palletId].products[productIndex].errorMessage = undefined;
					state.pallets[palletId].products[productIndex].timestamp = Date.now();
				}
			}
		});

		// 실패한 상품 상태 업데이트
		result.failedItems.forEach((item) => {
			for (const palletId in state.pallets) {
				const productIndex = state.pallets[palletId].products.findIndex((p) => p.id === item.id);
				if (productIndex >= 0) {
					state.pallets[palletId].products[productIndex].status = 'failed';
					state.pallets[palletId].products[productIndex].errorMessage = item.error;
					state.pallets[palletId].products[productIndex].timestamp = Date.now();
				}
			}
		});

		// 상태 저장
		saveBatchState(state);

		// 성공 메시지 로깅
		if (result.successIds.length > 0) {
			console.log(`${result.successIds.length}개 상품이 성공적으로 저장되었습니다.`);
		}

		// 실패 메시지 로깅
		if (result.failedItems.length > 0) {
			console.warn(`${result.failedItems.length}개 상품 저장에 실패했습니다.`);
			result.failedItems.forEach((item) => {
				console.warn(`- QAID: ${item.qaid}, 오류: ${item.error}`);
			});
		}
	} catch (error) {
		console.error('상품 상태 업데이트 중 오류 발생:', error);
	}
}

/**
 * 상품 상태 업데이트
 * 상품의 상태를 업데이트합니다.
 * @param productId 상품 ID
 * @param status 변경할 상태 ('pending', 'submitting', 'success', 'failed')
 * @param errorMessage 오류 메시지 (실패 시)
 * @returns 성공 여부
 */
export function updateProductStatus(
	productId: string,
	status: 'pending' | 'submitting' | 'success' | 'failed',
	errorMessage?: string
): boolean {
	try {
		// 현재 상태 가져오기
		const state = getBatchState();

		// 모든 팔레트를 순회하며 상품 찾기
		let found = false;
		for (const palletId in state.pallets) {
			const productIndex = state.pallets[palletId].products.findIndex((p) => p.id === productId);
			if (productIndex >= 0) {
				// 상품 찾음, 상태 업데이트
				state.pallets[palletId].products[productIndex] = {
					...state.pallets[palletId].products[productIndex],
					status,
					errorMessage: status === 'failed' ? errorMessage : undefined,
					timestamp: Date.now()
				};
				found = true;
				break;
			}
		}

		if (!found) {
			console.error(`ID가 ${productId}인 상품을 찾을 수 없습니다.`);
			return false;
		}

		// 상태 저장
		saveBatchState(state);
		return true;
	} catch (error) {
		console.error('상품 상태 업데이트 중 오류 발생:', error);
		return false;
	}
}

/**
 * 개별 상품 서버 전송
 * 단일 상품을 서버로 전송합니다.
 * @param productId 상품 ID
 * @returns 전송 결과를 담은 Promise
 */
export async function submitSingleProduct(productId: string): Promise<{
	success: boolean;
	message: string;
	error?: string;
}> {
	try {
		// 상품 조회
		const product = getProductById(productId);
		if (!product) {
			return {
				success: false,
				message: '상품을 찾을 수 없습니다.',
				error: '상품 ID가 유효하지 않습니다.'
			};
		}

		// 이미 성공한 상품인 경우
		if (product.status === 'success') {
			return {
				success: true,
				message: '이미 저장된 상품입니다.'
			};
		}

		// 전송 중 상태로 업데이트
		updateProductStatus(productId, 'submitting');

		// API 호출
		try {
			const { saveProduct } = await import('./palletApiService');
			const result = await saveProduct(product);

			// 결과에 따라 상태 업데이트
			if (result.success) {
				updateProductStatus(productId, 'success');
			} else {
				updateProductStatus(productId, 'failed', result.error);
			}

			return result;
		} catch (apiError: any) {
			// API 호출 실패
			updateProductStatus(productId, 'failed', apiError.message || '서버 통신 오류');

			return {
				success: false,
				message: '상품 저장 실패',
				error: apiError.message || '서버 통신 오류'
			};
		}
	} catch (error: any) {
		console.error('개별 상품 전송 중 오류 발생:', error);
		return {
			success: false,
			message: '상품 저장 중 오류 발생',
			error: error.message || '알 수 없는 오류'
		};
	}
}

/**
 * 현재 팔레트 ID 설정
 * 현재 작업 중인 팔레트 ID를 설정합니다.
 * @param palletId 설정할 팔레트 ID
 */
export async function setCurrentPalletId(palletId: string): Promise<void> {
	try {
		const { setCurrentPalletId: setCurrentPallet } = await import('../utils/batchStorageUtils');
		setCurrentPallet(palletId);
		console.log('현재 팔레트 ID가 설정되었습니다:', palletId);
	} catch (error) {
		console.error('현재 팔레트 ID 설정 중 오류 발생:', error);
	}
}

/**
 * 현재 팔레트 ID 가져오기
 * 현재 작업 중인 팔레트 ID를 반환합니다.
 * @returns 현재 팔레트 ID
 */
export async function getCurrentPalletId(): Promise<string> {
	try {
		const { getCurrentPalletId: getCurrentPallet } = await import('../utils/batchStorageUtils');
		return getCurrentPallet();
	} catch (error) {
		console.error('현재 팔레트 ID 조회 중 오류 발생:', error);
		return '';
	}
}

/**
 * 팔레트별 상품 목록 가져오기
 * 특정 팔레트의 상품 목록을 반환합니다.
 * @param palletId 팔레트 ID
 * @returns 해당 팔레트의 상품 목록
 */
export async function getPalletProducts(palletId: string): Promise<BatchProductData[]> {
	try {
		const { getPalletProducts: getPalletProductsFromStorage } = await import(
			'../utils/batchStorageUtils'
		);
		return getPalletProductsFromStorage(palletId);
	} catch (error) {
		console.error('팔레트 상품 목록 조회 중 오류 발생:', error);
		return [];
	}
}

/**
 * 팔레트별 상품 수 가져오기
 * 특정 팔레트의 상태별 상품 수를 반환합니다.
 * @param palletId 팔레트 ID
 * @returns 상태별 상품 수
 */
export async function getPalletProductCount(palletId: string): Promise<{
	total: number;
	pending: number;
	submitting: number;
	success: number;
	failed: number;
}> {
	try {
		const { getPalletProductCount: getPalletProductCountFromStorage } = await import(
			'../utils/batchStorageUtils'
		);
		return getPalletProductCountFromStorage(palletId);
	} catch (error) {
		console.error('팔레트 상품 수 조회 중 오류 발생:', error);
		return { total: 0, pending: 0, submitting: 0, success: 0, failed: 0 };
	}
}

/**
 * 팔레트 존재 여부 확인
 * 지정된 팔레트가 로컬스토리지에 존재하는지 확인합니다.
 * @param palletId 팔레트 ID
 * @returns 팔레트 존재 여부
 */
export async function hasPallet(palletId: string): Promise<boolean> {
	try {
		const { hasPallet: hasPalletInStorage } = await import('../utils/batchStorageUtils');
		return hasPalletInStorage(palletId);
	} catch (error) {
		console.error('팔레트 존재 여부 확인 중 오류 발생:', error);
		return false;
	}
}

/**
 * 모든 팔레트 목록 가져오기
 * 로컬스토리지에 저장된 모든 팔레트 목록을 반환합니다.
 * @returns 팔레트 ID와 상품 수 정보
 */
export async function getAllPallets(): Promise<
	Array<{
		palletId: string;
		productCount: number;
		pendingCount: number;
		successCount: number;
		failedCount: number;
		lastUpdated: number;
	}>
> {
	try {
		const { getAllPallets: getAllPalletsFromStorage } = await import('../utils/batchStorageUtils');
		return getAllPalletsFromStorage();
	} catch (error) {
		console.error('팔레트 목록 조회 중 오류 발생:', error);
		return [];
	}
}

/**
 * 팔레트 변경 시 데이터 로드
 * 팔레트 번호가 변경될 때 해당 팔레트의 상품 목록을 로드하고 UI에 표시할 데이터를 반환합니다.
 * @param palletId 변경할 팔레트 ID
 * @returns 팔레트 변경 결과 정보
 */
export async function loadPalletData(palletId: string): Promise<{
	success: boolean;
	message: string;
	products: BatchProductData[];
	counts: {
		total: number;
		pending: number;
		submitting: number;
		success: number;
		failed: number;
	};
	hasExistingData: boolean;
}> {
	try {
		// 현재 팔레트 ID 설정
		await setCurrentPalletId(palletId);

		// 해당 팔레트의 상품 목록 가져오기
		const products = await getPalletProducts(palletId);
		const counts = await getPalletProductCount(palletId);
		const hasExistingData = products.length > 0;

		let message = '';
		if (hasExistingData) {
			message = `팔레트 ${palletId}에 저장된 ${counts.total}개의 상품을 로드했습니다.`;
			if (counts.pending > 0) {
				message += ` (대기 중: ${counts.pending}개)`;
			}
			if (counts.failed > 0) {
				message += ` (실패: ${counts.failed}개)`;
			}
		} else {
			message = `팔레트 ${palletId}가 선택되었습니다. 새로운 상품을 스캔해주세요.`;
		}

		console.log('팔레트 데이터 로드 완료:', {
			palletId,
			productCount: products.length,
			counts
		});

		return {
			success: true,
			message,
			products,
			counts,
			hasExistingData
		};
	} catch (error) {
		console.error('팔레트 데이터 로드 중 오류 발생:', error);
		return {
			success: false,
			message: `팔레트 데이터 로드 실패: ${error}`,
			products: [],
			counts: { total: 0, pending: 0, submitting: 0, success: 0, failed: 0 },
			hasExistingData: false
		};
	}
}

/**
 * 팔레트 삭제
 * 지정된 팔레트와 해당 팔레트의 모든 상품을 삭제합니다.
 * @param palletId 삭제할 팔레트 ID
 * @returns 삭제 성공 여부
 */
export async function deletePallet(palletId: string): Promise<boolean> {
	try {
		const state = getBatchState();

		// 팔레트가 존재하지 않는 경우
		if (!state.pallets[palletId]) {
			console.warn(`팔레트 ${palletId}가 존재하지 않습니다.`);
			return false;
		}

		// 현재 팔레트가 삭제 대상인 경우 현재 팔레트 ID 초기화
		if (state.currentPalletId === palletId) {
			// 다른 팔레트가 있으면 첫 번째 팔레트로 변경, 없으면 빈 문자열
			const remainingPallets = Object.keys(state.pallets).filter((id) => id !== palletId);
			state.currentPalletId = remainingPallets.length > 0 ? remainingPallets[0] : '';
		}

		// 팔레트 삭제
		delete state.pallets[palletId];

		// 상태 저장
		saveBatchState(state);

		console.log(`팔레트 ${palletId}가 삭제되었습니다.`);
		return true;
	} catch (error) {
		console.error('팔레트 삭제 중 오류 발생:', error);
		return false;
	}
}

/**
 * 팔레트 복사
 * 지정된 팔레트의 상품들을 다른 팔레트로 복사합니다.
 * @param sourcePalletId 원본 팔레트 ID
 * @param targetPalletId 대상 팔레트 ID
 * @param copyOnlyPending 대기 중인 상품만 복사할지 여부 (기본값: false)
 * @returns 복사 결과 정보
 */
export async function copyPalletProducts(
	sourcePalletId: string,
	targetPalletId: string,
	copyOnlyPending = false
): Promise<{
	success: boolean;
	message: string;
	copiedCount: number;
}> {
	try {
		const state = getBatchState();

		// 원본 팔레트 존재 확인
		if (!state.pallets[sourcePalletId]) {
			return {
				success: false,
				message: `원본 팔레트 ${sourcePalletId}가 존재하지 않습니다.`,
				copiedCount: 0
			};
		}

		// 대상 팔레트가 존재하지 않으면 생성
		if (!state.pallets[targetPalletId]) {
			state.pallets[targetPalletId] = {
				products: [],
				palletInfo: {}
			};
		}

		// 복사할 상품 필터링
		const sourceProducts = state.pallets[sourcePalletId].products;
		const productsToCopy = copyOnlyPending
			? sourceProducts.filter((p) => p.status === 'pending')
			: sourceProducts;

		// 상품 복사 (새로운 ID 생성)
		let copiedCount = 0;
		productsToCopy.forEach((product) => {
			// 대상 팔레트에 동일한 QAID가 있는지 확인
			const existingProduct = state.pallets[targetPalletId].products.find(
				(p) => p.qaid === product.qaid
			);

			if (!existingProduct) {
				// 새로운 ID로 상품 복사
				const copiedProduct: BatchProductData = {
					...product,
					id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`, // 새로운 고유 ID
					palletId: targetPalletId,
					timestamp: Date.now()
				};

				state.pallets[targetPalletId].products.push(copiedProduct);
				copiedCount++;
			}
		});

		// 상태 저장
		saveBatchState(state);

		return {
			success: true,
			message: `${copiedCount}개 상품이 팔레트 ${targetPalletId}로 복사되었습니다.`,
			copiedCount
		};
	} catch (error) {
		console.error('팔레트 상품 복사 중 오류 발생:', error);
		return {
			success: false,
			message: `팔레트 상품 복사 중 오류 발생: ${error}`,
			copiedCount: 0
		};
	}
}

/**
 * 팔레트 병합
 * 여러 팔레트의 상품들을 하나의 팔레트로 병합합니다.
 * @param sourcePalletIds 원본 팔레트 ID 목록
 * @param targetPalletId 대상 팔레트 ID
 * @param deleteSourcePallets 원본 팔레트 삭제 여부 (기본값: false)
 * @returns 병합 결과 정보
 */
export async function mergePallets(
	sourcePalletIds: string[],
	targetPalletId: string,
	deleteSourcePallets = false
): Promise<{
	success: boolean;
	message: string;
	mergedCount: number;
	duplicateCount: number;
}> {
	try {
		const state = getBatchState();
		let mergedCount = 0;
		let duplicateCount = 0;

		// 대상 팔레트가 존재하지 않으면 생성
		if (!state.pallets[targetPalletId]) {
			state.pallets[targetPalletId] = {
				products: [],
				palletInfo: {}
			};
		}

		// 각 원본 팔레트에서 상품 병합
		sourcePalletIds.forEach((sourcePalletId) => {
			if (sourcePalletId === targetPalletId) {
				// 자기 자신과 병합하는 경우 건너뛰기
				return;
			}

			if (!state.pallets[sourcePalletId]) {
				console.warn(`원본 팔레트 ${sourcePalletId}가 존재하지 않습니다.`);
				return;
			}

			// 상품 병합
			state.pallets[sourcePalletId].products.forEach((product) => {
				// 대상 팔레트에 동일한 QAID가 있는지 확인
				const existingProduct = state.pallets[targetPalletId].products.find(
					(p) => p.qaid === product.qaid
				);

				if (!existingProduct) {
					// 팔레트 ID 업데이트하여 병합
					const mergedProduct: BatchProductData = {
						...product,
						palletId: targetPalletId,
						timestamp: Date.now()
					};

					state.pallets[targetPalletId].products.push(mergedProduct);
					mergedCount++;
				} else {
					duplicateCount++;
					console.log(`중복 상품 건너뛰기: QAID ${product.qaid}`);
				}
			});

			// 원본 팔레트 삭제 (옵션)
			if (deleteSourcePallets) {
				delete state.pallets[sourcePalletId];

				// 현재 팔레트가 삭제된 경우 대상 팔레트로 변경
				if (state.currentPalletId === sourcePalletId) {
					state.currentPalletId = targetPalletId;
				}
			}
		});

		// 상태 저장
		saveBatchState(state);

		let message = `${mergedCount}개 상품이 팔레트 ${targetPalletId}로 병합되었습니다.`;
		if (duplicateCount > 0) {
			message += ` (중복 ${duplicateCount}개 건너뛰기)`;
		}
		if (deleteSourcePallets) {
			message += ` 원본 팔레트 삭제됨.`;
		}

		return {
			success: true,
			message,
			mergedCount,
			duplicateCount
		};
	} catch (error) {
		console.error('팔레트 병합 중 오류 발생:', error);
		return {
			success: false,
			message: `팔레트 병합 중 오류 발생: ${error}`,
			mergedCount: 0,
			duplicateCount: 0
		};
	}
}

/**
 * 팔레트 상품 이동
 * 특정 상품들을 다른 팔레트로 이동합니다.
 * @param productIds 이동할 상품 ID 목록
 * @param targetPalletId 대상 팔레트 ID
 * @returns 이동 결과 정보
 */
export async function moveProductsToPallet(
	productIds: string[],
	targetPalletId: string
): Promise<{
	success: boolean;
	message: string;
	movedCount: number;
}> {
	try {
		const state = getBatchState();
		let movedCount = 0;

		// 대상 팔레트가 존재하지 않으면 생성
		if (!state.pallets[targetPalletId]) {
			state.pallets[targetPalletId] = {
				products: [],
				palletInfo: {}
			};
		}

		// 각 상품을 찾아서 이동
		productIds.forEach((productId) => {
			// 모든 팔레트에서 상품 찾기
			for (const sourcePalletId in state.pallets) {
				const productIndex = state.pallets[sourcePalletId].products.findIndex(
					(p) => p.id === productId
				);

				if (productIndex >= 0) {
					// 상품 찾음
					const product = state.pallets[sourcePalletId].products[productIndex];

					// 대상 팔레트에 동일한 QAID가 있는지 확인
					const existingProduct = state.pallets[targetPalletId].products.find(
						(p) => p.qaid === product.qaid
					);

					if (!existingProduct) {
						// 원본 팔레트에서 제거
						state.pallets[sourcePalletId].products.splice(productIndex, 1);

						// 대상 팔레트로 이동 (팔레트 ID 업데이트)
						product.palletId = targetPalletId;
						product.timestamp = Date.now();
						state.pallets[targetPalletId].products.push(product);

						movedCount++;
						console.log(`상품 ${product.qaid}를 팔레트 ${targetPalletId}로 이동했습니다.`);
					} else {
						console.warn(`대상 팔레트에 동일한 QAID(${product.qaid})가 이미 존재합니다.`);
					}

					break; // 상품을 찾았으므로 다음 상품으로
				}
			}
		});

		// 빈 팔레트 정리 (선택사항)
		Object.keys(state.pallets).forEach((palletId) => {
			if (state.pallets[palletId].products.length === 0 && palletId !== targetPalletId) {
				// 현재 팔레트가 빈 팔레트인 경우 대상 팔레트로 변경
				if (state.currentPalletId === palletId) {
					state.currentPalletId = targetPalletId;
				}
			}
		});

		// 상태 저장
		saveBatchState(state);

		return {
			success: true,
			message: `${movedCount}개 상품이 팔레트 ${targetPalletId}로 이동되었습니다.`,
			movedCount
		};
	} catch (error) {
		console.error('상품 이동 중 오류 발생:', error);
		return {
			success: false,
			message: `상품 이동 중 오류 발생: ${error}`,
			movedCount: 0
		};
	}
}

/**
 * 팔레트 상태 요약 정보 가져오기
 * 모든 팔레트의 상태 요약 정보를 반환합니다.
 * @returns 팔레트 상태 요약 정보
 */
export async function getPalletsSummary(): Promise<{
	totalPallets: number;
	totalProducts: number;
	currentPalletId: string;
	palletStats: Array<{
		palletId: string;
		productCount: number;
		pendingCount: number;
		successCount: number;
		failedCount: number;
		lastUpdated: number;
		isActive: boolean;
	}>;
}> {
	try {
		const state = getBatchState();
		const palletStats: Array<{
			palletId: string;
			productCount: number;
			pendingCount: number;
			successCount: number;
			failedCount: number;
			lastUpdated: number;
			isActive: boolean;
		}> = [];

		let totalProducts = 0;

		// 각 팔레트 통계 계산
		Object.keys(state.pallets).forEach((palletId) => {
			const products = state.pallets[palletId].products;
			const productCount = products.length;
			totalProducts += productCount;

			if (productCount > 0) {
				const pendingCount = products.filter((p) => p.status === 'pending').length;
				const successCount = products.filter((p) => p.status === 'success').length;
				const failedCount = products.filter((p) => p.status === 'failed').length;
				const lastUpdated = Math.max(...products.map((p) => p.timestamp));

				palletStats.push({
					palletId,
					productCount,
					pendingCount,
					successCount,
					failedCount,
					lastUpdated,
					isActive: palletId === state.currentPalletId
				});
			}
		});

		// 마지막 업데이트 시간 기준 내림차순 정렬
		palletStats.sort((a, b) => b.lastUpdated - a.lastUpdated);

		return {
			totalPallets: palletStats.length,
			totalProducts,
			currentPalletId: state.currentPalletId,
			palletStats
		};
	} catch (error) {
		console.error('팔레트 요약 정보 조회 중 오류 발생:', error);
		return {
			totalPallets: 0,
			totalProducts: 0,
			currentPalletId: '',
			palletStats: []
		};
	}
}

/**
 * 팔레트별 상품 검색
 * 지정된 팔레트에서 조건에 맞는 상품을 검색합니다.
 * @param palletId 검색할 팔레트 ID
 * @param searchOptions 검색 옵션
 * @returns 검색 결과
 */
export async function searchProductsInPallet(
	palletId: string,
	searchOptions: {
		qaid?: string;
		status?: 'pending' | 'submitting' | 'success' | 'failed';
		dateRange?: { start: number; end: number };
		hasError?: boolean;
	}
): Promise<BatchProductData[]> {
	try {
		const state = getBatchState();

		if (!state.pallets[palletId]) {
			return [];
		}

		let products = state.pallets[palletId].products;

		// QAID 검색
		if (searchOptions.qaid) {
			const searchQaid = searchOptions.qaid.toLowerCase();
			products = products.filter((p) => p.qaid.toLowerCase().includes(searchQaid));
		}

		// 상태 필터링
		if (searchOptions.status) {
			products = products.filter((p) => p.status === searchOptions.status);
		}

		// 날짜 범위 필터링
		if (searchOptions.dateRange) {
			products = products.filter(
				(p) =>
					p.timestamp >= searchOptions.dateRange!.start &&
					p.timestamp <= searchOptions.dateRange!.end
			);
		}

		// 오류 여부 필터링
		if (searchOptions.hasError !== undefined) {
			if (searchOptions.hasError) {
				products = products.filter((p) => p.status === 'failed' && p.errorMessage);
			} else {
				products = products.filter((p) => p.status !== 'failed' || !p.errorMessage);
			}
		}

		// 최신순으로 정렬
		return products.sort((a, b) => b.timestamp - a.timestamp);
	} catch (error) {
		console.error('팔레트 상품 검색 중 오류 발생:', error);
		return [];
	}
}

/**
 * 팔레트 정보 업데이트
 * 팔레트의 메타데이터 정보를 업데이트합니다.
 * @param palletId 팔레트 ID
 * @param palletInfo 업데이트할 팔레트 정보
 * @returns 업데이트 성공 여부
 */
export async function updatePalletInfo(palletId: string, palletInfo: any): Promise<boolean> {
	try {
		const state = getBatchState();

		// 팔레트가 존재하지 않으면 생성
		if (!state.pallets[palletId]) {
			state.pallets[palletId] = {
				products: [],
				palletInfo: {}
			};
		}

		// 팔레트 정보 업데이트
		state.pallets[palletId].palletInfo = {
			...state.pallets[palletId].palletInfo,
			...palletInfo,
			lastUpdated: Date.now()
		};

		// 상태 저장
		saveBatchState(state);

		console.log(`팔레트 ${palletId} 정보가 업데이트되었습니다.`);
		return true;
	} catch (error) {
		console.error('팔레트 정보 업데이트 중 오류 발생:', error);
		return false;
	}
}

/**
 * 팔레트 정보 가져오기
 * 팔레트의 메타데이터 정보를 반환합니다.
 * @param palletId 팔레트 ID
 * @returns 팔레트 정보
 */
export async function getPalletInfo(palletId: string): Promise<any> {
	try {
		const state = getBatchState();

		if (!state.pallets[palletId]) {
			return null;
		}

		return state.pallets[palletId].palletInfo || {};
	} catch (error) {
		console.error('팔레트 정보 조회 중 오류 발생:', error);
		return null;
	}
}

/**
 * 모의 서버 전송 함수
 * 실제 서버 API 호출을 모의합니다. 개발 및 테스트 환경에서 사용됩니다.
 * @param products 전송할 상품 목록
 * @returns 전송 결과를 담은 Promise
 */
export async function mockSubmitToServer(products: BatchProductData[]): Promise<BatchSaveResult> {
	return new Promise((resolve) => {
		// 모의 지연 (1~2초)
		setTimeout(
			() => {
				// 모의 성공/실패 결과 생성
				const successIds: string[] = [];
				const failedItems: Array<{ id: string; qaid: string; error: string }> = [];

				products.forEach((product) => {
					// 90% 확률로 성공, 10% 확률로 실패 (테스트용)
					if (Math.random() < 0.9) {
						successIds.push(product.id);
					} else {
						failedItems.push({
							id: product.id,
							qaid: product.qaid,
							error: '서버 통신 오류 (모의 실패)'
						});
					}
				});

				resolve({
					success: failedItems.length === 0,
					message:
						failedItems.length === 0
							? `${successIds.length}개 상품이 성공적으로 저장되었습니다.`
							: `${successIds.length}개 성공, ${failedItems.length}개 실패`,
					successIds,
					failedItems
				});
			},
			1000 + Math.random() * 1000
		);
	});
}
