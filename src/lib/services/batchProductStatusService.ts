/**
 * 배치 상품 상태 관리 서비스
 *
 * 이 파일은 배치 상품의 상태(대기중/성공/실패) 관리와 관련된 함수들을 제공합니다.
 * 상품 상태 변경, 상태별 조회, 카운트 기능을 구현합니다.
 */

import type { BatchProductData, BatchProductFilter, BatchSaveResult } from '../types/batchTypes';
import { getBatchState, saveBatchState } from '../utils/batchStorageUtils';
import { getProductById, getProductByQaid, getAllProducts } from './batchProductService';

/**
 * 상품 상태 변경
 * 상품 ID를 기반으로 상태를 변경합니다.
 * @param productId 상품 ID
 * @param status 변경할 상태 ('pending', 'success', 'failed')
 * @param errorMessage 오류 메시지 (실패 상태인 경우)
 * @returns 성공 여부를 담은 Promise
 */
export async function updateProductStatus(
	productId: string,
	status: 'pending' | 'success' | 'failed',
	errorMessage?: string
): Promise<boolean> {
	try {
		// 현재 상태 가져오기
		const state = getBatchState();

		// 모든 팔레트를 순회하며 상품 찾기
		let found = false;
		for (const palletId in state.pallets) {
			const productIndex = state.pallets[palletId].products.findIndex((p) => p.id === productId);
			if (productIndex >= 0) {
				// 상품 찾음, 상태 업데이트
				state.pallets[palletId].products[productIndex] = {
					...state.pallets[palletId].products[productIndex],
					status,
					errorMessage: status === 'failed' ? errorMessage : undefined,
					// 타임스탬프 업데이트
					timestamp: Date.now()
				};
				found = true;
				break;
			}
		}

		if (!found) {
			console.error(`ID가 ${productId}인 상품을 찾을 수 없습니다.`);
			return false;
		}

		// 상태 저장
		saveBatchState(state);
		return true;
	} catch (error) {
		console.error('상품 상태 업데이트 중 오류 발생:', error);
		return false;
	}
}

/**
 * QAID로 상품 상태 변경
 * QAID를 기반으로 상태를 변경합니다.
 * @param qaid QAID 바코드 값
 * @param status 변경할 상태 ('pending', 'success', 'failed')
 * @param errorMessage 오류 메시지 (실패 상태인 경우)
 * @returns 성공 여부를 담은 Promise
 */
export async function updateProductStatusByQaid(
	qaid: string,
	status: 'pending' | 'success' | 'failed',
	errorMessage?: string
): Promise<boolean> {
	try {
		// 현재 상태 가져오기
		const state = getBatchState();

		// 모든 팔레트를 순회하며 상품 찾기
		let found = false;
		for (const palletId in state.pallets) {
			const productIndex = state.pallets[palletId].products.findIndex((p) => p.qaid === qaid);
			if (productIndex >= 0) {
				// 상품 찾음, 상태 업데이트
				state.pallets[palletId].products[productIndex] = {
					...state.pallets[palletId].products[productIndex],
					status,
					errorMessage: status === 'failed' ? errorMessage : undefined,
					// 타임스탬프 업데이트
					timestamp: Date.now()
				};
				found = true;
				break;
			}
		}

		if (!found) {
			console.error(`QAID가 ${qaid}인 상품을 찾을 수 없습니다.`);
			return false;
		}

		// 상태 저장
		saveBatchState(state);
		return true;
	} catch (error) {
		console.error('상품 상태 업데이트 중 오류 발생:', error);
		return false;
	}
}

/**
 * 여러 상품 상태 일괄 변경
 * 여러 상품의 상태를 한 번에 변경합니다.
 * @param productIds 상품 ID 배열
 * @param status 변경할 상태 ('pending', 'success', 'failed')
 * @param errorMessages 오류 메시지 맵 (실패 상태인 경우, ID를 키로 사용)
 * @returns 성공 여부를 담은 Promise
 */
export async function updateMultipleProductStatus(
	productIds: string[],
	status: 'pending' | 'success' | 'failed',
	errorMessages?: Record<string, string>
): Promise<boolean> {
	try {
		// 현재 상태 가져오기
		const state = getBatchState();
		let updatedCount = 0;

		// 각 상품 ID에 대해 상태 업데이트
		for (const productId of productIds) {
			// 모든 팔레트를 순회하며 상품 찾기
			for (const palletId in state.pallets) {
				const productIndex = state.pallets[palletId].products.findIndex((p) => p.id === productId);
				if (productIndex >= 0) {
					// 상품 찾음, 상태 업데이트
					state.pallets[palletId].products[productIndex] = {
						...state.pallets[palletId].products[productIndex],
						status,
						errorMessage:
							status === 'failed' && errorMessages ? errorMessages[productId] : undefined,
						// 타임스탬프 업데이트
						timestamp: Date.now()
					};
					updatedCount++;
					break;
				}
			}
		}

		if (updatedCount === 0) {
			console.warn('업데이트할 상품을 찾을 수 없습니다.');
			return false;
		}

		// 상태 저장
		saveBatchState(state);
		return true;
	} catch (error) {
		console.error('여러 상품 상태 업데이트 중 오류 발생:', error);
		return false;
	}
}

/**
 * 팔레트 내 모든 상품 상태 변경
 * 특정 팔레트의 모든 상품 상태를 변경합니다.
 * @param palletId 팔레트 ID
 * @param status 변경할 상태 ('pending', 'success', 'failed')
 * @param filter 필터 옵션 (선택 사항, 특정 상태의 상품만 변경)
 * @returns 성공 여부를 담은 Promise
 */
export async function updateAllProductStatusInPallet(
	palletId: string,
	status: 'pending' | 'success' | 'failed',
	filter?: { currentStatus?: 'pending' | 'success' | 'failed' }
): Promise<boolean> {
	try {
		// 현재 상태 가져오기
		const state = getBatchState();

		// 팔레트가 존재하는지 확인
		if (!state.pallets[palletId]) {
			console.error(`ID가 ${palletId}인 팔레트를 찾을 수 없습니다.`);
			return false;
		}

		// 팔레트 내 상품 상태 업데이트
		let updatedCount = 0;
		state.pallets[palletId].products.forEach((product, index) => {
			// 필터 적용 (지정된 경우)
			if (filter && filter.currentStatus && product.status !== filter.currentStatus) {
				return;
			}

			// 상태 업데이트
			state.pallets[palletId].products[index] = {
				...product,
				status,
				errorMessage: status === 'failed' ? product.errorMessage : undefined,
				timestamp: Date.now()
			};
			updatedCount++;
		});

		if (updatedCount === 0) {
			console.warn('업데이트할 상품이 없습니다.');
			return false;
		}

		// 상태 저장
		saveBatchState(state);
		return true;
	} catch (error) {
		console.error('팔레트 내 상품 상태 업데이트 중 오류 발생:', error);
		return false;
	}
}

/**
 * 모든 상품 상태 변경
 * 모든 팔레트의 상품 상태를 변경합니다.
 * @param status 변경할 상태 ('pending', 'success', 'failed')
 * @param filter 필터 옵션 (선택 사항, 특정 상태의 상품만 변경)
 * @returns 성공 여부를 담은 Promise
 */
export async function updateAllProductStatus(
	status: 'pending' | 'success' | 'failed',
	filter?: { currentStatus?: 'pending' | 'success' | 'failed' }
): Promise<boolean> {
	try {
		// 현재 상태 가져오기
		const state = getBatchState();
		let updatedCount = 0;

		// 모든 팔레트를 순회하며 상품 상태 업데이트
		for (const palletId in state.pallets) {
			state.pallets[palletId].products.forEach((product, index) => {
				// 필터 적용 (지정된 경우)
				if (filter && filter.currentStatus && product.status !== filter.currentStatus) {
					return;
				}

				// 상태 업데이트
				state.pallets[palletId].products[index] = {
					...product,
					status,
					errorMessage: status === 'failed' ? product.errorMessage : undefined,
					timestamp: Date.now()
				};
				updatedCount++;
			});
		}

		if (updatedCount === 0) {
			console.warn('업데이트할 상품이 없습니다.');
			return false;
		}

		// 상태 저장
		saveBatchState(state);
		return true;
	} catch (error) {
		console.error('모든 상품 상태 업데이트 중 오류 발생:', error);
		return false;
	}
}

/**
 * 상태별 상품 조회 (확장 버전)
 * 특정 상태의 상품을 조회하고 추가 필터링 옵션을 제공합니다.
 * @param status 상품 상태 ('pending', 'success', 'failed', 'all')
 * @param options 추가 필터링 옵션
 * @returns 상품 목록
 */
export function getProductsByStatusExtended(
	status: 'pending' | 'success' | 'failed' | 'all',
	options?: {
		palletId?: string;
		sortBy?: 'timestamp' | 'qaid';
		sortOrder?: 'asc' | 'desc';
		limit?: number;
		offset?: number;
		searchText?: string;
	}
): BatchProductData[] {
	try {
		// 기본 필터 설정
		const filter: BatchProductFilter = {
			status: status === 'all' ? undefined : status,
			palletId: options?.palletId,
			searchText: options?.searchText
		};

		// 상품 조회
		let products = getAllProducts(filter);

		// 정렬
		if (options?.sortBy) {
			products = products.sort((a, b) => {
				if (options.sortBy === 'timestamp') {
					return options.sortOrder === 'asc'
						? a.timestamp - b.timestamp
						: b.timestamp - a.timestamp;
				} else if (options.sortBy === 'qaid') {
					return options.sortOrder === 'asc'
						? a.qaid.localeCompare(b.qaid)
						: b.qaid.localeCompare(a.qaid);
				}
				return 0;
			});
		}

		// 페이지네이션
		if (options?.limit !== undefined) {
			const offset = options.offset || 0;
			products = products.slice(offset, offset + options.limit);
		}

		return products;
	} catch (error) {
		console.error('상태별 상품 조회 중 오류 발생:', error);
		return [];
	}
}

/**
 * 상태 변경 이벤트 리스너 등록
 * 상품 상태 변경 시 호출될 콜백 함수를 등록합니다.
 * @param callback 상태 변경 시 호출될 콜백 함수
 * @returns 리스너 제거 함수
 */
export function addStatusChangeListener(
	callback: (data: {
		productId: string;
		oldStatus: 'pending' | 'success' | 'failed';
		newStatus: 'pending' | 'success' | 'failed';
		product: BatchProductData;
	}) => void
): () => void {
	// 상태 변경 이벤트를 위한 커스텀 이벤트 이름
	const eventName = 'batch-product-status-change';

	// 이벤트 리스너 함수
	const listener = (event: CustomEvent) => {
		callback(event.detail);
	};

	// 이벤트 리스너 등록
	window.addEventListener(eventName, listener as EventListener);

	// 리스너 제거 함수 반환
	return () => {
		window.removeEventListener(eventName, listener as EventListener);
	};
}

/**
 * 상태 변경 이벤트 발생
 * 상품 상태 변경 시 이벤트를 발생시킵니다.
 * @param productId 상품 ID
 * @param oldStatus 이전 상태
 * @param newStatus 새 상태
 */
export function dispatchStatusChangeEvent(
	productId: string,
	oldStatus: 'pending' | 'success' | 'failed',
	newStatus: 'pending' | 'success' | 'failed'
): void {
	try {
		// 상품 정보 조회
		const product = getProductById(productId);
		if (!product) {
			console.error(`ID가 ${productId}인 상품을 찾을 수 없습니다.`);
			return;
		}

		// 커스텀 이벤트 생성 및 발생
		const event = new CustomEvent('batch-product-status-change', {
			detail: {
				productId,
				oldStatus,
				newStatus,
				product
			}
		});

		window.dispatchEvent(event);
	} catch (error) {
		console.error('상태 변경 이벤트 발생 중 오류 발생:', error);
	}
}

/**
 * 상태별 상품 통계 조회
 * 상태별 상품 수와 비율을 조회합니다.
 * @param palletId 팔레트 ID (선택 사항)
 * @returns 상태별 통계 정보
 */
export function getProductStatusStats(palletId?: string): {
	counts: {
		total: number;
		pending: number;
		success: number;
		failed: number;
	};
	percentages: {
		pending: number;
		success: number;
		failed: number;
	};
} {
	try {
		// 상품 수 조회
		const counts = {
			total: 0,
			pending: 0,
			success: 0,
			failed: 0
		};

		// 현재 상태 가져오기
		const state = getBatchState();

		// 팔레트 ID가 지정된 경우 해당 팔레트의 상품만 계산
		if (palletId) {
			if (!state.pallets[palletId]) {
				return {
					counts: { total: 0, pending: 0, success: 0, failed: 0 },
					percentages: { pending: 0, success: 0, failed: 0 }
				};
			}

			const products = state.pallets[palletId].products;
			counts.total = products.length;
			counts.pending = products.filter((p) => p.status === 'pending').length;
			counts.success = products.filter((p) => p.status === 'success').length;
			counts.failed = products.filter((p) => p.status === 'failed').length;
		} else {
			// 전체 상품 수 계산
			counts.total = state.totalCount;
			counts.pending = state.pendingCount;
			counts.success = state.successCount;
			counts.failed = state.failedCount;
		}

		// 비율 계산
		const percentages = {
			pending: counts.total > 0 ? Math.round((counts.pending / counts.total) * 100) : 0,
			success: counts.total > 0 ? Math.round((counts.success / counts.total) * 100) : 0,
			failed: counts.total > 0 ? Math.round((counts.failed / counts.total) * 100) : 0
		};

		return { counts, percentages };
	} catch (error) {
		console.error('상태별 상품 통계 조회 중 오류 발생:', error);
		return {
			counts: { total: 0, pending: 0, success: 0, failed: 0 },
			percentages: { pending: 0, success: 0, failed: 0 }
		};
	}
}

/**
 * 실패한 상품 그룹화
 * 실패한 상품을 오류 메시지별로 그룹화합니다.
 * @param palletId 팔레트 ID (선택 사항)
 * @returns 오류 메시지별 상품 그룹
 */
export function groupFailedProductsByError(palletId?: string): Record<string, BatchProductData[]> {
	try {
		// 실패한 상품 조회
		const failedProducts = getProductsByStatusExtended('failed', { palletId });

		// 오류 메시지별 그룹화
		const groups: Record<string, BatchProductData[]> = {};
		failedProducts.forEach((product) => {
			const errorKey = product.errorMessage || '알 수 없는 오류';
			if (!groups[errorKey]) {
				groups[errorKey] = [];
			}
			groups[errorKey].push(product);
		});

		return groups;
	} catch (error) {
		console.error('실패한 상품 그룹화 중 오류 발생:', error);
		return {};
	}
}

/**
 * 상태 변경 히스토리 추적
 * 상품의 상태 변경 히스토리를 추적합니다.
 * 참고: 이 기능은 로컬스토리지에 추가 데이터를 저장합니다.
 * @param productId 상품 ID
 * @param oldStatus 이전 상태
 * @param newStatus 새 상태
 * @param reason 상태 변경 이유 (선택 사항)
 */
export function trackStatusChange(
	productId: string,
	oldStatus: 'pending' | 'success' | 'failed',
	newStatus: 'pending' | 'success' | 'failed',
	reason?: string
): void {
	try {
		// 상태 변경 히스토리 키
		const historyKey = 'batch_pallet_status_history';

		// 기존 히스토리 로드
		let history: Array<{
			productId: string;
			oldStatus: string;
			newStatus: string;
			timestamp: number;
			reason?: string;
		}> = [];

		try {
			const storedHistory = localStorage.getItem(historyKey);
			if (storedHistory) {
				history = JSON.parse(storedHistory);
			}
		} catch (e) {
			console.warn('상태 변경 히스토리 로드 실패, 새로 시작합니다.');
		}

		// 새 항목 추가
		history.push({
			productId,
			oldStatus,
			newStatus,
			timestamp: Date.now(),
			reason
		});

		// 히스토리 크기 제한 (최대 100개)
		if (history.length > 100) {
			history = history.slice(-100);
		}

		// 저장
		localStorage.setItem(historyKey, JSON.stringify(history));
	} catch (error) {
		console.error('상태 변경 히스토리 추적 중 오류 발생:', error);
	}
}

/**
 * 상태 변경 히스토리 조회
 * 상품의 상태 변경 히스토리를 조회합니다.
 * @param productId 상품 ID (선택 사항, 지정 시 해당 상품의 히스토리만 반환)
 * @returns 상태 변경 히스토리
 */
export function getStatusChangeHistory(productId?: string): Array<{
	productId: string;
	oldStatus: string;
	newStatus: string;
	timestamp: number;
	reason?: string;
}> {
	try {
		// 상태 변경 히스토리 키
		const historyKey = 'batch_pallet_status_history';

		// 히스토리 로드
		let history: Array<{
			productId: string;
			oldStatus: string;
			newStatus: string;
			timestamp: number;
			reason?: string;
		}> = [];

		try {
			const storedHistory = localStorage.getItem(historyKey);
			if (storedHistory) {
				history = JSON.parse(storedHistory);
			}
		} catch (e) {
			console.warn('상태 변경 히스토리 로드 실패');
			return [];
		}

		// 특정 상품의 히스토리만 필터링 (지정된 경우)
		if (productId) {
			history = history.filter((item) => item.productId === productId);
		}

		// 시간순 정렬 (최신순)
		return history.sort((a, b) => b.timestamp - a.timestamp);
	} catch (error) {
		console.error('상태 변경 히스토리 조회 중 오류 발생:', error);
		return [];
	}
}

/**
 * 상태 변경 알림 설정
 * 상품 상태 변경 시 알림을 표시합니다.
 * @param options 알림 옵션
 * @returns 알림 설정 제거 함수
 */
export function setupStatusChangeNotifications(options?: {
	showSuccess?: boolean;
	showFailure?: boolean;
	showPending?: boolean;
}): () => void {
	// 기본 옵션 설정
	const opts = {
		showSuccess: true,
		showFailure: true,
		showPending: false,
		...options
	};

	// 상태 변경 리스너 등록
	const removeListener = addStatusChangeListener((data) => {
		// 상태별 알림 표시 여부 확인
		if (
			(data.newStatus === 'success' && !opts.showSuccess) ||
			(data.newStatus === 'failed' && !opts.showFailure) ||
			(data.newStatus === 'pending' && !opts.showPending)
		) {
			return;
		}

		// 알림 메시지 생성
		let message = '';
		switch (data.newStatus) {
			case 'success':
				message = `상품 ${data.product.qaid}가 성공적으로 처리되었습니다.`;
				break;
			case 'failed':
				message = `상품 ${data.product.qaid} 처리 실패: ${
					data.product.errorMessage || '알 수 없는 오류'
				}`;
				break;
			case 'pending':
				message = `상품 ${data.product.qaid}가 대기 상태로 변경되었습니다.`;
				break;
		}

		// 알림 표시 (커스텀 이벤트 발생)
		const notificationEvent = new CustomEvent('batch-notification', {
			detail: {
				type: data.newStatus,
				message,
				product: data.product
			}
		});

		window.dispatchEvent(notificationEvent);
	});

	return removeListener;
}
