/**
 * 팔레트 API 서비스
 *
 * 이 파일은 팔레트 및 상품 관련 API 호출 함수들을 제공합니다.
 * 서버와의 통신을 담당하며, 배치 상품 데이터의 서버 전송 기능을 구현합니다.
 */

import { authClient } from './AxiosBackend';
import type { BatchProductData, BatchSaveResult } from '../types/batchTypes';

/**
 * 상품 일괄 저장 API
 * 여러 상품을 서버에 일괄 저장합니다.
 * @param products 저장할 상품 목록
 * @returns 저장 결과를 담은 Promise
 */
export async function saveProducts(products: BatchProductData[]): Promise<BatchSaveResult> {
	try {
		// 서버에 전송할 데이터 형식으로 변환
		const requestData = products.map((product) => ({
			qaid: product.qaid,
			palletId: product.palletId,
			productInfo: product.productInfo || {}
		}));

		// API 호출
		const response = await authClient.post('/api/pallets/products/batch', {
			products: requestData
		});

		// 응답 데이터 처리
		const responseData = response.data;

		// 성공 및 실패 항목 분류
		const successIds: string[] = [];
		const failedItems: Array<{ id: string; qaid: string; error: string }> = [];

		// 응답 데이터에서 성공/실패 항목 추출
		if (responseData.results) {
			products.forEach((product) => {
				const result = responseData.results.find((r: any) => r.qaid === product.qaid);
				if (result && result.success) {
					successIds.push(product.id);
				} else {
					failedItems.push({
						id: product.id,
						qaid: product.qaid,
						error: result?.error || '알 수 없는 오류'
					});
				}
			});
		}

		return {
			success: failedItems.length === 0,
			message:
				responseData.message ||
				(failedItems.length === 0
					? `${successIds.length}개 상품이 성공적으로 저장되었습니다.`
					: `${successIds.length}개 성공, ${failedItems.length}개 실패`),
			successIds,
			failedItems
		};
	} catch (error: any) {
		console.error('상품 일괄 저장 API 호출 중 오류 발생:', error);

		// 오류 응답 생성
		return {
			success: false,
			message: `서버 통신 오류: ${error.message || '알 수 없는 오류'}`,
			successIds: [],
			failedItems: products.map((product) => ({
				id: product.id,
				qaid: product.qaid,
				error: error.message || '서버 통신 오류'
			}))
		};
	}
}

/**
 * 개별 상품 저장 API
 * 단일 상품을 서버에 저장합니다.
 * @param product 저장할 상품
 * @returns 저장 결과를 담은 Promise
 */
export async function saveProduct(product: BatchProductData): Promise<{
	success: boolean;
	message: string;
	error?: string;
}> {
	try {
		// 서버에 전송할 데이터 형식으로 변환
		const requestData = {
			qaid: product.qaid,
			palletId: product.palletId,
			productInfo: product.productInfo || {}
		};

		// API 호출
		const response = await authClient.post('/api/pallets/products', requestData);

		return {
			success: true,
			message: response.data.message || '상품이 성공적으로 저장되었습니다.'
		};
	} catch (error: any) {
		console.error('상품 저장 API 호출 중 오류 발생:', error);

		return {
			success: false,
			message: '상품 저장 실패',
			error: error.message || '서버 통신 오류'
		};
	}
}

/**
 * 팔레트 정보 조회 API
 * 팔레트 정보를 서버에서 조회합니다.
 * @param palletId 팔레트 ID
 * @returns 팔레트 정보를 담은 Promise
 */
export async function getPalletInfo(palletId: string): Promise<{
	success: boolean;
	data?: any;
	error?: string;
}> {
	try {
		// API 호출
		const response = await authClient.get(`/api/pallets/${palletId}`);

		return {
			success: true,
			data: response.data
		};
	} catch (error: any) {
		console.error('팔레트 정보 조회 API 호출 중 오류 발생:', error);

		return {
			success: false,
			error: error.message || '서버 통신 오류'
		};
	}
}
