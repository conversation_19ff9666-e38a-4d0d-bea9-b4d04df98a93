/**
 * 플랫폼 감지 서비스
 * 데스크탑, 안드로이드, iOS, 웹 플랫폼을 감지하고 플랫폼별 기능을 제공합니다.
 */

import type { Platform, StorageType } from '$lib/types/auth';

/**
 * 현재 실행 중인 플랫폼을 감지합니다.
 * @returns 플랫폼 타입
 */
export function getCurrentPlatform(): Platform {
	// Tauri 환경 확인 (여러 방법으로 확인)
	const isTauriEnv =
		typeof window !== 'undefined' &&
		(!!window.__TAURI__ ||
			!!window.__TAURI_INTERNALS__ ||
			!!window.__TAURI_METADATA__ ||
			// Vite 환경에서 Tauri 확인
			!!import.meta.env.TAURI_PLATFORM ||
			// User Agent에서 Tauri 확인
			(typeof navigator !== 'undefined' && navigator.userAgent.includes('Tauri')));

	if (isTauriEnv) {
		// Tauri 환경에서 플랫폼 감지
		const userAgent = navigator.userAgent.toLowerCase();

		if (userAgent.includes('android')) {
			return 'android';
		} else if (userAgent.includes('iphone') || userAgent.includes('ipad')) {
			return 'ios';
		} else {
			return 'desktop';
		}
	}

	// 웹 브라우저 환경
	return 'web';
}

/**
 * 데스크탑 플랫폼인지 확인합니다.
 * @returns 데스크탑 여부
 */
export function isDesktop(): boolean {
	return getCurrentPlatform() === 'desktop';
}

/**
 * 안드로이드 플랫폼인지 확인합니다.
 * @returns 안드로이드 여부
 */
export function isAndroid(): boolean {
	return getCurrentPlatform() === 'android';
}

/**
 * iOS 플랫폼인지 확인합니다.
 * @returns iOS 여부
 */
export function isIOS(): boolean {
	return getCurrentPlatform() === 'ios';
}

/**
 * 웹 브라우저 환경인지 확인합니다.
 * @returns 웹 환경 여부
 */
export function isWeb(): boolean {
	return getCurrentPlatform() === 'web';
}

/**
 * Tauri 환경인지 확인합니다.
 * @returns Tauri 환경 여부
 */
export function isTauri(): boolean {
	return (
		typeof window !== 'undefined' &&
		(!!window.__TAURI__ ||
			!!window.__TAURI_INTERNALS__ ||
			!!window.__TAURI_METADATA__ ||
			// Vite 환경에서 Tauri 확인
			!!import.meta.env.TAURI_PLATFORM ||
			// User Agent에서 Tauri 확인
			(typeof navigator !== 'undefined' && navigator.userAgent.includes('Tauri')))
	);
}

/**
 * 모바일 플랫폼인지 확인합니다.
 * @returns 모바일 여부 (안드로이드 또는 iOS)
 */
export function isMobile(): boolean {
	const platform = getCurrentPlatform();
	return platform === 'android' || platform === 'ios';
}

/**
 * 플랫폼별 보안 저장소 지원 여부를 확인합니다.
 * @returns 보안 저장소 지원 여부
 */
export function hasSecureStorage(): boolean {
	const platform = getCurrentPlatform();

	switch (platform) {
		case 'desktop':
			// Tauri Store API 사용 가능
			return isTauri();
		case 'android':
			// Android Keystore 사용 가능 (Tauri 환경에서)
			return isTauri();
		case 'ios':
			// iOS Keychain 사용 가능 (Tauri 환경에서)
			return isTauri();
		case 'web':
			// 웹에서는 localStorage만 사용 가능 (보안성 낮음)
			return false;
		default:
			return false;
	}
}

/**
 * 플랫폼별 저장소 타입을 반환합니다.
 * @returns 저장소 타입
 */
export function getStorageType(): StorageType {
	const platform = getCurrentPlatform();

	if (!isTauri()) {
		// 웹 환경에서는 localStorage 사용
		return 'localstorage';
	}

	switch (platform) {
		case 'desktop':
			return 'tauri-store';
		case 'android':
			return 'android-keystore';
		case 'ios':
			return 'ios-keychain';
		default:
			return 'memory';
	}
}

/**
 * 플랫폼 정보를 콘솔에 출력합니다 (개발 환경에서만).
 */
export function debugPlatform(): void {
	if (import.meta.env.DEV) {
		console.log('[Platform Debug]', {
			platform: getCurrentPlatform(),
			isTauri: isTauri(),
			isMobile: isMobile(),
			hasSecureStorage: hasSecureStorage(),
			storageType: getStorageType(),
			userAgent: navigator.userAgent
		});
	}
}

/**
 * 플랫폼에 따라 body 요소에 적절한 클래스를 적용합니다.
 * 이미 스크립트로 적용되었을 수 있으므로 중복 확인 후 적용합니다.
 */
export function applyPlatformClasses(): void {
	if (typeof document === 'undefined') return;

	const platform = getCurrentPlatform();
	const body = document.body;

	// 기존 플랫폼 클래스 제거 (중복 방지)
	body.classList.remove('platform-desktop', 'platform-android', 'platform-ios', 'platform-web', 'platform-mobile');

	// 새 플랫폼 클래스 적용
	switch (platform) {
		case 'desktop':
			body.classList.add('platform-desktop');
			break;
		case 'android':
			body.classList.add('platform-android', 'platform-mobile');
			break;
		case 'ios':
			body.classList.add('platform-ios', 'platform-mobile');
			break;
		case 'web':
			body.classList.add('platform-web');
			break;
	}
}


/**
 * 플랫폼별 기능 지원 여부를 확인합니다.
 * @param feature 확인할 기능
 * @returns 지원 여부
 */
export function supportsFeature(
	feature: 'printer' | 'updater' | 'notifications' | 'filesystem'
): boolean {
	const platform = getCurrentPlatform();

	switch (feature) {
		case 'printer':
			// 프린터는 데스크탑에서만 지원
			return platform === 'desktop' && isTauri();
		case 'updater':
			// 업데이터는 데스크탑에서만 지원
			return platform === 'desktop' && isTauri();
		case 'notifications':
			// 알림은 모든 Tauri 환경에서 지원
			return isTauri();
		case 'filesystem':
			// 파일시스템은 모든 Tauri 환경에서 지원
			return isTauri();
		default:
			return false;
	}
}
