/**
 * 토큰 자동 갱신 서비스
 * 리프레시 토큰을 사용한 액세스 토큰 갱신, 중복 방지, 에러 처리 기능을 제공합니다.
 */

import axios from 'axios';
import { goto } from '$app/navigation';
import { tokenService } from './tokenService';
import { safeRedirect } from '$lib/utils/authHelpers';
import type { TokenResponse, TokenRefreshError } from '$lib/types/auth';

/**
 * 토큰 갱신 상태 인터페이스
 */
interface RefreshState {
	isRefreshing: boolean;
	refreshPromise: Promise<string | null> | null;
	failedQueue: Array<{
		resolve: (value: string | null) => void;
		reject: (error: any) => void;
	}>;
}

// 토큰 갱신 상태 관리
const refreshState: RefreshState = {
	isRefreshing: false,
	refreshPromise: null,
	failedQueue: []
};

/**
 * 대기 중인 요청들을 처리합니다.
 * @param error 에러 객체 (성공 시 null)
 * @param token 새로운 액세스 토큰 (실패 시 null)
 */
function processFailedQueue(error: TokenRefreshError | null, token: string | null = null): void {
	refreshState.failedQueue.forEach(({ resolve, reject }) => {
		if (error) {
			reject(error);
		} else {
			resolve(token);
		}
	});

	refreshState.failedQueue = [];
}

/**
 * 토큰 갱신 에러를 생성합니다.
 * @param type 에러 타입
 * @param message 에러 메시지
 * @param originalError 원본 에러
 * @returns 토큰 갱신 에러 객체
 */
function createRefreshError(
	type: TokenRefreshError['type'],
	message: string,
	originalError?: any
): TokenRefreshError {
	return {
		type,
		message,
		originalError
	};
}

/**
 * 사용자를 로그아웃 처리합니다.
 * @param reason 로그아웃 사유
 */
async function performLogout(reason: string): Promise<void> {
	try {
		if (import.meta.env.DEV) {
			console.log(`[Token Refresh] 로그아웃 처리: ${reason}`);
		}

		// 사용자에게 알림 표시 (브라우저 환경에서만)
		if (typeof window !== 'undefined') {
			// 토큰 만료로 인한 로그아웃임을 사용자에게 알림
			const message =
				reason === '리프레시 토큰 만료'
					? '보안을 위해 세션이 만료되었습니다. 다시 로그인해주세요.'
					: `세션이 종료되었습니다: ${reason}`;

			// 간단한 알림 표시 (나중에 토스트 알림으로 개선 가능)
			console.info(`[사용자 알림] ${message}`);

			// 세션 스토리지에 로그아웃 사유 저장 (로그인 페이지에서 표시용)
			try {
				sessionStorage.setItem('logout_reason', message);
			} catch (storageError) {
				// 세션 스토리지 사용 불가 시 무시
			}
		}

		// 서버에 로그아웃 요청 (토큰이 있는 경우에만)
		const refreshToken = await tokenService.getRefreshToken();
		if (refreshToken) {
			try {
				await axios.post(
					`${import.meta.env.VITE_HOME_URL}/api/auth/logout`,
					{},
					{
						headers: {
							Authorization: `Bearer ${refreshToken}`,
							Accept: 'application/json'
						},
						timeout: 5000 // 5초 타임아웃
					}
				);
			} catch (logoutError) {
				// 서버 로그아웃 실패해도 로컬 토큰은 삭제
				console.warn('[Token Refresh] 서버 로그아웃 실패:', logoutError);
			}
		}

		// 로컬 토큰 삭제
		await tokenService.clearTokens();

		// 로그인 페이지로 안전하게 리다이렉트
		await safeRedirect('/login');
	} catch (error) {
		console.error('[Token Refresh] 로그아웃 처리 실패:', error);
		// 에러가 발생해도 로그인 페이지로 이동
		goto('/login');
	}
}

/**
 * 리프레시 토큰을 사용하여 새로운 액세스 토큰을 발급받습니다.
 * @returns 새로운 액세스 토큰 또는 null
 */
async function performTokenRefresh(): Promise<string | null> {
	try {
		const refreshToken = await tokenService.getRefreshToken();
		if (!refreshToken) {
			throw createRefreshError('NO_REFRESH_TOKEN', '리프레시 토큰이 없습니다');
		}

		if (import.meta.env.DEV) {
			console.log('[Token Refresh] 토큰 갱신 시작');
		}

		if (import.meta.env.DEV) {
			console.log('[Token Refresh] 갱신 요청 준비:', {
				url: `${import.meta.env.VITE_HOME_URL}/api/auth/refresh`,
				hasRefreshToken: !!refreshToken,
				refreshTokenLength: refreshToken.length,
				refreshTokenPreview: refreshToken.substring(0, 20) + '...'
			});
		}

		// Laravel JWT 토큰 갱신 요청
		// 방법 1: 요청 본문에 refresh_token 포함 + Authorization 헤더
		let response;
		try {
			const requestPayload = {
				refresh_token: refreshToken
			};

			response = await axios.post(
				`${import.meta.env.VITE_HOME_URL}/api/auth/refresh`,
				requestPayload,
				{
					headers: {
						Authorization: `Bearer ${refreshToken}`,
						Accept: 'application/json',
						'Content-Type': 'application/json'
					},
					timeout: 15000 // 15초 타임아웃
				}
			);

			if (import.meta.env.DEV) {
				console.log('[Token Refresh] 방법 1 성공 (본문 + 헤더)');
			}
		} catch (firstError) {
			if (import.meta.env.DEV) {
				console.log('[Token Refresh] 방법 1 실패, 방법 2 시도 (헤더만)');
			}

			// 방법 2: Authorization 헤더만 사용 (빈 본문)
			try {
				response = await axios.post(
					`${import.meta.env.VITE_HOME_URL}/api/auth/refresh`,
					{},
					{
						headers: {
							Authorization: `Bearer ${refreshToken}`,
							Accept: 'application/json',
							'Content-Type': 'application/json'
						},
						timeout: 15000
					}
				);

				if (import.meta.env.DEV) {
					console.log('[Token Refresh] 방법 2 성공 (헤더만)');
				}
			} catch (secondError) {
				if (import.meta.env.DEV) {
					console.log('[Token Refresh] 방법 2 실패, 방법 3 시도 (본문만)');
				}

				// 방법 3: 요청 본문에만 refresh_token 포함
				response = await axios.post(
					`${import.meta.env.VITE_HOME_URL}/api/auth/refresh`,
					{
						refresh_token: refreshToken
					},
					{
						headers: {
							Accept: 'application/json',
							'Content-Type': 'application/json'
						},
						timeout: 15000
					}
				);

				if (import.meta.env.DEV) {
					console.log('[Token Refresh] 방법 3 성공 (본문만)');
				}
			}
		}

		// 응답 데이터 검증 및 구조 파싱
		if (!response.data || !response.data.success) {
			throw createRefreshError(
				'INVALID_RESPONSE',
				'서버에서 실패 응답을 받았습니다',
				response.data
			);
		}

		// 토큰 갱신 응답 구조: { success: true, data: { access_token, expires_in, token_type } }
		const responseData = response.data.data;
		if (!responseData || !responseData.access_token) {
			throw createRefreshError(
				'INVALID_RESPONSE',
				'서버에서 유효한 토큰 응답을 받지 못했습니다',
				response.data
			);
		}

		// TokenResponse 형태로 변환
		const tokenResponse: TokenResponse = {
			access_token: responseData.access_token,
			refresh_token: refreshToken, // 기존 리프레시 토큰 유지
			token_type: responseData.token_type || 'Bearer',
			expires_in: responseData.expires_in || 900 // 기본값 15분
		};

		if (import.meta.env.DEV) {
			console.log('[Token Refresh] 서버 응답 파싱 완료:', {
				hasAccessToken: !!tokenResponse.access_token,
				hasRefreshToken: !!tokenResponse.refresh_token,
				expiresIn: tokenResponse.expires_in,
				tokenType: tokenResponse.token_type
			});
		}

		// 새로운 토큰들 저장
		// 토큰 갱신 시에는 보통 액세스 토큰만 새로 발급되고 리프레시 토큰은 기존 것을 유지
		await tokenService.storeTokens(tokenResponse.access_token, tokenResponse.refresh_token);

		if (import.meta.env.DEV) {
			console.log('[Token Refresh] 액세스 토큰 갱신 완료 (리프레시 토큰 유지)');
		}

		if (import.meta.env.DEV) {
			console.log('[Token Refresh] 토큰 갱신 성공 - 새 토큰 저장 완료');
		}

		return tokenResponse.access_token;
	} catch (error) {
		if (import.meta.env.DEV) {
			console.error('[Token Refresh] 토큰 갱신 실패 상세:', {
				error,
				isAxiosError: axios.isAxiosError(error),
				status: axios.isAxiosError(error) ? error.response?.status : 'N/A',
				data: axios.isAxiosError(error) ? error.response?.data : 'N/A',
				message: error instanceof Error ? error.message : 'Unknown error'
			});
		}

		if (axios.isAxiosError(error)) {
			if (error.response?.status === 401) {
				// 리프레시 토큰도 만료됨
				throw createRefreshError('REFRESH_FAILED', '리프레시 토큰이 만료되었습니다', error);
			} else if (error.code === 'ECONNABORTED' || error.code === 'ETIMEDOUT') {
				// 네트워크 타임아웃
				throw createRefreshError('NETWORK_ERROR', '네트워크 연결 시간이 초과되었습니다', error);
			} else if (!error.response) {
				// 네트워크 에러
				throw createRefreshError('NETWORK_ERROR', '네트워크 연결에 실패했습니다', error);
			} else {
				// 기타 HTTP 에러
				throw createRefreshError(
					'REFRESH_FAILED',
					`토큰 갱신 실패: ${error.response.status} - ${error.response.data?.message || 'Unknown error'}`,
					error
				);
			}
		} else if (error && typeof error === 'object' && 'type' in error && 'message' in error) {
			// 이미 TokenRefreshError인 경우 그대로 전파
			throw error;
		} else {
			// 기타 에러
			throw createRefreshError('REFRESH_FAILED', '알 수 없는 오류가 발생했습니다', error);
		}
	}
}

/**
 * 토큰 자동 갱신 서비스
 * 함수형 프로그래밍 패턴을 따라 객체 리터럴로 구현
 */
export const tokenRefreshService = {
	/**
	 * 액세스 토큰을 갱신합니다.
	 * 동시 요청 시 중복 갱신을 방지합니다.
	 * @returns 새로운 액세스 토큰 또는 null
	 */
	async refreshAccessToken(): Promise<string | null> {
		// 이미 갱신 중인 경우 기존 Promise를 반환
		if (refreshState.isRefreshing && refreshState.refreshPromise) {
			return refreshState.refreshPromise;
		}

		// 새로운 갱신 프로세스 시작
		refreshState.isRefreshing = true;
		refreshState.refreshPromise = performTokenRefresh()
			.then((token) => {
				processFailedQueue(null, token);
				return token;
			})
			.catch((error) => {
				processFailedQueue(error, null);
				throw error;
			})
			.finally(() => {
				refreshState.isRefreshing = false;
				refreshState.refreshPromise = null;
			});

		return refreshState.refreshPromise;
	},

	/**
	 * 토큰 갱신이 필요한 경우 대기열에 추가합니다.
	 * @returns 새로운 액세스 토큰을 반환하는 Promise
	 */
	async waitForTokenRefresh(): Promise<string | null> {
		if (!refreshState.isRefreshing) {
			// 갱신 중이 아니면 즉시 갱신 시작
			return this.refreshAccessToken();
		}

		// 갱신 중이면 대기열에 추가
		return new Promise((resolve, reject) => {
			refreshState.failedQueue.push({ resolve, reject });
		});
	},

	/**
	 * 토큰 갱신 실패 시 로그아웃 처리를 수행합니다.
	 * @param error 토큰 갱신 에러
	 */
	async handleRefreshFailure(error: TokenRefreshError): Promise<void> {
		let logoutReason = '토큰 갱신 실패';

		switch (error.type) {
			case 'NO_REFRESH_TOKEN':
				logoutReason = '리프레시 토큰 없음';
				break;
			case 'REFRESH_FAILED':
				logoutReason = '리프레시 토큰 만료';
				break;
			case 'NETWORK_ERROR':
				logoutReason = '네트워크 오류';
				// 네트워크 오류의 경우 사용자에게 알림
				if (typeof window !== 'undefined') {
					console.warn('[Token Refresh] 네트워크 오류로 인한 로그아웃:', error.message);
				}
				break;
			case 'INVALID_RESPONSE':
				logoutReason = '서버 응답 오류';
				break;
		}

		await performLogout(logoutReason);
	},

	/**
	 * 토큰이 곧 만료되는지 확인하고 필요시 갱신합니다.
	 * @param bufferMinutes 여유 시간 (분 단위, 기본값: 5분)
	 * @returns 갱신된 토큰 또는 기존 토큰
	 */
	async ensureValidToken(bufferMinutes: number = 5): Promise<string | null> {
		try {
			// 현재 토큰이 곧 만료되는지 확인
			const isExpiringSoon = await tokenService.isAccessTokenExpiringSoon(bufferMinutes);

			if (isExpiringSoon) {
				if (import.meta.env.DEV) {
					console.log(
						`[Token Refresh] 토큰이 ${bufferMinutes}분 이내에 만료되어 갱신을 시도합니다`
					);
				}
				return await this.refreshAccessToken();
			}

			// 현재 토큰이 유효하면 그대로 반환
			return await tokenService.getAccessToken();
		} catch (error) {
			console.error('[Token Refresh] 토큰 유효성 확인 실패:', error);
			return null;
		}
	},

	/**
	 * 백그라운드에서 주기적으로 토큰을 확인하고 필요시 갱신합니다.
	 * @param intervalMinutes 확인 주기 (분 단위, 기본값: 2분)
	 * @param bufferMinutes 갱신 여유 시간 (분 단위, 기본값: 10분)
	 * @returns 인터벌 ID (정리 시 사용)
	 */
	startPeriodicRefresh(intervalMinutes: number = 2, bufferMinutes: number = 10): NodeJS.Timeout {
		const intervalMs = intervalMinutes * 60 * 1000;

		return setInterval(async () => {
			try {
				// 현재 인증 상태 확인
				const isAuthenticated = await tokenService.isAuthenticated();
				if (!isAuthenticated) {
					return; // 인증되지 않은 상태면 갱신하지 않음
				}

				// 토큰 갱신 필요성 확인
				const isExpiringSoon = await tokenService.isAccessTokenExpiringSoon(bufferMinutes);
				if (isExpiringSoon) {
					if (import.meta.env.DEV) {
						console.log('[Token Refresh] 백그라운드 토큰 갱신 시작');
					}

					await this.refreshAccessToken();
				}
			} catch (error) {
				console.error('[Token Refresh] 백그라운드 토큰 갱신 실패:', error);
			}
		}, intervalMs);
	},

	/**
	 * 현재 토큰 갱신 상태를 반환합니다.
	 * @returns 갱신 중 여부
	 */
	isRefreshing(): boolean {
		return refreshState.isRefreshing;
	},

	/**
	 * 대기 중인 요청 수를 반환합니다.
	 * @returns 대기 중인 요청 수
	 */
	getQueueLength(): number {
		return refreshState.failedQueue.length;
	}
};

/**
 * 액세스 토큰 갱신 함수 (편의 함수)
 * @returns 새로운 액세스 토큰 또는 null
 */
export async function refreshAccessToken(): Promise<string | null> {
	return tokenRefreshService.refreshAccessToken();
}

/**
 * 토큰 갱신 대기 함수 (편의 함수)
 * @returns 새로운 액세스 토큰을 반환하는 Promise
 */
export async function waitForTokenRefresh(): Promise<string | null> {
	return tokenRefreshService.waitForTokenRefresh();
}

/**
 * 유효한 토큰 보장 함수 (편의 함수)
 * @param bufferMinutes 여유 시간 (분 단위)
 * @returns 유효한 토큰 또는 null
 */
export async function ensureValidToken(bufferMinutes?: number): Promise<string | null> {
	return tokenRefreshService.ensureValidToken(bufferMinutes);
}
