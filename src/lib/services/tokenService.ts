/**
 * 통합 토큰 관리 서비스
 * JWT 토큰의 저장, 조회, 삭제, 유효성 검사 등 모든 토큰 관련 기능을 제공합니다.
 */

import { getTokenStorage } from './tokenStorage';
import {
	isTokenValid,
	isTokenExpired,
	getTokenRemainingTime,
	getUserIdFromToken,
	debugToken
} from '$lib/utils/jwtUtils';
import { getCurrentPlatform, debugPlatform } from './platformService';
import type { TokenResponse, TokenStatus, TokenStorage } from '$lib/types/auth';

/**
 * 토큰 서비스 클래스
 * 함수형 프로그래밍 패턴을 따라 객체 리터럴로 구현
 */
export const tokenService = {
	storage: null as TokenStorage | null,

	/**
	 * 토큰 서비스를 초기화합니다.
	 */
	async initialize(): Promise<void> {
		try {
			this.storage = getTokenStorage();
			await this.storage.initialize();

			if (import.meta.env.DEV) {
				console.log('[Token Service] 초기화 완료');
				debugPlatform();
			}
		} catch (error) {
			console.error('[Token Service] 초기화 실패:', error);
			throw error;
		}
	},

	/**
	 * 저장소 인스턴스를 확인하고 필요시 초기화합니다.
	 */
	async ensureStorage(): Promise<TokenStorage> {
		if (!this.storage) {
			await this.initialize();
		}
		return this.storage!;
	},

	/**
	 * 토큰 응답을 처리하여 저장합니다.
	 * @param tokenResponse 서버에서 받은 토큰 응답
	 */
	async storeTokenResponse(tokenResponse: TokenResponse): Promise<void> {
		try {
			if (import.meta.env.DEV) {
				console.log('[Token Service] 토큰 응답 저장 시작:', {
					hasAccessToken: !!tokenResponse.access_token,
					hasRefreshToken: !!tokenResponse.refresh_token,
					accessTokenLength: tokenResponse.access_token?.length || 0,
					refreshTokenLength: tokenResponse.refresh_token?.length || 0
				});
			}

			const storage = await this.ensureStorage();

			// 토큰 저장
			await storage.storeTokens(tokenResponse.access_token, tokenResponse.refresh_token);

			// 저장 후 즉시 확인 (개발 환경에서만)
			if (import.meta.env.DEV) {
				const storedAccess = await storage.getAccessToken();
				const storedRefresh = await storage.getRefreshToken();
				console.log('[Token Service] 토큰 저장 후 즉시 확인:', {
					accessTokenStored: !!storedAccess,
					refreshTokenStored: !!storedRefresh,
					accessTokenMatches: storedAccess === tokenResponse.access_token,
					refreshTokenMatches: storedRefresh === tokenResponse.refresh_token
				});
			}

			if (import.meta.env.DEV) {
				console.log('[Token Service] 토큰 응답 저장 완료');
			}
		} catch (error) {
			console.error('[Token Service] 토큰 응답 저장 실패:', error);
			throw error;
		}
	},

	/**
	 * 액세스 토큰을 저장합니다.
	 * @param token 액세스 토큰
	 */
	async storeAccessToken(token: string): Promise<void> {
		try {
			const storage = await this.ensureStorage();
			await storage.storeAccessToken(token);
		} catch (error) {
			console.error('[Token Service] 액세스 토큰 저장 실패:', error);
			throw error;
		}
	},

	/**
	 * 리프레시 토큰을 저장합니다.
	 * @param token 리프레시 토큰
	 */
	async storeRefreshToken(token: string): Promise<void> {
		try {
			const storage = await this.ensureStorage();
			await storage.storeRefreshToken(token);
		} catch (error) {
			console.error('[Token Service] 리프레시 토큰 저장 실패:', error);
			throw error;
		}
	},

	/**
	 * 두 토큰을 한 번에 저장합니다.
	 * @param accessToken 액세스 토큰
	 * @param refreshToken 리프레시 토큰
	 */
	async storeTokens(accessToken: string, refreshToken: string): Promise<void> {
		try {
			const storage = await this.ensureStorage();
			await storage.storeTokens(accessToken, refreshToken);

			if (import.meta.env.DEV) {
				console.log('[Token Service] 토큰 저장 완료');
			}
		} catch (error) {
			console.error('[Token Service] 토큰 저장 실패:', error);
			throw error;
		}
	},

	/**
	 * 유효한 액세스 토큰을 조회합니다.
	 * @returns 유효한 액세스 토큰 또는 null
	 */
	async getAccessToken(): Promise<string | null> {
		try {
			const storage = await this.ensureStorage();
			const token = await storage.getAccessToken();

			if (token && isTokenValid(token)) {
				return token;
			}

			// 유효하지 않은 토큰이 있으면 삭제
			if (token) {
				await storage.storeAccessToken('');
				if (import.meta.env.DEV) {
					console.log('[Token Service] 유효하지 않은 액세스 토큰 삭제됨');
				}
			}

			return null;
		} catch (error) {
			console.error('[Token Service] 액세스 토큰 조회 실패:', error);
			return null;
		}
	},

	/**
	 * 유효한 리프레시 토큰을 조회합니다.
	 * @returns 유효한 리프레시 토큰 또는 null
	 */
	async getRefreshToken(): Promise<string | null> {
		try {
			const storage = await this.ensureStorage();
			const token = await storage.getRefreshToken();

			// 리프레시 토큰은 JWT가 아닐 수 있으므로 존재 여부만 확인
			if (token && token.trim().length > 0) {
				// JWT 형식인 경우에만 유효성 검사
				if (token.split('.').length === 3) {
					// 리프레시 토큰은 버퍼 없이 정확한 만료 시간으로 검사
					if (isTokenValid(token, 0)) {
						return token;
					} else {
						// JWT 형식이지만 유효하지 않은 경우
						if (import.meta.env.DEV) {
							console.log('[Token Service] 만료된 JWT 리프레시 토큰 감지됨');
							debugToken(token, 'Expired Refresh Token');
						}

						// 만료된 토큰 삭제
						await storage.storeRefreshToken('');
						return null;
					}
				} else {
					// JWT가 아닌 리프레시 토큰은 그대로 반환 (Laravel Sanctum 등)
					if (import.meta.env.DEV) {
						console.log('[Token Service] Non-JWT 리프레시 토큰 반환');
					}
					return token;
				}
			}

			return null;
		} catch (error) {
			console.error('[Token Service] 리프레시 토큰 조회 실패:', error);
			return null;
		}
	},

	/**
	 * 저장된 모든 토큰을 삭제합니다.
	 */
	async clearTokens(): Promise<void> {
		try {
			const storage = await this.ensureStorage();
			await storage.clearTokens();

			if (import.meta.env.DEV) {
				console.log('[Token Service] 모든 토큰 삭제 완료');
			}
		} catch (error) {
			console.error('[Token Service] 토큰 삭제 실패:', error);
			throw error;
		}
	},

	/**
	 * 액세스 토큰이 유효한지 확인합니다.
	 * @returns 유효성 여부
	 */
	async isAccessTokenValid(): Promise<boolean> {
		const token = await this.getAccessToken();
		return token !== null;
	},

	/**
	 * 리프레시 토큰이 유효한지 확인합니다.
	 * @returns 유효성 여부
	 */
	async isRefreshTokenValid(): Promise<boolean> {
		try {
			const token = await this.getRefreshToken();
			if (!token) return false;

			// JWT 형식인지 확인
			if (token.split('.').length !== 3) {
				// JWT가 아닌 리프레시 토큰은 존재하면 유효한 것으로 처리
				return true;
			}

			// JWT 토큰 파싱하여 만료 시간 확인
			const { decodeJWT } = await import('$lib/utils/jwtUtils');
			const payload = decodeJWT(token);
			if (!payload || !payload.exp) {
				// 파싱 실패하거나 만료 시간이 없으면 유효하지 않음
				return false;
			}

			const now = Math.floor(Date.now() / 1000);
			const isValid = payload.exp > now;

			if (import.meta.env.DEV) {
				const remainingTime = payload.exp - now;
				console.log('[Token Service] 리프레시 토큰 유효성 검사:', {
					isValid,
					remainingSeconds: remainingTime,
					remainingMinutes: Math.floor(remainingTime / 60)
				});
			}

			return isValid;
		} catch (error) {
			console.error('[Token Service] 리프레시 토큰 유효성 검사 실패:', error);
			return false;
		}
	},

	/**
	 * 액세스 토큰이 곧 만료되는지 확인합니다.
	 * @param bufferMinutes 여유 시간 (분 단위, 기본값: 5분)
	 * @returns 곧 만료 여부
	 */
	async isAccessTokenExpiringSoon(bufferMinutes: number = 5): Promise<boolean> {
		try {
			const storage = await this.ensureStorage();
			const token = await storage.getAccessToken();

			if (!token) {
				return true; // 토큰이 없으면 만료된 것으로 처리
			}

			// JWT 형식 확인
			if (token.split('.').length !== 3) {
				return false; // JWT가 아니면 만료 확인 불가
			}

			const remainingTime = getTokenRemainingTime(token);
			const bufferSeconds = bufferMinutes * 60;

			if (import.meta.env.DEV) {
				console.log(
					`[Token Service] 액세스 토큰 만료 확인: ${remainingTime}초 남음, 버퍼: ${bufferSeconds}초`
				);
			}

			return remainingTime <= bufferSeconds;
		} catch (error) {
			console.error('[Token Service] 토큰 만료 확인 실패:', error);
			return true; // 오류 시 만료된 것으로 처리
		}
	},

	/**
	 * 현재 토큰 상태를 조회합니다.
	 * @returns 토큰 상태 정보
	 */
	async getTokenStatus(): Promise<TokenStatus> {
		try {
			const storage = await this.ensureStorage();
			const accessToken = await storage.getAccessToken();
			const refreshToken = await storage.getRefreshToken();

			const status: TokenStatus = {
				hasAccessToken: !!accessToken,
				hasRefreshToken: !!refreshToken,
				isAccessTokenValid: accessToken ? isTokenValid(accessToken) : false,
				isRefreshTokenValid: refreshToken
					? // JWT 형식인 경우에만 유효성 검사, 아니면 존재 여부만 확인
						refreshToken.split('.').length === 3
						? isTokenValid(refreshToken)
						: true
					: false,
				accessTokenRemainingTime: accessToken ? getTokenRemainingTime(accessToken) : 0,
				refreshTokenRemainingTime:
					refreshToken && refreshToken.split('.').length === 3
						? getTokenRemainingTime(refreshToken)
						: 0, // JWT가 아닌 리프레시 토큰은 만료시간 없음
				userId: accessToken ? getUserIdFromToken(accessToken) : null
			};

			if (import.meta.env.DEV) {
				console.log('[Token Service] 토큰 상태:', status);
			}

			return status;
		} catch (error) {
			console.error('[Token Service] 토큰 상태 조회 실패:', error);
			return {
				hasAccessToken: false,
				hasRefreshToken: false,
				isAccessTokenValid: false,
				isRefreshTokenValid: false,
				accessTokenRemainingTime: 0,
				refreshTokenRemainingTime: 0,
				userId: null
			};
		}
	},

	/**
	 * 사용자가 인증되어 있는지 확인합니다.
	 * @returns 인증 여부
	 */
	async isAuthenticated(): Promise<boolean> {
		const status = await this.getTokenStatus();
		return status.isAccessTokenValid || status.isRefreshTokenValid;
	},

	/**
	 * 현재 사용자 ID를 조회합니다.
	 * @returns 사용자 ID 또는 null
	 */
	async getCurrentUserId(): Promise<string | null> {
		const token = await this.getAccessToken();
		return token ? getUserIdFromToken(token) : null;
	},

	/**
	 * 토큰 정보를 디버그 출력합니다 (개발 환경에서만).
	 */
	async debugTokens(): Promise<void> {
		if (import.meta.env.DEV) {
			const status = await this.getTokenStatus();
			console.log('[Token Service Debug]', {
				platform: getCurrentPlatform(),
				...status
			});

			const storage = await this.ensureStorage();
			const accessToken = await storage.getAccessToken();
			const refreshToken = await storage.getRefreshToken();

			if (accessToken) {
				debugToken(accessToken, 'Current Access Token');
			}
			if (refreshToken) {
				debugToken(refreshToken, 'Current Refresh Token');
			}
		}
	}
};

/**
 * 토큰 서비스 초기화 함수
 * 앱 시작 시 호출하여 토큰 서비스를 초기화합니다.
 */
export async function initializeTokenService(): Promise<void> {
	await tokenService.initialize();
}

/**
 * 토큰 응답 저장 함수
 * @param tokenResponse 서버에서 받은 토큰 응답
 */
export async function storeTokenResponse(tokenResponse: TokenResponse): Promise<void> {
	await tokenService.storeTokenResponse(tokenResponse);
}

/**
 * 유효한 액세스 토큰 조회 함수
 * @returns 유효한 액세스 토큰 또는 null
 */
export async function getValidAccessToken(): Promise<string | null> {
	return await tokenService.getAccessToken();
}

/**
 * 유효한 리프레시 토큰 조회 함수
 * @returns 유효한 리프레시 토큰 또는 null
 */
export async function getValidRefreshToken(): Promise<string | null> {
	return await tokenService.getRefreshToken();
}

/**
 * 모든 토큰 삭제 함수
 */
export async function clearAllTokens(): Promise<void> {
	await tokenService.clearTokens();
}

/**
 * 인증 상태 확인 함수
 * @returns 인증 여부
 */
export async function isUserAuthenticated(): Promise<boolean> {
	return await tokenService.isAuthenticated();
}

/**
 * 토큰 만료 임박 확인 함수
 * @param bufferMinutes 여유 시간 (분 단위)
 * @returns 만료 임박 여부
 */
export async function isTokenExpiringSoon(bufferMinutes?: number): Promise<boolean> {
	return await tokenService.isAccessTokenExpiringSoon(bufferMinutes);
}
