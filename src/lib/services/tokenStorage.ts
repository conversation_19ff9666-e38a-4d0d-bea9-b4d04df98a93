/**
 * 토큰 저장소 서비스
 * 플랫폼별 안전한 토큰 저장, 조회, 삭제 기능을 제공합니다.
 */

import { getCurrentPlatform, getStorageType, isTauri } from './platformService';
import { isTokenValid, debugToken } from '$lib/utils/jwtUtils';
import type { TokenStorage } from '$lib/types/auth';

/**
 * 메모리 기반 토큰 캐시
 * 런타임 중 토큰을 메모리에 캐시하여 빠른 접근을 제공합니다.
 */
function createMemoryTokenCache() {
	let accessToken: string | null = null;
	let refreshToken: string | null = null;

	return {
		setAccessToken(token: string | null): void {
			accessToken = token;
			if (import.meta.env.DEV && token) {
				debugToken(token, 'Memory Cache - Access Token');
			}
		},

		setRefreshToken(token: string | null): void {
			refreshToken = token;
			if (import.meta.env.DEV && token) {
				debugToken(token, 'Memory Cache - Refresh Token');
			}
		},

		getAccessToken(): string | null {
			return accessToken;
		},

		getRefreshToken(): string | null {
			return refreshToken;
		},

		clear(): void {
			accessToken = null;
			refreshToken = null;
			if (import.meta.env.DEV) {
				console.log('[Memory Cache] 토큰 캐시 클리어됨');
			}
		},

		hasValidAccessToken(): boolean {
			return accessToken !== null && isTokenValid(accessToken);
		}
	};
}

/**
 * Tauri Store API 기반 토큰 저장소 (데스크탑용)
 */
function createTauriStoreTokenStorage(): TokenStorage {
	const cache = createMemoryTokenCache();
	let store: any = null;

	return {
		async initialize(): Promise<void> {
			try {
				if (!isTauri()) {
					throw new Error('Tauri 환경이 아닙니다');
				}

				// Tauri Store API 동적 import
				const { LazyStore } = await import('@tauri-apps/plugin-store');
				store = new LazyStore('.auth.dat');

				if (import.meta.env.DEV) {
					console.log('[Tauri Store] 토큰 저장소 초기화 완료');
				}
			} catch (error) {
				console.error('[Tauri Store] 초기화 실패:', error);
				throw error;
			}
		},

		async storeAccessToken(token: string): Promise<void> {
			try {
				if (!store) {
					await this.initialize();
				}

				await store.set('access_token', token);
				await store.save();
				cache.setAccessToken(token);

				if (import.meta.env.DEV) {
					console.log('[Tauri Store] 액세스 토큰 저장 완료');
				}
			} catch (error) {
				console.error('[Tauri Store] 액세스 토큰 저장 실패:', error);
				throw error;
			}
		},

		async storeRefreshToken(token: string): Promise<void> {
			try {
				if (!store) {
					await this.initialize();
				}

				await store.set('refresh_token', token);
				await store.save();
				cache.setRefreshToken(token);

				if (import.meta.env.DEV) {
					console.log('[Tauri Store] 리프레시 토큰 저장 완료');
				}
			} catch (error) {
				console.error('[Tauri Store] 리프레시 토큰 저장 실패:', error);
				throw error;
			}
		},

		async storeTokens(accessToken: string, refreshToken: string): Promise<void> {
			await Promise.all([this.storeAccessToken(accessToken), this.storeRefreshToken(refreshToken)]);
		},

		async getAccessToken(): Promise<string | null> {
			try {
				// 캐시에서 유효한 토큰이 있으면 반환
				if (cache.hasValidAccessToken()) {
					return cache.getAccessToken();
				}

				if (!store) {
					await this.initialize();
				}

				const token = await store.get('access_token');
				if (token && isTokenValid(token)) {
					cache.setAccessToken(token);
					return token;
				}

				// 유효하지 않은 토큰은 삭제
				if (token) {
					await store.delete('access_token');
					await store.save();
				}

				cache.setAccessToken(null);
				return null;
			} catch (error) {
				console.error('[Tauri Store] 액세스 토큰 조회 실패:', error);
				return null;
			}
		},

		async getRefreshToken(): Promise<string | null> {
			try {
				// 캐시 확인 (리프레시 토큰은 존재 여부만 확인)
				const cachedToken = cache.getRefreshToken();
				if (cachedToken && cachedToken.trim().length > 0) {
					return cachedToken;
				}

				if (!store) {
					await this.initialize();
				}

				const token = await store.get('refresh_token');

				if (import.meta.env.DEV) {
					console.log('[Tauri Store] 리프레시 토큰 조회:', {
						exists: !!token,
						length: token?.length || 0,
						isJWT: token ? token.split('.').length === 3 : false
					});
				}

				if (token && token.trim().length > 0) {
					// 리프레시 토큰은 JWT 형식인 경우에만 유효성 검사
					if (token.split('.').length === 3) {
						// JWT 형식인 경우 버퍼 없이 정확한 만료 시간으로 검사
						if (isTokenValid(token, 0)) {
							cache.setRefreshToken(token);
							return token;
						} else {
							// JWT 형식이지만 만료된 경우
							if (import.meta.env.DEV) {
								console.log('[Tauri Store] 만료된 JWT 리프레시 토큰 삭제');
								debugToken(token, 'Expired JWT Refresh Token');
							}
							await store.delete('refresh_token');
							await store.save();
							cache.setRefreshToken(null);
							return null;
						}
					} else {
						// JWT가 아닌 리프레시 토큰은 그대로 반환 (Laravel Sanctum 등)
						if (import.meta.env.DEV) {
							console.log('[Tauri Store] Non-JWT 리프레시 토큰 반환');
						}
						cache.setRefreshToken(token);
						return token;
					}
				}

				cache.setRefreshToken(null);
				return null;
			} catch (error) {
				console.error('[Tauri Store] 리프레시 토큰 조회 실패:', error);
				return null;
			}
		},

		async clearTokens(): Promise<void> {
			try {
				if (!store) {
					await this.initialize();
				}

				await store.delete('access_token');
				await store.delete('refresh_token');
				await store.save();
				cache.clear();

				if (import.meta.env.DEV) {
					console.log('[Tauri Store] 모든 토큰 삭제 완료');
				}
			} catch (error) {
				console.error('[Tauri Store] 토큰 삭제 실패:', error);
				throw error;
			}
		}
	};
}

/**
 * 안드로이드 Keystore 기반 토큰 저장소
 * 현재는 Tauri Store를 사용하되, 향후 Android Keystore 연동 예정
 */
function createAndroidKeystoreTokenStorage(): TokenStorage {
	const cache = createMemoryTokenCache();
	let store: any = null;

	return {
		async initialize(): Promise<void> {
			try {
				if (!isTauri()) {
					throw new Error('Tauri 환경이 아닙니다');
				}

				// 현재는 Tauri Store 사용, 향후 Android Keystore 연동
				const { LazyStore } = await import('@tauri-apps/plugin-store');
				store = new LazyStore('.auth-android.dat');

				if (import.meta.env.DEV) {
					console.log('[Android Keystore] 토큰 저장소 초기화 완료 (Tauri Store 사용)');
				}
			} catch (error) {
				console.error('[Android Keystore] 초기화 실패:', error);
				throw error;
			}
		},

		async storeAccessToken(token: string): Promise<void> {
			try {
				if (!store) {
					await this.initialize();
				}

				await store.set('access_token', token);
				await store.save();
				cache.setAccessToken(token);

				if (import.meta.env.DEV) {
					console.log('[Android Keystore] 액세스 토큰 저장 완료');
				}
			} catch (error) {
				console.error('[Android Keystore] 액세스 토큰 저장 실패:', error);
				throw error;
			}
		},

		async storeRefreshToken(token: string): Promise<void> {
			try {
				if (!store) {
					await this.initialize();
				}

				await store.set('refresh_token', token);
				await store.save();
				cache.setRefreshToken(token);

				if (import.meta.env.DEV) {
					console.log('[Android Keystore] 리프레시 토큰 저장 완료');
				}
			} catch (error) {
				console.error('[Android Keystore] 리프레시 토큰 저장 실패:', error);
				throw error;
			}
		},

		async storeTokens(accessToken: string, refreshToken: string): Promise<void> {
			await Promise.all([this.storeAccessToken(accessToken), this.storeRefreshToken(refreshToken)]);
		},

		async getAccessToken(): Promise<string | null> {
			try {
				// 캐시에서 유효한 토큰이 있으면 반환
				if (cache.hasValidAccessToken()) {
					return cache.getAccessToken();
				}

				if (!store) {
					await this.initialize();
				}

				const token = await store.get('access_token');
				if (token && isTokenValid(token)) {
					cache.setAccessToken(token);
					return token;
				}

				// 유효하지 않은 토큰은 삭제
				if (token) {
					await store.delete('access_token');
					await store.save();
				}

				cache.setAccessToken(null);
				return null;
			} catch (error) {
				console.error('[Android Keystore] 액세스 토큰 조회 실패:', error);
				return null;
			}
		},

		async getRefreshToken(): Promise<string | null> {
			try {
				// 캐시 확인 (리프레시 토큰은 존재 여부만 확인)
				const cachedToken = cache.getRefreshToken();
				if (cachedToken && cachedToken.trim().length > 0) {
					return cachedToken;
				}

				if (!store) {
					await this.initialize();
				}

				const token = await store.get('refresh_token');

				if (import.meta.env.DEV) {
					console.log('[Android Keystore] 리프레시 토큰 조회:', {
						exists: !!token,
						length: token?.length || 0,
						isJWT: token ? token.split('.').length === 3 : false
					});
				}

				if (token && token.trim().length > 0) {
					// 리프레시 토큰은 JWT 형식인 경우에만 유효성 검사
					if (token.split('.').length === 3) {
						// JWT 형식인 경우 버퍼 없이 정확한 만료 시간으로 검사
						if (isTokenValid(token, 0)) {
							cache.setRefreshToken(token);
							return token;
						} else {
							// JWT 형식이지만 만료된 경우
							if (import.meta.env.DEV) {
								console.log('[Android Keystore] 만료된 JWT 리프레시 토큰 삭제');
								debugToken(token, 'Expired JWT Refresh Token');
							}
							await store.delete('refresh_token');
							await store.save();
							cache.setRefreshToken(null);
							return null;
						}
					} else {
						// JWT가 아닌 리프레시 토큰은 그대로 반환
						if (import.meta.env.DEV) {
							console.log('[Android Keystore] Non-JWT 리프레시 토큰 반환');
						}
						cache.setRefreshToken(token);
						return token;
					}
				}

				cache.setRefreshToken(null);
				return null;
			} catch (error) {
				console.error('[Android Keystore] 리프레시 토큰 조회 실패:', error);
				return null;
			}
		},

		async clearTokens(): Promise<void> {
			try {
				if (!store) {
					await this.initialize();
				}

				await store.delete('access_token');
				await store.delete('refresh_token');
				await store.save();
				cache.clear();

				if (import.meta.env.DEV) {
					console.log('[Android Keystore] 모든 토큰 삭제 완료');
				}
			} catch (error) {
				console.error('[Android Keystore] 토큰 삭제 실패:', error);
				throw error;
			}
		}
	};
}

/**
 * LocalStorage 기반 토큰 저장소 (웹용 - 보안성 낮음)
 */
function createLocalStorageTokenStorage(): TokenStorage {
	const cache = createMemoryTokenCache();

	return {
		async initialize(): Promise<void> {
			if (import.meta.env.DEV) {
				console.log('[LocalStorage] 토큰 저장소 초기화 완료 (보안성 낮음)');
			}
		},

		async storeAccessToken(token: string): Promise<void> {
			try {
				localStorage.setItem('cnsprowms_access_token', token);
				cache.setAccessToken(token);

				if (import.meta.env.DEV) {
					console.log('[LocalStorage] 액세스 토큰 저장 완료');
				}
			} catch (error) {
				console.error('[LocalStorage] 액세스 토큰 저장 실패:', error);
				throw error;
			}
		},

		async storeRefreshToken(token: string): Promise<void> {
			try {
				localStorage.setItem('cnsprowms_refresh_token', token);
				cache.setRefreshToken(token);

				if (import.meta.env.DEV) {
					console.log('[LocalStorage] 리프레시 토큰 저장 완료');
				}
			} catch (error) {
				console.error('[LocalStorage] 리프레시 토큰 저장 실패:', error);
				throw error;
			}
		},

		async storeTokens(accessToken: string, refreshToken: string): Promise<void> {
			await Promise.all([this.storeAccessToken(accessToken), this.storeRefreshToken(refreshToken)]);
		},

		async getAccessToken(): Promise<string | null> {
			try {
				// 캐시에서 유효한 토큰이 있으면 반환
				if (cache.hasValidAccessToken()) {
					return cache.getAccessToken();
				}

				const token = localStorage.getItem('cnsprowms_access_token');
				if (token && isTokenValid(token)) {
					cache.setAccessToken(token);
					return token;
				}

				// 유효하지 않은 토큰은 삭제
				if (token) {
					localStorage.removeItem('cnsprowms_access_token');
				}

				cache.setAccessToken(null);
				return null;
			} catch (error) {
				console.error('[LocalStorage] 액세스 토큰 조회 실패:', error);
				return null;
			}
		},

		async getRefreshToken(): Promise<string | null> {
			try {
				// 캐시 확인 (리프레시 토큰은 존재 여부만 확인)
				const cachedToken = cache.getRefreshToken();
				if (cachedToken && cachedToken.trim().length > 0) {
					return cachedToken;
				}

				const token = localStorage.getItem('cnsprowms_refresh_token');

				if (import.meta.env.DEV) {
					console.log('[LocalStorage] 리프레시 토큰 조회:', {
						exists: !!token,
						length: token?.length || 0,
						isJWT: token ? token.split('.').length === 3 : false
					});
				}

				if (token && token.trim().length > 0) {
					// 리프레시 토큰은 JWT 형식인 경우에만 유효성 검사
					if (token.split('.').length === 3) {
						// JWT 형식인 경우 버퍼 없이 정확한 만료 시간으로 검사
						if (isTokenValid(token, 0)) {
							cache.setRefreshToken(token);
							return token;
						} else {
							// JWT 형식이지만 만료된 경우
							if (import.meta.env.DEV) {
								console.log('[LocalStorage] 만료된 JWT 리프레시 토큰 삭제');
								debugToken(token, 'Expired JWT Refresh Token');
							}
							localStorage.removeItem('cnsprowms_refresh_token');
							cache.setRefreshToken(null);
							return null;
						}
					} else {
						// JWT가 아닌 리프레시 토큰은 그대로 반환
						if (import.meta.env.DEV) {
							console.log('[LocalStorage] Non-JWT 리프레시 토큰 반환');
						}
						cache.setRefreshToken(token);
						return token;
					}
				}

				cache.setRefreshToken(null);
				return null;
			} catch (error) {
				console.error('[LocalStorage] 리프레시 토큰 조회 실패:', error);
				return null;
			}
		},

		async clearTokens(): Promise<void> {
			try {
				localStorage.removeItem('cnsprowms_access_token');
				localStorage.removeItem('cnsprowms_refresh_token');
				cache.clear();

				if (import.meta.env.DEV) {
					console.log('[LocalStorage] 모든 토큰 삭제 완료');
				}
			} catch (error) {
				console.error('[LocalStorage] 토큰 삭제 실패:', error);
				throw error;
			}
		}
	};
}

/**
 * 메모리 전용 토큰 저장소 (임시용)
 */
function createMemoryOnlyTokenStorage(): TokenStorage {
	const cache = createMemoryTokenCache();

	return {
		async initialize(): Promise<void> {
			if (import.meta.env.DEV) {
				console.log('[Memory Only] 토큰 저장소 초기화 완료 (메모리 전용)');
			}
		},

		async storeAccessToken(token: string): Promise<void> {
			cache.setAccessToken(token);
		},

		async storeRefreshToken(token: string): Promise<void> {
			cache.setRefreshToken(token);
		},

		async storeTokens(accessToken: string, refreshToken: string): Promise<void> {
			cache.setAccessToken(accessToken);
			cache.setRefreshToken(refreshToken);
		},

		async getAccessToken(): Promise<string | null> {
			return cache.getAccessToken();
		},

		async getRefreshToken(): Promise<string | null> {
			return cache.getRefreshToken();
		},

		async clearTokens(): Promise<void> {
			cache.clear();
		}
	};
}

/**
 * 플랫폼에 적합한 토큰 저장소 인스턴스를 생성합니다.
 * @returns 토큰 저장소 인스턴스
 */
export function createTokenStorage(): TokenStorage {
	const storageType = getStorageType();

	switch (storageType) {
		case 'tauri-store':
			return createTauriStoreTokenStorage();
		case 'android-keystore':
			return createAndroidKeystoreTokenStorage();
		case 'localstorage':
			return createLocalStorageTokenStorage();
		default:
			return createMemoryOnlyTokenStorage();
	}
}

// 싱글톤 인스턴스
let tokenStorageInstance: TokenStorage | null = null;

/**
 * 토큰 저장소 싱글톤 인스턴스를 반환합니다.
 * @returns 토큰 저장소 인스턴스
 */
export function getTokenStorage(): TokenStorage {
	if (!tokenStorageInstance) {
		tokenStorageInstance = createTokenStorage();
	}
	return tokenStorageInstance;
}
