// 데스크톱 통합 규칙 준수: Tauri 전용 코드 캡슐화
// - Tauri 환경: WebviewWindow 사용
// - 웹 환경: window.open 폴백
// - UI 컴포넌트에서 직접 Tauri 모듈을 임포트하지 않도록 서비스에 격리

/**
 * 인쇄(또는 프린트 미리보기)용 새 창을 연다.
 * @param url 열 주소 (필수)
 * @param label 창 레이블 (기본값: 'Print')
 * @param size 크기 옵션 { width=900, height=1000 }
 */
export async function openWebviewWindow(
  url: string,
  label: string = 'Print',
  size?: { width?: number; height?: number }
): Promise<void> {
  const width = size?.width ?? 900;
  const height = size?.height ?? 1000;

  // 브라우저 환경 감지
  const isBrowser = typeof window !== 'undefined';

  try {
    // Tauri 여부 체크 없이 먼저 동적 import 시도 → 실패 시 폴백
    const { WebviewWindow } = await import('@tauri-apps/api/webviewWindow');
    const webview = new WebviewWindow(label, { url, width, height });

    // 오류 이벤트 처리 (디버깅 목적) - 대기하지 않음
    webview.once('tauri://error', (e) => {
      console.error('WebviewWindow 생성 오류:', e);
    });
    return;
  } catch (e) {
    // Tauri import 실패 시 웹 환경으로 폴백
    console.warn('Tauri WebviewWindow 사용 불가, window.open으로 폴백합니다.', e);
  }

  // 웹 환경 폴백
  if (isBrowser) {
    const features = `noopener,noreferrer,width=${width},height=${height}`;
    window.open(url, '_blank', features);
  }
}
