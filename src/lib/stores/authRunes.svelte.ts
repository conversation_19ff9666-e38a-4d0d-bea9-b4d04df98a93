/**
 * Svelte 5 Runes 기반 인증 상태 관리
 *
 * 이 파일은 Svelte 5의 runes를 활용하여 더 현대적이고 반응형인
 * 인증 상태 관리를 제공합니다.
 */

import { browser } from '$app/environment';
import type { User } from '$lib/User';
import type { AuthState, AuthError, LoginCredentials } from './authStore';
import { authActions, authState } from './authStore';

/**
 * Svelte 5 runes를 활용한 인증 상태 관리 클래스
 *
 * 이 클래스는 $state, $derived 등의 runes를 사용하여
 * 컴포넌트에서 직접 사용할 수 있는 반응형 상태를 제공합니다.
 */
class AuthRunes {
	// 내부 상태 (private)
	private _state = $state<AuthState>({
		isAuthenticated: false,
		isInitialized: false,
		isLoading: false,
		user: null,
		accessToken: null,
		refreshToken: null,
		tokenExpiresAt: null,
		error: null,
		debugInfo: import.meta.env.DEV
			? {
					lastTokenRefresh: null,
					tokenRefreshCount: 0,
					platform: ''
				}
			: null
	});

	constructor() {
		// 브라우저 환경에서만 스토어 구독
		if (browser) {
			// authState 스토어의 변경사항을 runes 상태에 동기화
			authState.subscribe((state) => {
				this._state = { ...state };
			});
		}
	}

	// 읽기 전용 상태 접근자들
	get isAuthenticated() {
		return this._state.isAuthenticated;
	}
	get isInitialized() {
		return this._state.isInitialized;
	}
	get isLoading() {
		return this._state.isLoading;
	}
	get user() {
		return this._state.user;
	}
	get error() {
		return this._state.error;
	}
	get debugInfo() {
		return this._state.debugInfo;
	}

	// 파생된 상태들
	get hasUser() {
		return !!this._state.user;
	}
	get hasError() {
		return !!this._state.error;
	}
	get userName() {
		return this._state.user?.name || '';
	}
	get userRole() {
		return this._state.user?.role || '';
	}
	get userEmail() {
		return this._state.user?.email || '';
	}

	// 토큰 관련 파생 상태
	get hasTokens() {
		return !!(this._state.accessToken && this._state.refreshToken);
	}

	get isTokenExpiringSoon() {
		if (!this._state.tokenExpiresAt) return false;
		const now = new Date();
		const expiresAt = new Date(this._state.tokenExpiresAt);
		const timeDiff = expiresAt.getTime() - now.getTime();
		const fiveMinutes = 5 * 60 * 1000;
		return timeDiff <= fiveMinutes;
	}

	get tokenRemainingTime() {
		if (!this._state.tokenExpiresAt) return 0;
		const now = new Date();
		const expiresAt = new Date(this._state.tokenExpiresAt);
		return Math.max(0, expiresAt.getTime() - now.getTime());
	}

	// 에러 관련 파생 상태
	get errorMessage() {
		if (!this._state.error) return '';
		return authActions.getErrorMessage(this._state.error);
	}

	get isLoginError() {
		return this._state.error?.type === 'INVALID_CREDENTIALS';
	}

	get isNetworkError() {
		return this._state.error?.type === 'NETWORK_ERROR';
	}

	get isServerError() {
		return this._state.error?.type === 'SERVER_ERROR';
	}

	// 액션 메서드들 (authActions를 래핑)
	async initialize() {
		return await authActions.initialize();
	}

	async login(credentials: LoginCredentials) {
		return await authActions.login(credentials);
	}

	async logout() {
		return await authActions.logout();
	}

	async refreshAuth() {
		return await authActions.refreshAuth();
	}

	async loadUser() {
		return await authActions.loadUser();
	}

	updateUser(user: User) {
		authActions.updateUser(user);
	}

	clearError() {
		authActions.clearError();
	}

	async debugAuth() {
		return await authActions.debugAuth();
	}

	// 유틸리티 메서드들
	canAccess(requiredRole?: string): boolean {
		if (!this.isAuthenticated || !this.user) return false;
		if (!requiredRole) return true;
		return this.user.role === requiredRole || this.user.role === 'admin';
	}

	hasPermission(permission: string): boolean {
		if (!this.isAuthenticated || !this.user) return false;
		// 메뉴 권한 확인 (기존 User.ts 호환성)
		try {
			const menu = JSON.parse(this.user.menu || '[]');
			return menu.includes(permission);
		} catch {
			return false;
		}
	}

	getDisplayName(): string {
		if (!this.user) return '';
		return this.user.name || this.user.username || '';
	}

	getInitials(): string {
		const name = this.getDisplayName();
		if (!name) return '';

		const words = name.split(' ');
		if (words.length >= 2) {
			return (words[0][0] + words[1][0]).toUpperCase();
		}
		return name.substring(0, 2).toUpperCase();
	}
}

/**
 * 전역 인증 runes 인스턴스
 * 컴포넌트에서 직접 사용할 수 있습니다.
 */
export const auth = new AuthRunes();

/**
 * 컴포넌트에서 사용할 수 있는 인증 관련 유틸리티 함수들
 */

/**
 * 인증이 필요한 컴포넌트에서 사용하는 가드 함수
 * @param redirectTo 인증 실패 시 리다이렉트할 경로
 */
export function useAuthGuard(redirectTo: string = '/login') {
	const isAuthenticated = $derived(auth.isAuthenticated);
	const isInitialized = $derived(auth.isInitialized);

	$effect(() => {
		if (isInitialized && !isAuthenticated && browser) {
			import('$app/navigation').then(({ goto }) => {
				goto(redirectTo);
			});
		}
	});

	return { isAuthenticated, isInitialized };
}

/**
 * 로그인 폼에서 사용할 수 있는 헬퍼
 */
export function useLoginForm() {
	const isLoading = $derived(auth.isLoading);
	const error = $derived(auth.error);
	const errorMessage = $derived(auth.errorMessage);

	const login = async (credentials: LoginCredentials) => {
		try {
			await auth.login(credentials);
			return true;
		} catch (error) {
			console.error('로그인 실패:', error);
			return false;
		}
	};

	const clearError = () => {
		auth.clearError();
	};

	return {
		isLoading,
		error,
		errorMessage,
		login,
		clearError
	};
}

/**
 * 사용자 정보 표시에 사용할 수 있는 헬퍼
 */
export function useUserInfo() {
	const user = $derived(auth.user);
	const userName = $derived(auth.userName);
	const userRole = $derived(auth.userRole);
	const userEmail = $derived(auth.userEmail);
	const displayName = $derived(auth.getDisplayName());
	const initials = $derived(auth.getInitials());

	return {
		user,
		userName,
		userRole,
		userEmail,
		displayName,
		initials
	};
}

/**
 * 권한 확인에 사용할 수 있는 헬퍼
 */
export function usePermissions() {
	const canAccess = (requiredRole?: string) => auth.canAccess(requiredRole);
	const hasPermission = (permission: string) => auth.hasPermission(permission);

	return {
		canAccess,
		hasPermission
	};
}

/**
 * 토큰 상태 모니터링에 사용할 수 있는 헬퍼
 */
export function useTokenStatus() {
	const hasTokens = $derived(auth.hasTokens);
	const isTokenExpiringSoon = $derived(auth.isTokenExpiringSoon);
	const tokenRemainingTime = $derived(auth.tokenRemainingTime);

	// 토큰 만료 임박 시 자동 갱신
	$effect(() => {
		if (isTokenExpiringSoon && hasTokens) {
			auth.refreshAuth().catch(console.error);
		}
	});

	return {
		hasTokens,
		isTokenExpiringSoon,
		tokenRemainingTime
	};
}

/**
 * 에러 처리에 사용할 수 있는 헬퍼
 */
export function useAuthError() {
	const error = $derived(auth.error);
	const errorMessage = $derived(auth.errorMessage);
	const hasError = $derived(auth.hasError);
	const isLoginError = $derived(auth.isLoginError);
	const isNetworkError = $derived(auth.isNetworkError);
	const isServerError = $derived(auth.isServerError);

	const clearError = () => auth.clearError();

	return {
		error,
		errorMessage,
		hasError,
		isLoginError,
		isNetworkError,
		isServerError,
		clearError
	};
}

/**
 * 개발 환경에서 디버깅에 사용할 수 있는 헬퍼
 */
export function useAuthDebug() {
	if (!import.meta.env.DEV) {
		return {
			debugInfo: null,
			debugAuth: () => {},
			logState: () => {}
		};
	}

	const debugInfo = $derived(auth.debugInfo);

	const debugAuth = async () => {
		await auth.debugAuth();
	};

	const logState = () => {
		console.log('[Auth Runes Debug]', {
			isAuthenticated: auth.isAuthenticated,
			isInitialized: auth.isInitialized,
			isLoading: auth.isLoading,
			hasUser: auth.hasUser,
			hasTokens: auth.hasTokens,
			hasError: auth.hasError,
			debugInfo: auth.debugInfo
		});
	};

	return {
		debugInfo,
		debugAuth,
		logState
	};
}
