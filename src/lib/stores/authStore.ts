/**
 * JWT 인증 상태 관리 스토어 (Svelte 5 호환)
 *
 * 이 스토어는 JWT 토큰 기반 인증 시스템의 상태를 관리합니다.
 * Svelte 5의 runes를 활용하여 반응형 상태 관리를 제공합니다.
 */

import { writable, derived, type Writable } from 'svelte/store';
import { browser } from '$app/environment';
import { goto } from '$app/navigation';
import type { User } from '$lib/User';
import { tokenService } from '$lib/services/tokenService';
import { authClient } from '$lib/services/AxiosBackend';
import {
	createAuthError,
	getErrorMessage,
	convertAxiosError,
	safeRedirect
} from '$lib/utils/authHelpers';
import type {
	AuthError,
	AuthErrorType,
	AuthState,
	LoginCredentials,
	TokenResponse
} from '$lib/types/auth';

/**
 * 초기 인증 상태
 */
const initialState: AuthState = {
	isAuthenticated: false,
	isInitialized: false,
	isLoading: false,
	user: null,
	accessToken: null,
	refreshToken: null,
	tokenExpiresAt: null,
	error: null,
	debugInfo: import.meta.env.DEV
		? {
				lastTokenRefresh: null,
				tokenRefreshCount: 0,
				platform: ''
			}
		: null
};

/**
 * 인증 상태 스토어
 */
export const authState: Writable<AuthState> = writable(initialState);

/**
 * 파생된 상태들 (computed values)
 */
export const isAuthenticated = derived(authState, ($state) => $state.isAuthenticated);
export const currentUser = derived(authState, ($state) => $state.user);
export const isLoading = derived(authState, ($state) => $state.isLoading);
export const authError = derived(authState, ($state) => $state.error);
export const isInitialized = derived(authState, ($state) => $state.isInitialized);

/**
 * 토큰 만료 임박 여부 (5분 이내)
 */
export const isTokenExpiringSoon = derived(authState, ($state) => {
	if (!$state.tokenExpiresAt) return false;
	const now = new Date();
	const expiresAt = new Date($state.tokenExpiresAt);
	const timeDiff = expiresAt.getTime() - now.getTime();
	const fiveMinutes = 5 * 60 * 1000; // 5분을 밀리초로 변환
	return timeDiff <= fiveMinutes;
});

/**
 * 인증 상태 업데이트 헬퍼 함수들
 */
const updateAuthState = (updates: Partial<AuthState>) => {
	authState.update((state) => ({ ...state, ...updates }));
};

const setLoading = (loading: boolean) => {
	updateAuthState({ isLoading: loading });
};

const setError = (error: AuthError | null) => {
	updateAuthState({ error });

	// 개발 환경에서 에러 로깅
	if (import.meta.env.DEV && error) {
		console.error('[Auth Store] 인증 에러:', error);
	}
};

const setUser = (user: User | null) => {
	updateAuthState({ user });
};

const setTokens = (
	accessToken: string | null,
	refreshToken: string | null,
	expiresAt: Date | null = null
) => {
	updateAuthState({
		accessToken,
		refreshToken,
		tokenExpiresAt: expiresAt,
		isAuthenticated: !!(accessToken && refreshToken)
	});
};

/**
 * 디버그 정보 업데이트 (개발 환경에서만)
 */
const updateDebugInfo = (updates: Partial<NonNullable<AuthState['debugInfo']>>) => {
	if (import.meta.env.DEV) {
		authState.update((state) => ({
			...state,
			debugInfo: state.debugInfo ? { ...state.debugInfo, ...updates } : null
		}));
	}
};

/**
 * 인증 스토어 액션 함수들
 */
export const authActions = {
	/**
	 * 인증 시스템을 초기화합니다.
	 */
	async initialize(): Promise<void> {
		if (!browser) return;

		try {
			setLoading(true);
			setError(null);

			// 토큰 서비스 초기화
			await tokenService.initialize();

			// 저장된 토큰 확인
			const accessToken = await tokenService.getAccessToken();
			const refreshToken = await tokenService.getRefreshToken();

			if (accessToken && refreshToken) {
				// 토큰이 있으면 사용자 정보 로드 시도
				setTokens(accessToken, refreshToken);
				await this.loadUser();
			}

			// 디버그 정보 설정
			if (import.meta.env.DEV) {
				const { getCurrentPlatform } = await import('$lib/services/platformService');
				updateDebugInfo({ platform: getCurrentPlatform() });
				console.log('[Auth Store] 초기화 완료');
			}

			updateAuthState({ isInitialized: true });
		} catch (error) {
			console.error('[Auth Store] 초기화 실패:', error);
			setError(createAuthError('UNKNOWN_ERROR', '인증 시스템 초기화에 실패했습니다.'));
		} finally {
			setLoading(false);
		}
	},

	/**
	 * 사용자 로그인을 처리합니다.
	 */
	async login(credentials: LoginCredentials): Promise<void> {
		try {
			setLoading(true);
			setError(null);

			// JWT 로그인 API 호출
			const response = await authClient.post('/api/auth/login', {
				username: credentials.username,
				password: credentials.password
			});

			const tokenResponse: TokenResponse = response.data.data.tokens;
			const userResponse: User = response.data.data.user;

			// 개발 환경에서 토큰 응답 로깅
			if (import.meta.env.DEV) {
				console.log('[Auth Store] 로그인 응답 토큰:', {
					hasAccessToken: !!tokenResponse.access_token,
					hasRefreshToken: !!tokenResponse.refresh_token,
					accessTokenLength: tokenResponse.access_token?.length || 0,
					refreshTokenLength: tokenResponse.refresh_token?.length || 0,
					expiresIn: tokenResponse.expires_in
				});
			}

			// 토큰 저장
			await tokenService.storeTokenResponse(tokenResponse);

			// 저장 후 확인 (개발 환경에서만)
			if (import.meta.env.DEV) {
				const storedAccess = await tokenService.getAccessToken();
				const storedRefresh = await tokenService.getRefreshToken();
				console.log('[Auth Store] 토큰 저장 후 확인:', {
					storedAccessToken: !!storedAccess,
					storedRefreshToken: !!storedRefresh
				});
			}

			// 상태 업데이트
			const expiresAt = new Date(Date.now() + tokenResponse.expires_in * 1000);
			setTokens(tokenResponse.access_token, tokenResponse.refresh_token, expiresAt);

			// 사용자 정보 설정
			if (userResponse) {
				setUser(userResponse);

				// 기존 User.ts 호환성을 위한 세션 스토리지 저장
				const { setUser: setLegacyUser } = await import('$lib/User');
				await setLegacyUser(userResponse);
			} else {
				// 사용자 정보가 없으면 별도로 로드
				await this.loadUser();
			}

			if (import.meta.env.DEV) {
				console.log('[Auth Store] 로그인 성공');
			}
		} catch (error: any) {
			console.error('[Auth Store] 로그인 실패:', error);

			const authError = convertAxiosError(error);
			setError(authError);
			throw authError;
		} finally {
			setLoading(false);
		}
	},

	/**
	 * 사용자 로그아웃을 처리합니다.
	 */
	async logout(): Promise<void> {
		let serverLogoutFailed = false;
		let networkError = false;

		try {
			setLoading(true);
			setError(null); // 기존 에러 상태 초기화

			// 서버에 로그아웃 요청 (실패해도 로컬 정리는 반드시 진행)
			try {
				await authClient.post('/api/auth/logout');
				if (import.meta.env.DEV) {
					console.log('[Auth Store] 서버 로그아웃 요청 성공');
				}
			} catch (error: any) {
				serverLogoutFailed = true;

				// 네트워크 오류 여부 확인
				if (error.code === 'NETWORK_ERROR' || !error.response) {
					networkError = true;
					console.warn(
						'[Auth Store] 네트워크 오류로 서버 로그아웃 실패 (로컬 정리는 계속 진행):',
						error
					);
				} else {
					console.warn('[Auth Store] 서버 로그아웃 요청 실패 (로컬 정리는 계속 진행):', error);
				}
			}

			// 서버 로그아웃 실패 여부와 관계없이 로컬 토큰 완전 삭제
			try {
				await tokenService.clearTokens();
				if (import.meta.env.DEV) {
					console.log('[Auth Store] 로컬 토큰 완전 삭제 완료');
				}
			} catch (error) {
				console.error('[Auth Store] 로컬 토큰 삭제 실패:', error);
				// 토큰 삭제 실패는 심각한 문제이므로 에러로 처리
				throw error;
			}

			// 기존 User.ts 호환성을 위한 세션 스토리지 및 쿠키 정리
			try {
				const { removeUser } = await import('$lib/User');
				await removeUser();
				if (import.meta.env.DEV) {
					console.log('[Auth Store] 세션 스토리지 및 쿠키 정리 완료');
				}
			} catch (error) {
				console.error('[Auth Store] 세션 정리 실패:', error);
			}

			// 인증 상태 완전 초기화
			authState.set({
				...initialState,
				isInitialized: true,
				debugInfo: import.meta.env.DEV
					? {
							lastTokenRefresh: null,
							tokenRefreshCount: 0,
							platform: initialState.debugInfo?.platform || ''
						}
					: null
			});

			// 서버 로그아웃 실패 시 사용자에게 알림 (하지만 로그아웃은 완료됨)
			if (serverLogoutFailed) {
				if (networkError) {
					setError(
						createAuthError(
							'NETWORK_ERROR',
							'네트워크 연결 문제로 서버 로그아웃에 실패했지만, 로컬 로그아웃은 완료되었습니다.'
						)
					);
				} else {
					setError(
						createAuthError(
							'SERVER_ERROR',
							'서버 로그아웃에 실패했지만, 로컬 로그아웃은 완료되었습니다.'
						)
					);
				}
			}

			if (import.meta.env.DEV) {
				console.log('[Auth Store] 로그아웃 완료 - 인증 상태 완전 초기화');
			}

			// 로그인 페이지로 안전하게 리다이렉트
			await safeRedirect('/login');
		} catch (error) {
			console.error('[Auth Store] 로그아웃 중 심각한 오류 발생:', error);

			// 심각한 오류가 발생해도 최소한의 정리는 시도
			try {
				await tokenService.clearTokens();
				authState.set({
					...initialState,
					isInitialized: true,
					error: createAuthError('UNKNOWN_ERROR', '로그아웃 중 오류가 발생했습니다.')
				});
				await safeRedirect('/login');
			} catch (criticalError) {
				console.error('[Auth Store] 로그아웃 복구 시도도 실패:', criticalError);
			}

			setError(createAuthError('UNKNOWN_ERROR', '로그아웃 중 오류가 발생했습니다.'));
		} finally {
			setLoading(false);
		}
	},

	/**
	 * 토큰을 갱신합니다.
	 */
	async refreshAuth(): Promise<boolean> {
		try {
			if (import.meta.env.DEV) {
				console.log('[Auth Store] 토큰 갱신 시작');
			}

			// tokenRefreshService를 사용하여 토큰 갱신
			const { tokenRefreshService } = await import('$lib/services/tokenRefreshService');
			const newAccessToken = await tokenRefreshService.refreshAccessToken();

			if (newAccessToken) {
				// 갱신된 토큰으로 상태 업데이트
				const refreshToken = await tokenService.getRefreshToken();
				setTokens(newAccessToken, refreshToken);

				// 사용자 정보가 없으면 다시 로드 (토큰 갱신 후 사용자 정보 동기화)
				// get() 함수를 사용하여 현재 상태를 안전하게 가져오기
				const { get } = await import('svelte/store');
				const currentState = get(authState);

				if (!currentState.user) {
					try {
						await this.loadUser();
					} catch (userLoadError) {
						// 사용자 정보 로드 실패해도 토큰 갱신은 성공으로 처리
						console.warn('[Auth Store] 토큰 갱신 후 사용자 정보 로드 실패:', userLoadError);
					}
				}

				// 디버그 정보 업데이트
				if (import.meta.env.DEV) {
					updateDebugInfo({
						lastTokenRefresh: new Date(),
						tokenRefreshCount: (initialState.debugInfo?.tokenRefreshCount || 0) + 1
					});
					console.log('[Auth Store] 토큰 갱신 및 상태 동기화 완료');
				}

				return true;
			} else {
				// 토큰 갱신 실패
				if (import.meta.env.DEV) {
					console.log('[Auth Store] 토큰 갱신 실패 - 새 토큰을 받지 못함');
				}
				return false;
			}
		} catch (error: any) {
			console.error('[Auth Store] 토큰 갱신 중 오류:', error);

			// tokenRefreshService에서 이미 에러 처리를 했으므로
			// 여기서는 추가 로그아웃을 하지 않음
			const authError = createAuthError(
				'REFRESH_FAILED',
				'인증 갱신에 실패했습니다.',
				error.response?.status
			);
			setError(authError);

			return false;
		}
	},

	/**
	 * 사용자 정보를 로드합니다.
	 */
	async loadUser(): Promise<void> {
		try {
			const response = await authClient.get('/api/auth/me');
			const user: User = response.data.data.user;

			setUser(user);

			// 기존 User.ts 호환성을 위한 세션 스토리지 저장
			const { setUser: setLegacyUser } = await import('$lib/User');
			await setLegacyUser(user);

			if (import.meta.env.DEV) {
				console.log('[Auth Store] 사용자 정보 로드 완료:', user.name);
			}
		} catch (error: any) {
			console.error('[Auth Store] 사용자 정보 로드 실패:', error);

			if (error.response?.status === 401) {
				// 인증 실패 시 토큰 갱신 시도
				const refreshed = await this.refreshAuth();
				if (refreshed) {
					// 갱신 성공 시 재시도
					await this.loadUser();
				}
			} else {
				setError(
					createAuthError(
						'SERVER_ERROR',
						'사용자 정보를 불러올 수 없습니다.',
						error.response?.status
					)
				);
			}
		}
	},

	/**
	 * 사용자 정보를 업데이트합니다.
	 */
	updateUser(user: User): void {
		setUser(user);

		// 기존 User.ts 호환성을 위한 세션 스토리지 업데이트
		if (browser) {
			import('$lib/User').then(({ setUser: setLegacyUser }) => {
				setLegacyUser(user);
			});
		}

		if (import.meta.env.DEV) {
			console.log('[Auth Store] 사용자 정보 업데이트:', user.name);
		}
	},

	/**
	 * 에러를 지웁니다.
	 */
	clearError(): void {
		setError(null);
	},

	/**
	 * 사용자 친화적 에러 메시지를 반환합니다.
	 */
	getErrorMessage(error?: AuthError): string {
		if (error) {
			return getErrorMessage(error);
		}

		// 현재 상태에서 에러 가져오기
		let currentError: AuthError | null = null;
		const unsubscribe = authState.subscribe((state) => {
			currentError = state.error;
		});
		unsubscribe();

		return currentError ? getErrorMessage(currentError) : '';
	},

	/**
	 * 디버그 정보를 출력합니다 (개발 환경에서만).
	 */
	async debugAuth(): Promise<void> {
		if (import.meta.env.DEV) {
			const state = await new Promise<AuthState>((resolve) => {
				const unsubscribe = authState.subscribe((state) => {
					resolve(state);
					unsubscribe();
				});
			});

			console.log('[Auth Store Debug]', {
				isAuthenticated: state.isAuthenticated,
				isInitialized: state.isInitialized,
				isLoading: state.isLoading,
				hasUser: !!state.user,
				hasTokens: !!(state.accessToken && state.refreshToken),
				error: state.error,
				debugInfo: state.debugInfo
			});

			// 토큰 서비스 디버그 정보도 출력
			await tokenService.debugTokens();
		}
	}
};

/**
 * 인증 상태 초기화 함수
 * 앱 시작 시 호출하여 인증 상태를 초기화합니다.
 */
export async function initializeAuth(): Promise<void> {
	await authActions.initialize();
}

/**
 * 현재 인증 상태를 반환하는 헬퍼 함수
 */
export function getCurrentAuthState(): Promise<AuthState> {
	return new Promise(async (resolve) => {
		try {
			// get() 함수를 사용하여 현재 상태를 동기적으로 가져오기
			const { get } = await import('svelte/store');
			const currentState = get(authState);
			resolve(currentState);
		} catch (error) {
			console.error('[Auth Store] getCurrentAuthState 에러:', error);
			// 에러 시 기본 상태 반환
			resolve({
				isAuthenticated: false,
				isInitialized: false,
				isLoading: false,
				user: null,
				accessToken: null,
				refreshToken: null,
				tokenExpiresAt: null,
				error: null,
				debugInfo: null
			});
		}
	});
}

/**
 * 인증이 필요한 페이지에서 사용할 가드 함수
 */
export async function requireAuth(): Promise<boolean> {
	const state = await getCurrentAuthState();

	if (!state.isAuthenticated) {
		await goto('/login');
		return false;
	}

	return true;
}

/**
 * 로그인 페이지에서 사용할 리다이렉트 함수
 */
export async function redirectIfAuthenticated(redirectTo: string = '/'): Promise<boolean> {
	const state = await getCurrentAuthState();

	if (state.isAuthenticated) {
		await goto(redirectTo);
		return true;
	}

	return false;
}
