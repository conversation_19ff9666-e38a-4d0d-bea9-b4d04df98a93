import { writable } from 'svelte/store';
import { authClient } from '$lib/services/AxiosBackend';

import type { User } from '$lib/User';
import { processPageData } from '$lib/utils/pagination';
import { executeMessage, handleCatch } from '$lib/Functions';
import type { PaginationStore } from '$lib/types/types';

// 게시판 스토어 타입 정의
interface BoardStoreData extends PaginationStore {
	// 게시판 데이터
	articles?: [];
	notices?: [];
}

export const boardStore = writable<BoardStoreData>({});

export async function loadBoard(endpoint: string, user: User) {
	const payload = {
		userId: user.id
	};

	const { status, data } = await authClient.get(endpoint, {
		params: payload
	});

	try {
		if (status === 200 && data) {
			if (!data.data.articles) {
				await executeMessage("데이터 전송중 에러가 발생했습니다.\n잠시 후 F5 키를 눌러 다시 시도해 주세요.", 'error');
				return;
			}

			const pageData = processPageData(data.data.pagination);
			boardStore.set(pageData);

			const notices = await data.data.notices;
			const articles = await data.data.articles;
			boardStore.update(currentData => ({
				...currentData,
				articles,
				notices
			}));
		} else {
			await executeMessage('게시글 정보를 받아올 수 없습니다.', 'error');
		}
	} catch (error) {
		await handleCatch(error);
	}
}
