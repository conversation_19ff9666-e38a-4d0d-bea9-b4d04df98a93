import { get, readable, writable } from 'svelte/store';
import { authClient } from '$lib/services/AxiosBackend';

import type { User } from '$lib/User';
import { processPageData } from '$lib/utils/pagination';
import { executeMessage, handleCatch } from '$lib/Functions';

export const carryoutStore = writable({});

export async function loadItems(endpoint: string, user: User) {
	const payload = {
		userId: user.id
	};

	const { status, data } = await authClient.get(endpoint, {
		params: payload
	});

	try {
		if (status === 200 && data) {
			if (!data.data.items) {
				await executeMessage("데이터 전송중 에러가 발생했습니다.\n잠시 후 F5 키를 눌러 다시 시도해 주세요.", 'error');
				return;
			}

			const pageData = processPageData(data.data.pagination);
			carryoutStore.set(pageData);

			const items = await data.data.items;
			carryoutStore.update(currentData => ({
				...currentData,
				items
			}));
		} else {
			await executeMessage('외주 반출 목록을 받아올 수 없습니다.', 'error');
		}
	} catch (error) {
		await handleCatch(error);
	}
}

/**
 * 상품의 상태
 */
export const CARRYOUT_STATUS_CARRIED_OUT = 10;
export const CARRYOUT_STATUS_CARRIED_IN = 30;
export const CARRYOUT_STATUS_CANCELED = 90;

export const carryoutStatuses = readable([
	{ value: CARRYOUT_STATUS_CARRIED_OUT, text: '반출' },
	{ value: CARRYOUT_STATUS_CARRIED_IN, text: '반입' },
	{ value: CARRYOUT_STATUS_CANCELED, text: '취소' }
]);

export function getCarryoutStatusName(value: number) {
	const items = get(carryoutStatuses);
	const status = items.find(status => status.value === value);

	return status ? status.text : 'Unknown';
}
