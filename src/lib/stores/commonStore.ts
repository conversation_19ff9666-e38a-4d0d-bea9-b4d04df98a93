import { writable } from 'svelte/store';

// 공통으로 사용되는 상태들
export interface CommonState {
	// 현재 로그인한 사용자 정보
	currentUser: any | null;
	
	// 현재 선택된 팔레트 정보 (팔레트 생성/수정 페이지에서 공유)
	selectedPallet: any | null;
	
	// 현재 작업 중인 위치 정보
	workingLocation: {
		country: string;
		city: string;
		store: string;
		line: string;
		rack: string;
		level: string;
		column: string;
	} | null;
	
	// 검색 필터 상태 (페이지 간 공유)
	searchFilters: {
		[key: string]: any;
	};
	
	// 로딩 상태
	isLoading: boolean;
	
	// 에러 메시지
	errorMessage: string | null;
}

// 초기 상태
const initialState: CommonState = {
	currentUser: null,
	selectedPallet: null,
	workingLocation: null,
	searchFilters: {},
	isLoading: false,
	errorMessage: null
};

// 공통 스토어 생성
export const commonStore = writable<CommonState>(initialState);

// 스토어 초기화 함수
export function resetCommonStore() {
	commonStore.set(initialState);
}

// 부분 업데이트 함수들
export function setCurrentUser(user: any) {
	commonStore.update(state => ({
		...state,
		currentUser: user
	}));
}

export function setSelectedPallet(pallet: any) {
	commonStore.update(state => ({
		...state,
		selectedPallet: pallet
	}));
}

export function setWorkingLocation(location: CommonState['workingLocation']) {
	commonStore.update(state => ({
		...state,
		workingLocation: location
	}));
}

export function setSearchFilters(filters: { [key: string]: any }) {
	commonStore.update(state => ({
		...state,
		searchFilters: { ...state.searchFilters, ...filters }
	}));
}

export function setLoading(loading: boolean) {
	commonStore.update(state => ({
		...state,
		isLoading: loading
	}));
}

export function setErrorMessage(message: string | null) {
	commonStore.update(state => ({
		...state,
		errorMessage: message
	}));
}

// 특정 필터만 업데이트
export function updateSearchFilter(key: string, value: any) {
	commonStore.update(state => ({
		...state,
		searchFilters: {
			...state.searchFilters,
			[key]: value
		}
	}));
}

// 특정 필터 제거
export function removeSearchFilter(key: string) {
	commonStore.update(state => {
		const newFilters = { ...state.searchFilters };
		delete newFilters[key];
		return {
			...state,
			searchFilters: newFilters
		};
	});
} 