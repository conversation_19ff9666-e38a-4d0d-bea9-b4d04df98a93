import { writable } from 'svelte/store';

export const APP_NAME = 'Cornerstone Project WMS';
export const DIR_NAME = 'CornerstoneWMS';

// 엑셀 다운로드 URL(공통)
export const EXCEL_DOWNLOAD_URL = '/api/wms/download/excel';

/**
 * 사용할 프린트 리스트들<br>
 * 앞의 7자리 가지고 비교한다.
 *
 * BIXOLON SLP-D220  : QklYT0xPTiBTTFAtRDIyMA==
 * BIXOLON SLP-DX220 : QklYT0xPTiBTTFAtRFgyMjA==
 * TSC TE210 : VFNDIFRFMjEw
 * TSC DA220 : VFNDIERBMjlw
 */
export const LABEL_PRINTER_IDS = {
	BIXOLON: 'QklYT0x', // BIXOLON
	TSC_TR: 'VFNDIFR', // TSC TR 시리즈
	TSC_TE: 'VFNDIER', // TSC TE 시리즈
	XPRINTER: 'WHByaW50' // XP-DT427B
};

// 배열로도 사용할 수 있도록 값들만 별도로 추출
export const LABEL_PRINTER_ID_LIST = Object.values(LABEL_PRINTER_IDS);

//이전 페이지 저장
export const previousPage = writable('');

// 사이드 네비게이션 보이기
export const sideNavVisible = writable(true);
