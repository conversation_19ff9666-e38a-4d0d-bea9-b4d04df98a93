import { writable } from 'svelte/store';
import { authClient } from '$lib/services/AxiosBackend';

import type { User } from '$lib/User';
import { processPageData } from '$lib/utils/pagination';
import { executeMessage, handleCatch } from '$lib/Functions';

export const faqStore = writable({});

export async function loadFaq(endpoint: string, user: User) {
	const payload = {
		userId: user.id
	};

	const { status, data } = await authClient.get(endpoint, {
		params: payload
	});

	try {
		if (status === 200 && data) {
			if (!data.data.items) {
				await executeMessage("데이터 전송중 에러가 발생했습니다.\n잠시 후 F5 키를 눌러 다시 시도해 주세요.", 'error');
				return;
			}

			const pageData = processPageData(data.data.pagination);
			faqStore.set(pageData);

			const items = await data.data.items;
			faqStore.update(currentData => ({
				...currentData,
				items
			}));
		} else {
			await executeMessage('FAQ 정보를 받아올 수 없습니다.', 'error');
		}
	} catch (error) {
		await handleCatch(error);
	}
}
