import { writable } from 'svelte/store';
import { authClient } from '$lib/services/AxiosBackend';

export const loadedStore = writable({ items: [] });

export async function loadItems(endpoint: string, user: User) {
	const payload = {
		userId: user.id
	};

	try {
		loadedStore.set({ items: [] }); // 먼저 초기화

		const { status, data } = await authClient.get(endpoint, {
			params: payload
		});

		if (status === 200 && data) {
			const items = await data.data;

			loadedStore.set(items); // 새 데이터로 업데이트
		}
	} catch (error) {
		console.error('Failed to load items:', error);
		loadedStore.set({ items: [] }); // 에러 발생시 빈 배열로 초기화
	}
}