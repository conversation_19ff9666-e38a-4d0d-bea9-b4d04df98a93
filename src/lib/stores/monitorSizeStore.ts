import { writable } from 'svelte/store';
import { processPageData } from '$lib/utils/pagination';
import type { MonitorSizeItem, MonitorSizeSearchParams, MonitorSizeSearchResponse } from '$lib/types/monitorTypes';
import { fetchMonitorSizes, updateMonitorSizeItem } from '$lib/services/monitorSizeService';
import { handleCatch } from '$lib/Functions';
import type { PaginationStore } from '$lib/types/types';

export interface MonitorSizeStore extends PaginationStore {
  items: MonitorSizeItem[];
  loading: boolean;
}

export const monitorSizeStore = writable<MonitorSizeStore>({
  pageTotal: 0,
  pageCurrentPage: 1,
  pageFrom: 0,
  pageTo: 0,
  pageLastPage: 1,
  pagePerPage: 20,
  pageStartNo: 0,
  hasNext: false,
  hasPrev: false,
  pageLinks: [],
  items: [],
  loading: false
});

export async function loadMonitorSizes(params: MonitorSizeSearchParams) {
  monitorSizeStore.update((s) => ({ ...s, loading: true }));
  try {
    const res: MonitorSizeSearchResponse = await fetchMonitorSizes(params);

    // 서버의 pagination 메타를 클라이언트 표준 OptimizedPageData로 적절히 매핑
    const pagination = {
      total: res.pagination.total,
      per_page: res.pagination.per_page,
      current_page: res.pagination.current_page,
      last_page: res.pagination.last_page,
      from: (res.pagination.current_page - 1) * res.pagination.per_page + 1,
      to: Math.min(res.pagination.current_page * res.pagination.per_page, res.pagination.total),
      has_prev: res.pagination.current_page > 1,
      has_next: res.pagination.current_page < res.pagination.last_page
    };

    const pageData = processPageData(pagination);

    monitorSizeStore.set({
      ...(pageData as PaginationStore),
      items: res.items,
      loading: false
    });
  } catch (e) {
    await handleCatch(e);
    monitorSizeStore.update((s) => ({ ...s, loading: false }));
  }
}

export async function saveMonitorSizeItem(id: number, payload: { brand: 'brand' | 'general'; size: number; unit: 'INCH' | 'CM' }) {
  try {
    const updated = await updateMonitorSizeItem(id, payload);
    monitorSizeStore.update((s) => {
      const items = (s.items ?? []).map((it) => (it.id === id ? updated : it));
      return { ...s, items };
    });
    return updated;
  } catch (e) {
    await handleCatch(e);
    throw e;
  }
}


