import { get, readable, writable } from 'svelte/store';
import { authClient } from '$lib/services/AxiosBackend';

import type { User } from '$lib/User';
import { executeMessage, handleCatch, makeLocationInfo } from '$lib/Functions';

// 타입 선언
export type PalletProductItem = {
  id: number;
  product_id: number;
  quantity: number;
  amount: number;
  invoice1: number;
  invoice2: number;
  invoice3: number;
  product: {
    qaid: string;
    name: string;
    amount: number;
    cate4: { name: string };
    cate5?: { name: string } | null;
    rg?: string | null;
  };
  repair_grade: { name: string };
  repair_symptom: { name: string };
  repair_process: { name: string };
  checked_status: number;
  checked_at?: string | null;
  registered_at?: string | null;
  checked_user?: { name: string } | null;
};

export type PalletProductStoreState = {
  items: PalletProductItem[];
};

export type PalletInfoState = {
  pallet_info: {
    code: string;
    store: string;
    line: string;
    rack: string;
    level: string;
    column: string;
    location_name: string;
    country: string;
    city: string;
  };
  status: number;
  exported_at?: string | null;
  registered_user?: { name: string } | null;
  checked_at?: string | null;
  totalQuantity: number;
  totalAmount: number;
  totalInvoice1: number;
  totalInvoice2: number;
  totalInvoice3: number;
  invoiceTotal: number;
  palletGradeName: string;
};

const initialPalletInfo: PalletInfoState = {
  pallet_info: {
    code: '',
    store: '',
    line: '',
    rack: '',
    level: '',
    column: '',
    location_name: '',
    country: '',
    city: ''
  },
  status: 0,
  exported_at: null,
  registered_user: { name: '' },
  checked_at: null,
  totalQuantity: 0,
  totalAmount: 0,
  totalInvoice1: 0,
  totalInvoice2: 0,
  totalInvoice3: 0,
  invoiceTotal: 0,
  palletGradeName: ''
};

export const palletInfoStore = writable<PalletInfoState>(initialPalletInfo);
export const palletProductStore = writable<PalletProductStoreState>({ items: [] });

export async function loadItems(endpoint: string, user: User) {
	const payload = {
		userId: user.id
	};

	const { status, data } = await authClient.get(endpoint, {
		params: payload
	});

	try {
		if (status === 200 && data) {
			const palletProductData: PalletProductStoreState = {
					items: (data.data.items ?? []) as PalletProductItem[]
			};
			const palletData: any = data.data.pallet;

			let totalQuantity = 0;
			let totalAmount = 0;
			let totalInvoice1 = 0;
			let totalInvoice2 = 0;
			let totalInvoice3 = 0;
			let invoiceTotal = 0;
			let palletGradeName = '';

      palletProductData.items.forEach((item: PalletProductItem) => {
				totalQuantity += item.quantity;
				totalAmount += item.amount;
				totalInvoice1 += item.invoice1;
				totalInvoice2 += item.invoice2;
				totalInvoice3 += item.invoice3;
				invoiceTotal += item.invoice1 + item.invoice2 + item.invoice3;
				palletGradeName = item.repair_grade.name;
			});

			palletData.totalQuantity = totalQuantity;
			palletData.totalAmount = totalAmount;
			palletData.totalInvoice1 = totalInvoice1;
			palletData.totalInvoice2 = totalInvoice2;
			palletData.totalInvoice3 = totalInvoice3;
			palletData.invoiceTotal = invoiceTotal;
			palletData.palletGradeName = palletGradeName;

			const loc = makeLocationInfo(palletData.location);

			palletData.pallet_info = {
				'code': loc.pallet_code,
				'store': loc.store,
				'line': loc.line,
				'rack': loc.rack,
				'level': loc.level,
				'column': loc.column,
				'location_name': loc.location_name,
				'country': loc.country,
				'city': loc.city
			};

			palletProductStore.set(palletProductData);
            palletInfoStore.set(palletData as PalletInfoState);
		} else {
			await executeMessage('상품 목록을 받아올 수 없습니다.', 'error');
		}
	} catch (error) {
		await handleCatch(error);
	}
}

/**
 * 상품의 상태
 */
export const PALLET_PRODUCT_STATUS_REGISTERED = 10;
export const PALLET_PRODUCT_STATUS_EXPORTED = 30;
export const PALLET_PRODUCT_STATUS_DELETED = 90;

export const palletProductStatuses = readable([
	{ value: PALLET_PRODUCT_STATUS_REGISTERED, text: '적재' },
	{ value: PALLET_PRODUCT_STATUS_EXPORTED, text: '출고' },
	{ value: PALLET_PRODUCT_STATUS_DELETED, text: '삭제' }
]);

export function getPalletProductStatusName(value: number) {
	const items = get(palletProductStatuses);
	const status = items.find(status => status.value === value);

	return status ? status.text : 'Unknown';
}

/**
 * 상품의 검수 상태
 */
export const PALLET_PRODUCT_CHECK_STATUS_ON_PALLET = 10;
export const PALLET_PRODUCT_CHECK_STATUS_CHECKED = 20;

export const palletProductCheckedStatuses = readable([
	{ value: PALLET_PRODUCT_CHECK_STATUS_ON_PALLET, text: '미검수' },
	{ value: PALLET_PRODUCT_CHECK_STATUS_CHECKED, text: '검수완료' }
]);

export function getPalletProductCheckedStatusName(value: number) {
	const items = get(palletProductCheckedStatuses);
	const status = items.find(status => status.value === value);

	return status ? status.text : 'Unknown';
}