import { get, readable, writable } from 'svelte/store';
import { authClient } from '$lib/services/AxiosBackend';

import type { User } from '$lib/User';
import { processPageData} from '$lib/utils/pagination';
import { executeMessage, handleCatch, makeLocationInfo } from '$lib/Functions';

export const palletStore = writable({});

export async function loadPallets(endpoint: string, user: User): Promise<void> {
	const payload = {
		userId: user.id
	};

	const { status, data } = await authClient.get(endpoint, {
		params: payload
	});

	try {
		if (status === 200 && data) {
			if (!data.data.pallets) {
				await executeMessage("데이터 전송중 에러가 발생했습니다.\n잠시 후 F5 키를 눌러 다시 시도해 주세요.", 'error');
				return;
			}

			const pageData = processPageData(data.data.pagination);
			palletStore.set(pageData);

			let items = await data.data.pallets;
			items = items.map((item: any) => {
				const loc = makeLocationInfo(item.location);
				// if (item.location && 'level' in item.location && 'column' in item.location) {
				const pallet_info = {
					'code': loc.pallet_code,
					'store': loc.store,
					'line': loc.line,
					'rack': loc.rack,
					'level': loc.level,
					'column': loc.column,
					'location_name': loc.location_name,
					'country': loc.country,
					'city': loc.city
				};

				return { ...item, pallet_info };
			});

			palletStore.update(currentData => ({
				...currentData,
				items
			}));
		} else {
			await executeMessage('팔레트 정보를 받아올 수 없습니다.', 'error');
		}
	} catch (error) {
		await handleCatch(error);
	}
}

/**
 * 팔레트 상태
 */
export const PALLET_STATUS_REGISTERED = 10;
export const PALLET_STATUS_LOADED = 20;
export const PALLET_STATUS_CLOSED = 30;
export const PALLET_STATUS_EXPORTED = 40;
export const PALLET_STATUS_DELETED = 90;

export const palletStatuses = readable([
	{ value: PALLET_STATUS_REGISTERED, text: '등록' },
	{ value: PALLET_STATUS_LOADED, text: '적재중' },
	{ value: PALLET_STATUS_CLOSED, text: '적재마감(출고대기)' },
	{ value: PALLET_STATUS_EXPORTED, text: '출고완료' },
	{ value: PALLET_STATUS_DELETED, text: '삭제' }
]);

export function getPalletStatusName(value: number) {
	const statuses = get(palletStatuses);
	const status = statuses.find(status => status.value === value);

	return status ? status.text : 'Unknown';
}

/**
 * 팔레트 번호 가져오기 (간단한 버전)
 *
 * @param location
 */
export function getPalletNo(location: any) {
	if (location) {
		const code_arr = location.code.split('-');

		return code_arr[3] + '-' + code_arr[4];
	} else {
		return '-';
	}
}
