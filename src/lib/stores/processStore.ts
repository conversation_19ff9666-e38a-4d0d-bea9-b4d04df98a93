import type { User } from '$lib/User';

import { get, readable, writable } from 'svelte/store';

import { authClient } from '$lib/services/AxiosBackend';
import { executeMessage } from '$lib/Functions';

export const processStore = writable([]);

export async function loadProcesses(endpoint: string, user: User) {
	const payload = {
		userId: user.id
	};

	const { status, data } = await authClient.get(endpoint, {
		params: payload
	});

	if (status === 200 && data) {
		const processData = await data.data.processes;

		processStore.set(processData);
	} else {
		await executeMessage('점검코드 정보를 받아올 수 없습니다.', 'error');
	}
}

/**
 * 점검 검수코드
 */
export const PROCESS_TYPE_CHECK = 'check';
export const PROCESS_TYPE_REPAIR = 'repair';
export const PROCESS_TYPE_GRADE = 'grade';
export const PROCESS_TYPE_FIX = 'fix';
export const PROCESS_TYPE_CHARGE = 'charge';

export const processTypes = readable([
	{ value: PROCESS_TYPE_CHECK, text: '증상내용' },
	{ value: PROCESS_TYPE_REPAIR, text: '처리내용' },
	{ value: PROCESS_TYPE_GRADE, text: '수리상태' },
	{ value: PROCESS_TYPE_FIX, text: '별도 수리비' },
	{ value: PROCESS_TYPE_CHARGE, text: '추가비용' }
]);

export function getProcessTypeName(value: string) {
	const types = get(processTypes);
	const type = types.find((type) => type.value === value);
	return type ? type.text : 'Unknown';
}

/**
 * 점검 등급
 */
export const PROCESS_GRADE_BEST = 'ST_BEST';
export const PROCESS_GRADE_GOOD = 'ST_GOOD';
export const PROCESS_GRADE_NORMAL = 'ST_NORMAL';
export const PROCESS_GRADE_REFURB = 'ST_REFURB';
export const PROCESS_GRADE_XL = 'ST_XL';
export const PROCESS_GRADE_XL1 = 'ST_XL1';
export const PROCESS_GRADE_XL2 = 'ST_XL2';
export const PROCESS_GRADE_XL3 = 'ST_XL3';
export const PROCESS_GRADE_XL4 = 'ST_XL4';
export const PROCESS_GRADE_XL5 = 'ST_XL5';
export const PROCESS_GRADE_XL6 = 'ST_XL6';

export const processGrade = readable([
	{ value: PROCESS_GRADE_BEST, text: 'CB' },
	{ value: PROCESS_GRADE_GOOD, text: 'CG' },
	{ value: PROCESS_GRADE_NORMAL, text: 'CN' },
	{ value: PROCESS_GRADE_REFURB, text: 'RP' },
	{ value: PROCESS_GRADE_XL, text: 'XL' },
	{ value: PROCESS_GRADE_XL1, text: 'XL1' },
	{ value: PROCESS_GRADE_XL2, text: 'XL2' },
	{ value: PROCESS_GRADE_XL3, text: 'XL3' },
	{ value: PROCESS_GRADE_XL4, text: 'XL4' },
	{ value: PROCESS_GRADE_XL5, text: 'XL5' },
	{ value: PROCESS_GRADE_XL6, text: 'XL6' },
]);

export function getProcessGradeName(value: string) {
	const types = get(processGrade);
	const type = types.find((type) => type.value === value);

	return type ? type.text : '등급 없음';
}

/**
 * 등급별 색상 및 이름 설정
 * 
 * @param process 등급 코드
 * @returns 색상과 이름이 포함된 객체 또는 null
 */
function getGradeStyle(process: string): { color: string; name: string } | null {
	if (process === 'CB' || process === 'CN' || process === 'CG') {
		return { color: '#29b8ff', name: process };
	} else if (process === 'RP') {
		return { color: '#f8d903', name: process };
	} else if (process.startsWith('XL')) {
		return { color: '#ff6666', name: process };
	} else if (process === 'WT') {
		return { color: '#a2a2a2', name: 'WT' };
	}
	return null;
}

/**
 * 점검(수리)상태 색상 표시 버튼
 *
 * @param item
 */
export function getProcessGradeColorButton(item: any) {
	let process;

	// 프로세스 등급 추출
	if (item?.grade_name) {
		// 팔레트 리스트
		const gradeName = item.grade_name;
		process = gradeName.startsWith('XL') ? 'XL' : gradeName;
	} else if (item?.repair_grade?.name) {
		// 팔레트 상품 리스트
		process = item?.repair_grade?.name;
	} else if (item?.repair_product) {
		// 일반 리스트: 수리 완료 상태인 경우 등급 표시, 미완료인 경우 WT 표시
		process = item.repair_product.status === 30 
			? item.repair_product.repair_grade.name 
			: 'WT';
	} else if (item?.pallet_products?.length > 0) {
		// @todo: 예전 방식으로 인해...추후 필요 없을 때 삭제해도 됨
		process = item.pallet_products[0]?.repair_grade.name;
	} else {
		return '-';
	}

	// 등급별 스타일 가져오기
	const style = getGradeStyle(process);
	if (!style) {
		return '-';
	}

	return `<button class="btn btn-ghost btn-xs text-neutral" style='background-color: ${style.color};'>${style.name}</button>`;
}
