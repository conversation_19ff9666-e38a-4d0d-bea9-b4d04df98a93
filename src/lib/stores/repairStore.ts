import { writable, readable } from 'svelte/store';
import { authClient } from '$lib/services/AxiosBackend';

import type { User } from '$lib/User';
import { processPageData } from '$lib/utils/pagination';
import { executeMessage, handleCatch } from '$lib/Functions';

export enum InvoiceTypes {
	Checked = 'CHECK',
	Repaired = 'REPAIR',
	ReinstalledOS = 'OS reinstall',
}

export enum GradeBasis {
	Size = 'size',
	Price = 'price',
	None = 'none'
}

export enum GradeBasisUnit {
	Inch = 'inch',
	CM = 'cm',
	Won = 'won'
}

export const repairFeeStore = writable({});

export async function loadRepairFee(endpoint: string, user: User) {
	const payload = {
		userId: user.id
	};

	const { status, data } = await authClient.get(endpoint, {
		params: payload
	});

	try {
		if (status === 200 && data.success) {
			if (!data.data.items) {
				await executeMessage("데이터 전송중 에러가 발생했습니다.\n잠시 후 F5 키를 눌러 다시 시도해 주세요.", 'error');
				return;
			}

			const pageData = processPageData(data.data.pagination);
			repairFeeStore.set(pageData);

			const items = await data.data.items;
			repairFeeStore.update(currentData => ({
				...currentData,
				items
			}));
		} else {
			await executeMessage(data.data.message, 'error');
		}
	} catch (error) {
		await handleCatch(error);
	}
}

export const REPAIR_STATUS_WAITING = 10;  // 구성품 대기
export const REPAIR_STATUS_COMPLETE = 30; // 점검완료

export const repairStatuses = readable([
  { value: REPAIR_STATUS_WAITING, text: '구성품 대기' },
  { value: REPAIR_STATUS_COMPLETE, text: '점검완료' }
]);