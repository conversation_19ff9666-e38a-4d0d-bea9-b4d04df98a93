import { get, readable, writable } from 'svelte/store';
import { authClient } from '$lib/services/AxiosBackend';

import type { User } from '$lib/User';
import { executeMessage } from '$lib/Functions';

export const warehousePalletInfoStore = writable({});
export const warehousePalletItemStore = writable({});

export async function loadItems(endpoint: string, user: User) {
	const payload = {
		userId: user.id
	};

	const { status, data } = await authClient.get(endpoint, {
		params: payload
	});

	if (status === 200 && data) {
		const palletData = data.data.pallet;
		const palletItemData = {
			items: data.data.items,
		};

		let totalQuantity = 0;
		let totalAmount = 0;

		palletItemData.items.forEach((item: any) => {
			totalQuantity += item.quantity;
			totalAmount += item.product.amount;
		});

		palletData.totalQuantity = totalQuantity;
		palletData.totalAmount = totalAmount;

		warehousePalletItemStore.set(palletItemData);
		warehousePalletInfoStore.set(palletData);
	} else {
		await executeMessage('상품목록을 받아올 수 없습니다.', 'error');
	}
}

/**
 * 상품의 상태
 */
export const WAREHOUSE_PALLET_ITEM_STATUS_STORED = 'stored';
export const WAREHOUSE_PALLET_ITEM_STATUS_EXPORTED = 'exported';
export const WAREHOUSE_PALLET_ITEM_STATUS_INTERNAL_USE = 'internal_use';
export const WAREHOUSE_PALLET_ITEM_STATUS_INTERNAL_PARTS = 'internal_parts';
export const WAREHOUSE_PALLET_ITEM_STATUS_RETURNED = 'returned';
export const WAREHOUSE_PALLET_ITEM_STATUS_DISCARDED = 'discarded';

export const warehousePalletItemStatuses = readable([
	{ value: WAREHOUSE_PALLET_ITEM_STATUS_STORED, text: '입고' },
	{ value: WAREHOUSE_PALLET_ITEM_STATUS_EXPORTED, text: '출고' },
	{ value: WAREHOUSE_PALLET_ITEM_STATUS_INTERNAL_USE, text: '내부 사용' },
	{ value: WAREHOUSE_PALLET_ITEM_STATUS_INTERNAL_PARTS, text: '내부 부품' },
	{ value: WAREHOUSE_PALLET_ITEM_STATUS_RETURNED, text: '반품(쿠팡)' },
	{ value: WAREHOUSE_PALLET_ITEM_STATUS_DISCARDED, text: '폐기' }
]);

export function warehousePalletItemStatusName(value: string) {
	const items = get(warehousePalletItemStatuses);
	const status = items.find(status => status.value === value);

	return status ? status.text : 'Unknown';
}
