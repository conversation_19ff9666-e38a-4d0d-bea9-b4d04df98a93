import { get, readable, writable } from 'svelte/store';
import { authClient } from '$lib/services/AxiosBackend';

import type { User } from '$lib/User';
import { processPageData } from '$lib/utils/pagination';
import { executeMessage, handleCatch } from '$lib/Functions';

export const warehousePalletStore = writable({});

export async function loadWarehousePallets(endpoint: string, user: User): Promise<void> {
	const payload = {
		userId: user.id
	};

	const { status, data } = await authClient.get(endpoint, {
		params: payload
	});

	try {
		if (status === 200 && data) {
			if (!data.data.pallets) {
				await executeMessage("데이터 전송중 에러가 발생했습니다.\n잠시 후 F5 키를 눌러 다시 시도해 주세요.", 'error');
				return;
			}

			const pageData = processPageData(data.data.pagination);
			warehousePalletStore.set(pageData);

			const items = await data.data.pallets;

			warehousePalletStore.update(currentData => ({
				...currentData,
				items
			}));
		} else {
			await executeMessage('팔레트 정보를 받아올 수 없습니다.', 'error');
		}
	} catch (error) {
		await handleCatch(error);
	}
}

// 팔레트 상태
export const WAREHOUSE_PALLET_STATUS_AVAILABLE = 'available';
export const WAREHOUSE_PALLET_STATUS_IN_USE = 'in_use';
export const WAREHOUSE_PALLET_STATUS_PENDING = 'pending';
export const WAREHOUSE_PALLET_STATUS_COMPLETED = 'completed';

export const warehousePalletStatuses = readable([
	{ value: WAREHOUSE_PALLET_STATUS_AVAILABLE, text: '사용 가능' },
	{ value: WAREHOUSE_PALLET_STATUS_IN_USE, text: '적재중' },
	{ value: WAREHOUSE_PALLET_STATUS_PENDING, text: '입고 대기중' },
	{ value: WAREHOUSE_PALLET_STATUS_COMPLETED, text: '입고완료' }
]);

export function warehousePalletStatusColor(value: string) {
	let color = '';
	const statusesArray = get(warehousePalletStatuses);
	const statusText = statusesArray.find(status => status.value === value)?.text || '';

	if (value === WAREHOUSE_PALLET_STATUS_AVAILABLE) {
		color = `<span class="text-blue-900 font-bold bg-blue-100 px-3 py-1 rounded shadow min-w-[100px] inline-block text-center">${statusText}</span>`; // Dark blue text
	} else if (value === WAREHOUSE_PALLET_STATUS_IN_USE) {
		color = `<span class="text-orange-900 font-bold bg-orange-100 px-3 py-1 rounded shadow min-w-[100px] inline-block text-center">${statusText}</span>`; // Dark orange text
	} else if (value === WAREHOUSE_PALLET_STATUS_PENDING) {
		color = `<span class="text-red-900 font-bold bg-red-100 px-3 py-1 rounded shadow min-w-[100px] inline-block text-center">${statusText}</span>`; // Dark red text
	} else if (value === WAREHOUSE_PALLET_STATUS_COMPLETED) {
		color = `<span class="text-green-900 font-bold bg-green-100 px-3 py-1 rounded shadow min-w-[100px] inline-block text-center">${statusText}</span>`; // Dark green text
	}

	return color;
}