import { writable } from 'svelte/store';
import { authClient } from '$lib/services/AxiosBackend';
import { processPageData } from '$lib/utils/pagination';
import { executeMessage, handleCatch } from '$lib/Functions';
import type { WorkHistoryStoreData } from '$lib/types/types';

export const workHistoryStore = writable<WorkHistoryStoreData>({});

export async function loadWorkHistory(
	page: number = 1,
	pageSize: number = 16,
	date: string = new Date().toISOString().split('T')[0]
) {
	const params = new URLSearchParams({
		page: page.toString(),
		pageSize: pageSize.toString(),
		date: date
	});

	try {
		const { status, data } = await authClient.get(`/api/wms/user/work-history/today?${params}`);

		if (status === 200 && data.success) {
			if (!data.data.work_logs) {
				await executeMessage(
					'데이터 전송중 에러가 발생했습니다.\n잠시 후 F5 키를 눌러 다시 시도해 주세요.',
					'error'
				);
				return;
			}

			// 백엔드 응답 구조에 따라 페이지네이션 처리 방식 결정
			let pageData;

			// 최적화된 페이지네이션 데이터가 있는지 확인
			if (data.data.pagination) {
				// 최적화된 방식 사용
				pageData = processPageData(data.data.pagination);
			} else {
				await executeMessage('페이지 정보를 찾을 수 없습니다.', 'error');
				return;
			}

			const workLogs = await data.data.work_logs;
			const summary = await data.data.summary;

			workHistoryStore.set({
				...pageData,
				workLogs,
				summary
			});
		} else {
			await executeMessage(data.message || '작업 내역을 불러오는데 실패했습니다.', 'error');
		}
	} catch (error) {
		await handleCatch(error);
	}
}

/**
 * 현재 페이지 데이터 새로고침
 */
export async function refreshWorkHistory(date: string = new Date().toISOString().split('T')[0]) {
	const { get } = await import('svelte/store');
	const currentState = get(workHistoryStore);

	if (currentState) {
		const currentPage = currentState.pageCurrentPage || 1;
		const currentPageSize = currentState.pagePerPage || 16;
		await loadWorkHistory(currentPage, currentPageSize, date);
	}
}
