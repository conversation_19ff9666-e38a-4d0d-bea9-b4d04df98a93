/**
 * JWT 인증 시스템 관련 타입 정의
 *
 * 이 파일은 JWT 인증 시스템에서 사용되는 모든 타입들을 중앙 집중식으로 관리합니다.
 * 타입 안전성을 보장하고 코드의 일관성을 유지하기 위해 사용됩니다.
 */

/**
 * 플랫폼 타입
 * 애플리케이션이 실행될 수 있는 플랫폼들을 정의합니다.
 */
export type Platform = 'desktop' | 'android' | 'ios' | 'web';

/**
 * 저장소 타입
 * 플랫폼별로 사용되는 토큰 저장소 타입들을 정의합니다.
 */
export type StorageType =
	| 'tauri-store'
	| 'android-keystore'
	| 'ios-keychain'
	| 'localstorage'
	| 'memory';

/**
 * JWT 페이로드 인터페이스
 * JWT 토큰에 포함되는 표준 클레임들을 정의합니다.
 */
export interface JWTPayload {
	/** 사용자 ID (Subject) */
	sub: string;
	/** 발급 시간 (Issued At) - Unix timestamp */
	iat: number;
	/** 만료 시간 (Expiration Time) - Unix timestamp */
	exp: number;
	/** 토큰 타입 (선택적) */
	type?: 'access' | 'refresh';
	/** 발급자 (Issuer) - 선택적 */
	iss?: string;
	/** 대상 (Audience) - 선택적 */
	aud?: string;
	/** JWT ID - 선택적 */
	jti?: string;
	/** 추가 클레임들 */
	[key: string]: any;
}

/**
 * 토큰 응답 인터페이스
 * 서버에서 로그인/토큰 갱신 시 반환하는 응답 구조를 정의합니다.
 */
export interface TokenResponse {
	/** 액세스 토큰 */
	access_token: string;
	/** 리프레시 토큰 */
	refresh_token: string;
	/** 토큰 타입 (항상 'Bearer') */
	token_type: 'Bearer';
	/** 액세스 토큰 만료 시간 (초 단위) */
	expires_in: number;
	/** 사용자 정보 (선택적) */
	user?: any;
}

/**
 * 토큰 상태 인터페이스
 * 현재 저장된 토큰들의 상태 정보를 나타냅니다.
 */
export interface TokenStatus {
	/** 액세스 토큰 보유 여부 */
	hasAccessToken: boolean;
	/** 리프레시 토큰 보유 여부 */
	hasRefreshToken: boolean;
	/** 액세스 토큰 유효성 */
	isAccessTokenValid: boolean;
	/** 리프레시 토큰 유효성 */
	isRefreshTokenValid: boolean;
	/** 액세스 토큰 남은 시간 (초) */
	accessTokenRemainingTime: number;
	/** 리프레시 토큰 남은 시간 (초) */
	refreshTokenRemainingTime: number;
	/** 사용자 ID */
	userId: string | null;
}

/**
 * 로그인 자격 증명 인터페이스
 * 사용자 로그인 시 필요한 정보를 정의합니다.
 */
export interface LoginCredentials {
	/** 사용자명 또는 이메일 */
	username: string;
	/** 비밀번호 */
	password: string;
}

/**
 * 인증 에러 타입
 * 인증 과정에서 발생할 수 있는 에러 타입들을 정의합니다.
 */
export type AuthErrorType =
	| 'INVALID_CREDENTIALS' // 잘못된 로그인 정보
	| 'TOKEN_EXPIRED' // 토큰 만료
	| 'REFRESH_FAILED' // 토큰 갱신 실패
	| 'NETWORK_ERROR' // 네트워크 오류
	| 'VALIDATION_ERROR' // 입력 검증 오류
	| 'SERVER_ERROR' // 서버 오류
	| 'UNKNOWN_ERROR'; // 알 수 없는 오류

/**
 * 인증 에러 인터페이스
 * 인증 관련 에러 정보를 담는 구조를 정의합니다.
 */
export interface AuthError {
	/** 에러 타입 */
	type: AuthErrorType;
	/** 에러 메시지 */
	message: string;
	/** HTTP 상태 코드 (선택적) */
	statusCode?: number;
	/** 추가 에러 세부사항 (선택적) */
	details?: any;
}

/**
 * 토큰 갱신 에러 타입
 * 토큰 갱신 과정에서 발생할 수 있는 에러 타입들을 정의합니다.
 */
export type TokenRefreshErrorType =
	| 'NO_REFRESH_TOKEN' // 리프레시 토큰 없음
	| 'REFRESH_FAILED' // 갱신 실패
	| 'NETWORK_ERROR' // 네트워크 오류
	| 'INVALID_RESPONSE'; // 잘못된 응답

/**
 * 토큰 갱신 에러 인터페이스
 * 토큰 갱신 관련 에러 정보를 담는 구조를 정의합니다.
 */
export interface TokenRefreshError {
	/** 에러 타입 */
	type: TokenRefreshErrorType;
	/** 에러 메시지 */
	message: string;
	/** 원본 에러 (선택적) */
	originalError?: any;
}

/**
 * 인증 상태 인터페이스
 * 애플리케이션의 전체 인증 상태를 나타냅니다.
 */
export interface AuthState {
	// 인증 상태
	/** 인증 여부 */
	isAuthenticated: boolean;
	/** 초기화 완료 여부 */
	isInitialized: boolean;
	/** 로딩 중 여부 */
	isLoading: boolean;

	// 사용자 정보
	/** 현재 사용자 정보 */
	user: any | null; // User 타입은 기존 User.ts에서 import

	// 토큰 정보
	/** 현재 액세스 토큰 */
	accessToken: string | null;
	/** 현재 리프레시 토큰 */
	refreshToken: string | null;
	/** 토큰 만료 시간 */
	tokenExpiresAt: Date | null;

	// 에러 상태
	/** 현재 에러 정보 */
	error: AuthError | null;

	// 디버그 정보 (개발 환경에서만)
	/** 디버그 정보 */
	debugInfo: {
		/** 마지막 토큰 갱신 시간 */
		lastTokenRefresh: Date | null;
		/** 토큰 갱신 횟수 */
		tokenRefreshCount: number;
		/** 현재 플랫폼 */
		platform: string;
	} | null;
}

/**
 * 토큰 저장소 인터페이스
 * 플랫폼별 토큰 저장소가 구현해야 하는 메서드들을 정의합니다.
 */
export interface TokenStorage {
	/**
	 * 저장소를 초기화합니다.
	 */
	initialize(): Promise<void>;

	/**
	 * 액세스 토큰을 저장합니다.
	 * @param token 액세스 토큰
	 */
	storeAccessToken(token: string): Promise<void>;

	/**
	 * 리프레시 토큰을 저장합니다.
	 * @param token 리프레시 토큰
	 */
	storeRefreshToken(token: string): Promise<void>;

	/**
	 * 두 토큰을 한 번에 저장합니다.
	 * @param accessToken 액세스 토큰
	 * @param refreshToken 리프레시 토큰
	 */
	storeTokens(accessToken: string, refreshToken: string): Promise<void>;

	/**
	 * 액세스 토큰을 조회합니다.
	 * @returns 액세스 토큰 또는 null
	 */
	getAccessToken(): Promise<string | null>;

	/**
	 * 리프레시 토큰을 조회합니다.
	 * @returns 리프레시 토큰 또는 null
	 */
	getRefreshToken(): Promise<string | null>;

	/**
	 * 저장된 모든 토큰을 삭제합니다.
	 */
	clearTokens(): Promise<void>;
}

/**
 * 플랫폼 서비스 인터페이스
 * 플랫폼 감지 및 플랫폼별 기능을 제공하는 서비스의 인터페이스입니다.
 */
export interface PlatformService {
	/**
	 * 현재 플랫폼을 반환합니다.
	 * @returns 현재 플랫폼
	 */
	getCurrentPlatform(): Platform;

	/**
	 * 데스크탑 플랫폼인지 확인합니다.
	 * @returns 데스크탑 여부
	 */
	isDesktop(): boolean;

	/**
	 * 안드로이드 플랫폼인지 확인합니다.
	 * @returns 안드로이드 여부
	 */
	isAndroid(): boolean;

	/**
	 * iOS 플랫폼인지 확인합니다.
	 * @returns iOS 여부
	 */
	isIOS(): boolean;

	/**
	 * 웹 환경인지 확인합니다.
	 * @returns 웹 환경 여부
	 */
	isWeb(): boolean;

	/**
	 * Tauri 환경인지 확인합니다.
	 * @returns Tauri 환경 여부
	 */
	isTauri(): boolean;

	/**
	 * 모바일 플랫폼인지 확인합니다.
	 * @returns 모바일 여부
	 */
	isMobile(): boolean;

	/**
	 * 보안 저장소 지원 여부를 확인합니다.
	 * @returns 보안 저장소 지원 여부
	 */
	hasSecureStorage(): boolean;

	/**
	 * 저장소 타입을 반환합니다.
	 * @returns 저장소 타입
	 */
	getStorageType(): StorageType;
}

/**
 * 토큰 서비스 인터페이스
 * 토큰 관리 서비스가 제공해야 하는 메서드들을 정의합니다.
 */
export interface TokenService {
	/**
	 * 토큰 서비스를 초기화합니다.
	 */
	initialize(): Promise<void>;

	/**
	 * 토큰 응답을 저장합니다.
	 * @param tokenResponse 서버에서 받은 토큰 응답
	 */
	storeTokenResponse(tokenResponse: TokenResponse): Promise<void>;

	/**
	 * 두 토큰을 저장합니다.
	 * @param accessToken 액세스 토큰
	 * @param refreshToken 리프레시 토큰
	 */
	storeTokens(accessToken: string, refreshToken: string): Promise<void>;

	/**
	 * 유효한 액세스 토큰을 조회합니다.
	 * @returns 유효한 액세스 토큰 또는 null
	 */
	getAccessToken(): Promise<string | null>;

	/**
	 * 유효한 리프레시 토큰을 조회합니다.
	 * @returns 유효한 리프레시 토큰 또는 null
	 */
	getRefreshToken(): Promise<string | null>;

	/**
	 * 모든 토큰을 삭제합니다.
	 */
	clearTokens(): Promise<void>;

	/**
	 * 액세스 토큰 유효성을 확인합니다.
	 * @returns 유효성 여부
	 */
	isAccessTokenValid(): Promise<boolean>;

	/**
	 * 리프레시 토큰 유효성을 확인합니다.
	 * @returns 유효성 여부
	 */
	isRefreshTokenValid(): Promise<boolean>;

	/**
	 * 토큰이 곧 만료되는지 확인합니다.
	 * @param bufferMinutes 여유 시간 (분)
	 * @returns 만료 임박 여부
	 */
	isAccessTokenExpiringSoon(bufferMinutes?: number): Promise<boolean>;

	/**
	 * 토큰 상태를 조회합니다.
	 * @returns 토큰 상태 정보
	 */
	getTokenStatus(): Promise<TokenStatus>;

	/**
	 * 인증 여부를 확인합니다.
	 * @returns 인증 여부
	 */
	isAuthenticated(): Promise<boolean>;

	/**
	 * 현재 사용자 ID를 조회합니다.
	 * @returns 사용자 ID 또는 null
	 */
	getCurrentUserId(): Promise<string | null>;
}

/**
 * 토큰 갱신 서비스 인터페이스
 * 토큰 자동 갱신 서비스가 제공해야 하는 메서드들을 정의합니다.
 */
export interface TokenRefreshService {
	/**
	 * 액세스 토큰을 갱신합니다.
	 * @returns 새로운 액세스 토큰 또는 null
	 */
	refreshAccessToken(): Promise<string | null>;

	/**
	 * 토큰 갱신을 대기합니다.
	 * @returns 새로운 액세스 토큰 또는 null
	 */
	waitForTokenRefresh(): Promise<string | null>;

	/**
	 * 토큰 갱신 실패를 처리합니다.
	 * @param error 토큰 갱신 에러
	 */
	handleRefreshFailure(error: TokenRefreshError): Promise<void>;

	/**
	 * 유효한 토큰을 보장합니다.
	 * @param bufferMinutes 여유 시간 (분)
	 * @returns 유효한 토큰 또는 null
	 */
	ensureValidToken(bufferMinutes?: number): Promise<string | null>;

	/**
	 * 현재 갱신 중인지 확인합니다.
	 * @returns 갱신 중 여부
	 */
	isRefreshing(): boolean;

	/**
	 * 대기 중인 요청 수를 반환합니다.
	 * @returns 대기 중인 요청 수
	 */
	getQueueLength(): number;
}

/**
 * 인증 스토어 액션 인터페이스
 * 인증 상태 관리 스토어의 액션 메서드들을 정의합니다.
 */
export interface AuthActions {
	/**
	 * 인증 시스템을 초기화합니다.
	 */
	initialize(): Promise<void>;

	/**
	 * 사용자 로그인을 처리합니다.
	 * @param credentials 로그인 자격 증명
	 */
	login(credentials: LoginCredentials): Promise<void>;

	/**
	 * 사용자 로그아웃을 처리합니다.
	 */
	logout(): Promise<void>;

	/**
	 * 토큰을 갱신합니다.
	 * @returns 갱신 성공 여부
	 */
	refreshAuth(): Promise<boolean>;

	/**
	 * 사용자 정보를 로드합니다.
	 */
	loadUser(): Promise<void>;

	/**
	 * 사용자 정보를 업데이트합니다.
	 * @param user 사용자 정보
	 */
	updateUser(user: any): void;

	/**
	 * 에러를 지웁니다.
	 */
	clearError(): void;

	/**
	 * 사용자 친화적 에러 메시지를 반환합니다.
	 * @param error 에러 객체 (선택적)
	 * @returns 에러 메시지
	 */
	getErrorMessage(error?: AuthError): string;
}

/**
 * API 응답 래퍼 인터페이스
 * 서버 API 응답의 공통 구조를 정의합니다.
 */
export interface ApiResponse<T = any> {
	/** 성공 여부 */
	success: boolean;
	/** 응답 데이터 */
	data: T;
	/** 메시지 */
	message?: string;
	/** 에러 정보 (실패 시) */
	errors?: any;
}

/**
 * 페이지네이션 정보 인터페이스
 * API 응답에서 페이지네이션 정보를 나타냅니다.
 */
export interface PaginationInfo {
	/** 현재 페이지 */
	current_page: number;
	/** 마지막 페이지 */
	last_page: number;
	/** 페이지당 항목 수 */
	per_page: number;
	/** 전체 항목 수 */
	total: number;
	/** 현재 페이지 첫 번째 항목 번호 */
	from: number;
	/** 현재 페이지 마지막 항목 번호 */
	to: number;
}

/**
 * 페이지네이션된 응답 인터페이스
 * 페이지네이션이 적용된 API 응답 구조를 정의합니다.
 */
export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
	/** 페이지네이션 정보 */
	pagination: PaginationInfo;
}

/**
 * HTTP 메서드 타입
 * 지원되는 HTTP 메서드들을 정의합니다.
 */
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

/**
 * API 엔드포인트 설정 인터페이스
 * API 엔드포인트의 설정 정보를 정의합니다.
 */
export interface ApiEndpoint {
	/** HTTP 메서드 */
	method: HttpMethod;
	/** 엔드포인트 경로 */
	path: string;
	/** 인증 필요 여부 */
	requiresAuth?: boolean;
	/** 타임아웃 (밀리초) */
	timeout?: number;
}

/**
 * 유틸리티 타입들
 */

/**
 * 선택적 속성을 가진 타입
 * 기존 타입의 모든 속성을 선택적으로 만듭니다.
 */
export type Partial<T> = {
	[P in keyof T]?: T[P];
};

/**
 * 필수 속성을 가진 타입
 * 기존 타입의 모든 속성을 필수로 만듭니다.
 */
export type Required<T> = {
	[P in keyof T]-?: T[P];
};

/**
 * 특정 속성만 선택한 타입
 * 기존 타입에서 지정된 속성들만 선택합니다.
 */
export type Pick<T, K extends keyof T> = {
	[P in K]: T[P];
};

/**
 * 특정 속성을 제외한 타입
 * 기존 타입에서 지정된 속성들을 제외합니다.
 */
export type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>;
