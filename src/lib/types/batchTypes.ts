/**
 * 배치 팔레트 적재 기능 관련 타입 정의
 *
 * 이 파일은 일괄 팔레트 적재 기능에 필요한 데이터 모델과 인터페이스를 정의합니다.
 * 로컬스토리지에 임시 저장되는 상품 데이터와 스토리지 상태를 관리하기 위한 타입들이 포함되어 있습니다.
 */

/**
 * 배치 상품 데이터 인터페이스
 * 로컬스토리지에 임시 저장되는 개별 상품 정보를 정의합니다.
 */
export interface BatchProductData {
	/** 로컬 ID (임시) - UUID v4 형식 */
	id: string;

	/** QAID 바코드 값 */
	qaid: string;

	/** 생성/수정 시간 (타임스탬프) */
	timestamp: number;

	/** 상품 상태 (대기중/전송중/성공/실패) */
	status: 'pending' | 'submitting' | 'success' | 'failed';

	/** 오류 메시지 (실패 시) */
	errorMessage?: string;

	/** 팔레트 번호 - 상품이 속한 팔레트 식별자 */
	palletId: string;

	/** 상품 정보 (기존 시스템과 호환) */
	productInfo?: any;
}

/**
 * 배치 스토리지 상태 인터페이스
 * 로컬스토리지에 저장되는 전체 상태 정보를 정의합니다.
 */
export interface BatchStorageState {
	/** 팔레트별 상품 목록 (팔레트 ID를 키로 사용) */
	pallets: Record<
		string,
		{
			/** 팔레트에 속한 상품 목록 */
			products: BatchProductData[];

			/** 팔레트 정보 (필요시 확장) */
			palletInfo?: any;
		}
	>;

	/** 전체 상품 수 */
	totalCount: number;

	/** 대기 중인 상품 수 */
	pendingCount: number;

	/** 성공한 상품 수 */
	successCount: number;

	/** 실패한 상품 수 */
	failedCount: number;

	/** 마지막 업데이트 시간 (타임스탬프) */
	lastUpdated: number;

	/** 현재 선택된 팔레트 ID */
	currentPalletId: string;
}

/**
 * 스토리지 용량 정보 인터페이스
 * 로컬스토리지 용량 관리를 위한 정보를 정의합니다.
 */
export interface StorageCapacityInfo {
	/** 사용 중인 용량 (KB) */
	used: number;

	/** 사용 가능한 용량 (KB) */
	available: number;

	/** 전체 용량 (KB) */
	total: number;

	/** 사용 비율 (%) */
	percentUsed: number;

	/** 용량 제한 근접 여부 */
	isNearLimit: boolean;

	/** 배치 관련 데이터 사용 용량 (KB) */
	batchUsed?: number;

	/** 배치 관련 데이터 사용 비율 (%) */
	batchPercentUsed?: number;
}

/**
 * 배치 상품 필터 옵션
 * 상품 목록 조회 시 필터링 옵션을 정의합니다.
 */
export interface BatchProductFilter {
	/** 상태별 필터링 */
	status?: 'pending' | 'submitting' | 'success' | 'failed' | 'all';

	/** 팔레트 ID 기준 필터링 */
	palletId?: string;

	/** 검색어 (QAID 또는 상품 정보 내 텍스트 검색) */
	searchText?: string;
}

/**
 * 배치 저장 결과 인터페이스
 * 서버 전송 결과를 정의합니다.
 */
export interface BatchSaveResult {
	/** 전체 성공 여부 */
	success: boolean;

	/** 결과 메시지 */
	message: string;

	/** 성공한 상품 ID 목록 */
	successIds: string[];

	/** 실패한 상품 정보 목록 */
	failedItems: Array<{
		id: string;
		qaid: string;
		error: string;
	}>;
}

/**
 * 로컬스토리지 키 상수
 * 로컬스토리지에 데이터 저장 시 사용하는 키 값들을 정의합니다.
 */
export const BATCH_STORAGE_KEYS = {
	/** 배치 스토리지 상태 저장 키 */
	STATE: 'batch_pallet_storage_state',

	/** 마지막 확인 시간 저장 키 */
	LAST_CHECK: 'batch_pallet_last_check',

	/** 버전 정보 저장 키 (데이터 구조 변경 시 마이그레이션에 사용) */
	VERSION: 'batch_pallet_storage_version'
};

/**
 * 현재 스토리지 데이터 버전
 * 데이터 구조 변경 시 버전을 올려 마이그레이션 로직을 실행하도록 합니다.
 */
export const CURRENT_STORAGE_VERSION = '1.0';
