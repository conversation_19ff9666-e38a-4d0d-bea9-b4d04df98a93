// 모니터 사이즈 관리 관련 타입 정의

export type MonitorUnit = 'INCH' | 'CM';
export type MonitorBrand = 'brand' | 'general';

export interface MonitorSizeItem {
  id: number;
  name: string; // 불변
  brand: MonitorBrand;
  size: number;
  unit: MonitorUnit;
  name_hash: string; // 불변
  created_at: string;
  updated_at: string;
}

export interface MonitorSizeSearchParams {
  name?: string;
  name_hash?: string;
  brand?: MonitorBrand;
  unit?: MonitorUnit;
  min_size?: number;
  max_size?: number;
  pageSize?: number;
  page?: number;
}

export interface PaginationMetaBasic {
  total: number;
  per_page: number;
  current_page: number;
  last_page: number;
}

export interface MonitorSizeSearchResponse {
  items: MonitorSizeItem[];
  pagination: PaginationMetaBasic;
}


