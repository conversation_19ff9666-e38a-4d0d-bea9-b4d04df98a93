/**
 * 오디오 관리 시스템
 * Repair 시스템과 Pallet 시스템에서 공통으로 사용하는 오디오 피드백 관리
 */

// 오디오 이벤트 타입 정의
export enum AudioEvent {
	CHECK_LOCATION_STORE = 'check_location_store',
	COMPLETE_CHECKIN = 'complete_checkin_audio',
	COMPLETING_CHECKIN = 'completing_checkin_audio',
	COMPLETING_REPAIR = 'completing_repair_in_audio',
	FAIL_AND_RETRY = 'fail_and_retry_again',
	PRINT_LABEL = 'print_label_audio',
	REGISTER_COMPLETE = 'register_complete_audio',
	SCAN_BARCODE = 'scan_barcode_audio',
	SCAN_COMPLETE = 'scan_complete_audio',
	SCAN_LABEL = 'scan_label_audio',
	SCAN_LEVEL = 'scan_level_audio',
	SCAN_PALLET = 'scan_pallet_audio',
	UNPUTABLE_ON_PACKED_PALLET = 'unputable_on_packed_pallet',
	WRITE_LABEL = 'write_label_audio',
	WRONG_PALLET = 'wrong_pallet_audio',
}

// 오디오 파일 경로 매핑
export const AUDIO_FILE_PATHS = {
	[AudioEvent.CHECK_LOCATION_STORE]: () => import('$lib/assets/audio/check_location_store.mp3'),
	[AudioEvent.COMPLETE_CHECKIN]: () => import('$lib/assets/audio/complete_checkin_audio.mp3'),
	[AudioEvent.COMPLETING_CHECKIN]: () => import('$lib/assets/audio/completing_checkin_audio.mp3'),
	[AudioEvent.COMPLETING_REPAIR]: () => import('$lib/assets/audio/completing_repair_in_audio.mp3'),
	[AudioEvent.FAIL_AND_RETRY]: () => import('$lib/assets/audio/fail_and_retry_again.mp3'),
	[AudioEvent.PRINT_LABEL]: () => import('$lib/assets/audio/print_label_audio.mp3'),
	[AudioEvent.REGISTER_COMPLETE]: () => import('$lib/assets/audio/register_complete_audio.mp3'),
	[AudioEvent.SCAN_BARCODE]: () => import('$lib/assets/audio/scan_barcode_audio.mp3'),
	[AudioEvent.SCAN_COMPLETE]: () => import('$lib/assets/audio/scan_complete_audio.mp3'),
	[AudioEvent.SCAN_LABEL]: () => import('$lib/assets/audio/scan_label_audio.mp3'),
	[AudioEvent.SCAN_LEVEL]: () => import('$lib/assets/audio/scan_level_audio.mp3'),
	[AudioEvent.SCAN_PALLET]: () => import('$lib/assets/audio/scan_pallet_audio.mp3'),
	[AudioEvent.UNPUTABLE_ON_PACKED_PALLET]: () => import('$lib/assets/audio/unputable_on_packed_pallet.mp3'),
	[AudioEvent.WRITE_LABEL]: () => import('$lib/assets/audio/write_label_audio.mp3'),
	[AudioEvent.WRONG_PALLET]: () => import('$lib/assets/audio/wrong_pallet_audio.mp3'),
} as const;

// 오디오 매니저 설정 타입
export interface AudioManagerConfig {
	enabled?: boolean;
	volume?: number;
	muted?: boolean;
	preloadAll?: boolean;
	playbackDelay?: number;
}

// 오디오 상태 타입
export interface AudioState {
	audioCache: Map<AudioEvent, HTMLAudioElement>;
	isLoading: Set<AudioEvent>;
	config: Required<AudioManagerConfig>;
}

// 글로벌 오디오 상태
const audioState: AudioState = {
	audioCache: new Map(),
	isLoading: new Set(),
	config: {
		enabled: true,
		volume: 1.0,
		muted: false,
		preloadAll: false,
		playbackDelay: 100
	}
};

/**
 * 모든 오디오 파일 사전 로드
 */
export async function preloadAudioFiles(): Promise<void> {
	const audioEvents = Object.values(AudioEvent);
	const loadPromises = audioEvents.map(event => loadAudioFile(event));

	try {
		await Promise.allSettled(loadPromises);
	} catch (error) {
		console.warn('일부 오디오 파일 로드 실패:', error);
	}
}

/**
 * 오디오 파일 로드
 */
export async function loadAudioFile(event: AudioEvent): Promise<HTMLAudioElement> {
	// 이미 로딩 중인 경우 대기
	if (audioState.isLoading.has(event)) {
		return new Promise((resolve, reject) => {
			const checkLoading = () => {
				if (!audioState.isLoading.has(event)) {
					const audio = audioState.audioCache.get(event);
					if (audio) {
						resolve(audio);
					} else {
						reject(new Error(`오디오 로드 실패: ${event}`));
					}
				} else {
					setTimeout(checkLoading, 50);
				}
			};
			checkLoading();
		});
	}

	// 이미 캐시된 경우 반환
	if (audioState.audioCache.has(event)) {
		return audioState.audioCache.get(event)!;
	}

	audioState.isLoading.add(event);

	try {
		const audioModule = await AUDIO_FILE_PATHS[event]();
		const audio = new Audio(audioModule.default);

		// 오디오 설정 적용
		audio.volume = audioState.config.volume;
		audio.muted = audioState.config.muted;
		audio.preload = 'auto';

		// 로드 완료 대기
		await new Promise<void>((resolve, reject) => {
			audio.oncanplaythrough = () => resolve();
			audio.onerror = () => reject(new Error(`오디오 로드 실패: ${event}`));
			audio.load();
		});

		audioState.audioCache.set(event, audio);
		return audio;
	} catch (error) {
		console.warn(`오디오 파일 로드 실패: ${event}`, error);
		throw error;
	} finally {
		audioState.isLoading.delete(event);
	}
}

/**
 * 오디오 재생
 */
export async function play(event: AudioEvent): Promise<void> {
	if (!audioState.config.enabled) {
		return;
	}

	try {
		const audio = await loadAudioFile(event);

		// 재생 지연 적용
		if (audioState.config.playbackDelay > 0) {
			await new Promise(resolve => setTimeout(resolve, audioState.config.playbackDelay));
		}

		// 기존 재생 중지 및 처음부터 재생
		audio.currentTime = 0;

		await audio.play();

	} catch (error) {
		console.warn(`오디오 재생 실패: ${event}`, error);
		// 사용자에게 오류 메시지 표시하지 않음 (오디오는 선택적 기능)
	}
}

/**
 * 모든 오디오 중지
 */
export function stopAll(): void {
	audioState.audioCache.forEach(audio => {
		audio.pause();
		audio.currentTime = 0;
	});
}

/**
 * 볼륨 설정
 */
export function setVolume(volume: number): void {
	audioState.config.volume = Math.max(0, Math.min(1, volume));
	audioState.audioCache.forEach(audio => {
		audio.volume = audioState.config.volume;
	});
}

/**
 * 음소거 설정
 */
export function setMuted(muted: boolean): void {
	audioState.config.muted = muted;
	audioState.audioCache.forEach(audio => {
		audio.muted = muted;
	});
}

/**
 * 오디오 활성화/비활성화
 */
export function setEnabled(enabled: boolean): void {
	audioState.config.enabled = enabled;
	if (!enabled) {
		stopAll();
	}
}

/**
 * 캐시된 오디오 파일 정리
 */
export function clearCache(): void {
	stopAll();
	audioState.audioCache.clear();
}

/**
 * 현재 설정 반환
 */
export function getConfig(): AudioManagerConfig {
	return { ...audioState.config };
}

/**
 * 오디오 시스템 초기화
 */
export function initAudio(config: AudioManagerConfig = {}): void {
	// 설정 적용
	audioState.config = {
		enabled: config.enabled ?? audioState.config.enabled,
		volume: config.volume ?? audioState.config.volume,
		muted: config.muted ?? audioState.config.muted,
		preloadAll: config.preloadAll ?? audioState.config.preloadAll,
		playbackDelay: config.playbackDelay ?? audioState.config.playbackDelay
	};

	// 사전 로드 설정이 있으면 로드 시작
	if (audioState.config.preloadAll) {
		preloadAudioFiles();
	}
}

/**
 * 간편한 오디오 재생 함수
 */
export async function playAudio(event: AudioEvent): Promise<void> {
	await play(event);
}

/**
 * 예제: 바코드 스캔 관련 오디오 재생 헬퍼 함수들
 */
export const AudioHelper = {
	/**
	 * 바코드 스캔 시 오디오
	 */
	async scanBarcode(): Promise<void> {
		await playAudio(AudioEvent.SCAN_BARCODE);
	},
};