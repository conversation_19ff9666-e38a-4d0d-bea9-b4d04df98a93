export interface SettingItem {
	name: string;
	code: string;
	settings: Record<string, any>;
}

export class PrinterDatabase {
	private db: IDBDatabase | null = null;
	private readonly DB_NAME = "EmployeeNotificationDB";
	private readonly STORE_NAME = "print_settings";
	private readonly DB_VERSION = 1;

	// 기본 설정값 정의
	private readonly DEFAULT_SETTINGS: SettingItem[] = [
		{
			name: '폰트(글꼴)',
			code: 'font',
			settings: { fontFamily: 'Roboto' }
		},
		{
			name: '바코드',
			code: 'barcode',
			settings: {
				x: 0, // 바코드 이미지의 시작위치
				y: 3, // 바코드 이미지의 시작위치(높이)
				// width: 130, // 바코드 이미지의 너비(최대: 142)
				// height: 43, // 바코드 이미지의 높이(최대: 43)
			}
		},
		{
			name: 'QAID',
			code: 'qaid',
			settings: { fontSize: 10 }
		},
		{
			name: '출력일 형식',
			code: 'date',
			settings: { x: 71, y: 67, format: 'MM/DD/YY', fontSize: 6, textColor: '#000000', bold: true, italics: true }
		},
		{ name: '번호', code: 'user', settings: { x: 120, y: 67, fontSize: 6,  textColor: '#000000', bold: true, italics: true } }
	];

	async initDatabase(): Promise<void> {
		try {
			return new Promise((resolve, reject) => {
				const request = indexedDB.open(this.DB_NAME, this.DB_VERSION);
				console.log("indexedDB init");

				request.onerror = () => {
					console.error("indexedDB 에러:", request.error);
					reject(request.error);
				};

				request.onsuccess = () => {
					console.log("indexedDB success");
					this.db = request.result;
					resolve();
				};

				request.onupgradeneeded = (event: IDBVersionChangeEvent) => {
					console.log("indexedDB upgrade needed");
					const db = (event.target as IDBOpenDBRequest).result;

					// 새로운 스토어 생성
					const store = db.createObjectStore(this.STORE_NAME, { keyPath: "settingName" });

					// 초기 데이터 추가
					store.add({
						settingName: "기본(default)",
						settings: this.DEFAULT_SETTINGS,
						updatedAt: new Date().toISOString()
					});
				};
			});
		} catch (error) {
			console.error("Database initialization failed:", error);
			throw error;
		}
	}

	async saveSettings(settingName: string, settings: any): Promise<void> {
		return new Promise((resolve, reject) => {
			if (!this.db) throw new Error("데이터베이스가 초기화되지 않았습니다.");
			if (!settingName.trim()) throw new Error("설정 이름은 필수입니다.");

			const transaction = this.db.transaction([this.STORE_NAME], "readwrite");
			const store = transaction.objectStore(this.STORE_NAME);

			const safeSettings = JSON.parse(JSON.stringify({
				settingName,
				settings,
				updatedAt: new Date().toISOString()
			}));

			const request = store.put(safeSettings);

			request.onsuccess = () => resolve();
			request.onerror = () => reject(request.error);
		});
	}

	async getSettings(settingName: string): Promise<any> {
		return new Promise((resolve, reject) => {
			if (!this.db) throw new Error("데이터베이스가 초기화되지 않았습니다.");

			const transaction = this.db.transaction([this.STORE_NAME], "readonly");
			const store = transaction.objectStore(this.STORE_NAME);
			const request = store.get(settingName);

			request.onsuccess = () => {
				resolve(request.result?.settings || this.DEFAULT_SETTINGS);
			};
			request.onerror = () => reject(request.error);
		});
	}

	async getAllSettingNames(): Promise<string[]> {
		return new Promise((resolve, reject) => {
			if (!this.db) throw new Error("데이터베이스가 초기화되지 않았습니다.");

			const transaction = this.db.transaction([this.STORE_NAME], "readonly");
			const store = transaction.objectStore(this.STORE_NAME);
			const request = store.getAllKeys();

			request.onsuccess = () => {
				resolve(request.result as string[]);
			};
			request.onerror = () => reject(request.error);
		});
	}

	async deleteSettings(settingName: string): Promise<void> {
		return new Promise((resolve, reject) => {
			if (!this.db) throw new Error("데이터베이스가 초기화되지 않았습니다.");
			if (!settingName.trim()) throw new Error("설정 이름은 필수입니다.");

			// "기본(default)" 설정은 삭제할 수 없도록 보호
			if (settingName === "기본(default)") {
				reject(new Error("기본 설정은 삭제할 수 없습니다."));
				return;
			}

			const transaction = this.db.transaction([this.STORE_NAME], "readwrite");
			const store = transaction.objectStore(this.STORE_NAME);
			const request = store.delete(settingName);

			request.onsuccess = () => resolve();
			request.onerror = () => reject(request.error);
		});
	}
}