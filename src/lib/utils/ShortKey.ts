import {goto} from '$app/navigation';

const shortcuts = [
	{ key: "1", path: "/works"},
	{ key: "2", path: "/works/inspects"},
];
// 입고목록(Ctrl + 1)
export const shortKey1 = (e: KeyboardEvent) => {
	if (e.ctrlKey && e.key === "1") {
		e.preventDefault();
		goto(`/works`);
	}
}

// 입고검수(Ctrl + 2)
export const shortKey2 = (e: KeyboardEvent) => {
	if (e.ctrlKey && e.key === "2") {
		e.preventDefault();
		goto(`/works/inspects`);
	}
}