/**
 * 배치 데이터 복원 기능 테스트
 *
 * 요구사항 6.3: 로컬스토리지 데이터가 복원될 때 사용자에게 복원된 데이터가 있음을 알림
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
	detectDataRestore,
	markDataRestored,
	isDataRestored,
	initBatchStorage,
	getBatchState,
	saveBatchState
} from '../batchStorageUtils';
import type { BatchStorageState } from '../../types/batchTypes';
import { BATCH_STORAGE_KEYS } from '../../types/batchTypes';

// 로컬스토리지 모킹
const localStorageMock = (() => {
	let store: Record<string, string> = {};

	return {
		getItem: (key: string) => store[key] || null,
		setItem: (key: string, value: string) => {
			store[key] = value;
		},
		removeItem: (key: string) => {
			delete store[key];
		},
		clear: () => {
			store = {};
		},
		get length() {
			return Object.keys(store).length;
		},
		key: (index: number) => Object.keys(store)[index] || null
	};
})();

// 전역 localStorage 모킹
Object.defineProperty(window, 'localStorage', {
	value: localStorageMock
});

describe('배치 데이터 복원 기능', () => {
	beforeEach(() => {
		// 각 테스트 전에 로컬스토리지 초기화
		localStorageMock.clear();
		initBatchStorage(true);
	});

	afterEach(() => {
		localStorageMock.clear();
	});

	describe('detectDataRestore', () => {
		it('복원 가능한 데이터가 없을 때 false를 반환해야 함', () => {
			const result = detectDataRestore('A-1-1-1-1');

			expect(result.hasRestorableData).toBe(false);
			expect(result.restoreInfo).toBeNull();
			expect(result.allPallets).toHaveLength(0);
		});

		it('최근 데이터는 복원 대상에서 제외되어야 함', () => {
			// 현재 시간의 데이터 생성 (5분 이내)
			const currentTime = Date.now();
			const recentTimestamp = currentTime - 2 * 60 * 1000; // 2분 전

			const state: BatchStorageState = {
				pallets: {
					'A-1-1-1-1': {
						products: [
							{
								id: 'test-1',
								qaid: 'QA001',
								status: 'pending',
								timestamp: recentTimestamp,
								palletId: 'A-1-1-1-1',
								productInfo: { test: 'data' }
							}
						],
						palletInfo: {}
					}
				},
				totalCount: 1,
				pendingCount: 1,
				successCount: 0,
				failedCount: 0,
				lastUpdated: currentTime,
				currentPalletId: 'A-1-1-1-1'
			};

			saveBatchState(state);

			const result = detectDataRestore('A-1-1-1-1');

			expect(result.hasRestorableData).toBe(false);
			expect(result.restoreInfo).toBeNull();
		});

		it('오래된 대기 중인 데이터는 복원 대상이어야 함', () => {
			// 10분 전 데이터 생성
			const currentTime = Date.now();
			const oldTimestamp = currentTime - 10 * 60 * 1000; // 10분 전

			const state: BatchStorageState = {
				pallets: {
					'A-1-1-1-1': {
						products: [
							{
								id: 'test-1',
								qaid: 'QA001',
								status: 'pending',
								timestamp: oldTimestamp,
								palletId: 'A-1-1-1-1',
								productInfo: { test: 'data' }
							}
						],
						palletInfo: {}
					}
				},
				totalCount: 1,
				pendingCount: 1,
				successCount: 0,
				failedCount: 0,
				lastUpdated: oldTimestamp,
				currentPalletId: 'A-1-1-1-1'
			};

			saveBatchState(state);

			const result = detectDataRestore('A-1-1-1-1');

			expect(result.hasRestorableData).toBe(true);
			expect(result.restoreInfo).not.toBeNull();
			expect(result.restoreInfo?.palletId).toBe('A-1-1-1-1');
			expect(result.restoreInfo?.productCount).toBe(1);
			expect(result.restoreInfo?.pendingCount).toBe(1);
			expect(result.restoreInfo?.failedCount).toBe(0);
			expect(result.restoreInfo?.isCurrentPallet).toBe(true);
			expect(result.restoreInfo?.shouldNotify).toBe(true);
		});

		it('실패한 데이터는 복원 대상이어야 함', () => {
			// 10분 전 실패 데이터 생성
			const currentTime = Date.now();
			const oldTimestamp = currentTime - 10 * 60 * 1000; // 10분 전

			const state: BatchStorageState = {
				pallets: {
					'A-1-1-1-2': {
						products: [
							{
								id: 'test-1',
								qaid: 'QA001',
								status: 'failed',
								timestamp: oldTimestamp,
								palletId: 'A-1-1-1-2',
								productInfo: { test: 'data' },
								errorMessage: '서버 오류'
							}
						],
						palletInfo: {}
					}
				},
				totalCount: 1,
				pendingCount: 0,
				successCount: 0,
				failedCount: 1,
				lastUpdated: oldTimestamp,
				currentPalletId: 'A-1-1-1-1'
			};

			saveBatchState(state);

			const result = detectDataRestore('A-1-1-1-1');

			expect(result.hasRestorableData).toBe(true);
			expect(result.restoreInfo).not.toBeNull();
			expect(result.restoreInfo?.palletId).toBe('A-1-1-1-2');
			expect(result.restoreInfo?.failedCount).toBe(1);
			expect(result.restoreInfo?.isCurrentPallet).toBe(false);
		});

		it('성공한 데이터만 있으면 복원 대상이 아니어야 함', () => {
			// 10분 전 성공 데이터 생성
			const currentTime = Date.now();
			const oldTimestamp = currentTime - 10 * 60 * 1000; // 10분 전

			const state: BatchStorageState = {
				pallets: {
					'A-1-1-1-1': {
						products: [
							{
								id: 'test-1',
								qaid: 'QA001',
								status: 'success',
								timestamp: oldTimestamp,
								palletId: 'A-1-1-1-1',
								productInfo: { test: 'data' }
							}
						],
						palletInfo: {}
					}
				},
				totalCount: 1,
				pendingCount: 0,
				successCount: 1,
				failedCount: 0,
				lastUpdated: oldTimestamp,
				currentPalletId: 'A-1-1-1-1'
			};

			saveBatchState(state);

			const result = detectDataRestore('A-1-1-1-1');

			expect(result.hasRestorableData).toBe(false);
			expect(result.restoreInfo).toBeNull();
		});

		it('여러 팔레트 중 가장 최근 복원 대상을 선택해야 함', () => {
			const currentTime = Date.now();
			const oldTimestamp1 = currentTime - 15 * 60 * 1000; // 15분 전
			const oldTimestamp2 = currentTime - 10 * 60 * 1000; // 10분 전

			const state: BatchStorageState = {
				pallets: {
					'A-1-1-1-1': {
						products: [
							{
								id: 'test-1',
								qaid: 'QA001',
								status: 'pending',
								timestamp: oldTimestamp1,
								palletId: 'A-1-1-1-1',
								productInfo: { test: 'data' }
							}
						],
						palletInfo: {}
					},
					'A-1-1-1-2': {
						products: [
							{
								id: 'test-2',
								qaid: 'QA002',
								status: 'pending',
								timestamp: oldTimestamp2,
								palletId: 'A-1-1-1-2',
								productInfo: { test: 'data' }
							}
						],
						palletInfo: {}
					}
				},
				totalCount: 2,
				pendingCount: 2,
				successCount: 0,
				failedCount: 0,
				lastUpdated: oldTimestamp2,
				currentPalletId: 'A-1-1-1-3'
			};

			saveBatchState(state);

			const result = detectDataRestore('A-1-1-1-3');

			expect(result.hasRestorableData).toBe(true);
			expect(result.restoreInfo).not.toBeNull();
			// 더 최근 데이터인 A-1-1-1-2가 선택되어야 함
			expect(result.restoreInfo?.palletId).toBe('A-1-1-1-2');
			expect(result.allPallets).toHaveLength(2);
		});

		it('현재 팔레트를 우선적으로 선택해야 함', () => {
			const currentTime = Date.now();
			const oldTimestamp1 = currentTime - 15 * 60 * 1000; // 15분 전
			const oldTimestamp2 = currentTime - 10 * 60 * 1000; // 10분 전

			const state: BatchStorageState = {
				pallets: {
					'A-1-1-1-1': {
						products: [
							{
								id: 'test-1',
								qaid: 'QA001',
								status: 'pending',
								timestamp: oldTimestamp1,
								palletId: 'A-1-1-1-1',
								productInfo: { test: 'data' }
							}
						],
						palletInfo: {}
					},
					'A-1-1-1-2': {
						products: [
							{
								id: 'test-2',
								qaid: 'QA002',
								status: 'pending',
								timestamp: oldTimestamp2,
								palletId: 'A-1-1-1-2',
								productInfo: { test: 'data' }
							}
						],
						palletInfo: {}
					}
				},
				totalCount: 2,
				pendingCount: 2,
				successCount: 0,
				failedCount: 0,
				lastUpdated: oldTimestamp2,
				currentPalletId: 'A-1-1-1-1'
			};

			saveBatchState(state);

			const result = detectDataRestore('A-1-1-1-1');

			expect(result.hasRestorableData).toBe(true);
			expect(result.restoreInfo).not.toBeNull();
			// 더 오래되었지만 현재 팔레트인 A-1-1-1-1이 선택되어야 함
			expect(result.restoreInfo?.palletId).toBe('A-1-1-1-1');
			expect(result.restoreInfo?.isCurrentPallet).toBe(true);
		});
	});

	describe('markDataRestored', () => {
		it('데이터 복원 상태를 기록해야 함', () => {
			const palletId = 'A-1-1-1-1';

			markDataRestored(palletId);

			const restoreKey = `${BATCH_STORAGE_KEYS.STATE}_restored_${palletId}`;
			const restoredTime = localStorageMock.getItem(restoreKey);

			expect(restoredTime).not.toBeNull();
			expect(parseInt(restoredTime!, 10)).toBeGreaterThan(0);
		});
	});

	describe('isDataRestored', () => {
		it('복원되지 않은 팔레트는 false를 반환해야 함', () => {
			const palletId = 'A-1-1-1-1';

			const result = isDataRestored(palletId);

			expect(result).toBe(false);
		});

		it('복원된 팔레트는 true를 반환해야 함', () => {
			const palletId = 'A-1-1-1-1';

			markDataRestored(palletId);
			const result = isDataRestored(palletId);

			expect(result).toBe(true);
		});

		it('오래된 복원 기록은 무효로 처리해야 함', () => {
			const palletId = 'A-1-1-1-1';
			const restoreKey = `${BATCH_STORAGE_KEYS.STATE}_restored_${palletId}`;

			// 2시간 전 복원 기록 생성
			const oldTime = Date.now() - 2 * 60 * 60 * 1000;
			localStorageMock.setItem(restoreKey, oldTime.toString());

			// 1시간 유효 시간으로 확인
			const result = isDataRestored(palletId, 60 * 60 * 1000);

			expect(result).toBe(false);
			// 오래된 기록은 자동으로 삭제되어야 함
			expect(localStorageMock.getItem(restoreKey)).toBeNull();
		});
	});

	describe('통합 시나리오', () => {
		it('데이터 복원 -> 알림 표시 -> 복원 완료 기록 -> 중복 알림 방지', () => {
			const currentTime = Date.now();
			const oldTimestamp = currentTime - 10 * 60 * 1000; // 10분 전
			const palletId = 'A-1-1-1-1';

			// 1. 오래된 대기 중인 데이터 생성
			const state: BatchStorageState = {
				pallets: {
					[palletId]: {
						products: [
							{
								id: 'test-1',
								qaid: 'QA001',
								status: 'pending',
								timestamp: oldTimestamp,
								palletId: palletId,
								productInfo: { test: 'data' }
							}
						],
						palletInfo: {}
					}
				},
				totalCount: 1,
				pendingCount: 1,
				successCount: 0,
				failedCount: 0,
				lastUpdated: oldTimestamp,
				currentPalletId: palletId
			};

			saveBatchState(state);

			// 2. 데이터 복원 감지 - 알림 표시되어야 함
			const firstResult = detectDataRestore(palletId);
			expect(firstResult.hasRestorableData).toBe(true);
			expect(firstResult.restoreInfo?.shouldNotify).toBe(true);

			// 3. 복원 완료 기록
			markDataRestored(palletId);

			// 4. 다시 확인 - 중복 알림 방지되어야 함
			const secondResult = detectDataRestore(palletId);
			// 데이터는 여전히 있지만 알림은 표시하지 않아야 함
			expect(isDataRestored(palletId)).toBe(true);
		});
	});
});
