/**
 * 배치 스토리지 유틸리티 단위 테스트
 *
 * 이 파일은 batchStorageUtils.ts의 모든 함수에 대한 단위 테스트를 구현합니다.
 * 로컬스토리지 관리, 데이터 검증, 용량 관리 등의 기능을 테스트합니다.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
	initBatchStorage,
	getBatchState,
	saveBatchState,
	resetBatchStorage,
	checkStorageCapacity,
	cleanupStorage,
	checkAutoSaveNeeded,
	attemptDataRecovery,
	createBackup,
	getBackupList,
	restoreFromBackup,
	monitorStorageCapacity,
	enableAutoStorageOptimization,
	setCurrentPalletId,
	getCurrentPalletId,
	getPalletProducts,
	getPalletProductCount,
	hasPallet,
	getAllPallets,
	getStorageStats
} from '../batchStorageUtils';
import type { BatchStorageState, BatchProductData } from '../../types/batchTypes';
import { BATCH_STORAGE_KEYS, CURRENT_STORAGE_VERSION } from '../../types/batchTypes';

// 로컬스토리지 모킹
const mockLocalStorage = (() => {
	let store: Record<string, string> = {};

	return {
		getItem: (key: string) => store[key] || null,
		setItem: (key: string, value: string) => {
			store[key] = value;
		},
		removeItem: (key: string) => {
			delete store[key];
		},
		clear: () => {
			store = {};
		},
		get length() {
			return Object.keys(store).length;
		},
		key: (index: number) => Object.keys(store)[index] || null
	};
})();

// 전역 localStorage 모킹
Object.defineProperty(window, 'localStorage', {
	value: mockLocalStorage
});

describe('배치 스토리지 유틸리티', () => {
	beforeEach(() => {
		// 각 테스트 전에 로컬스토리지 초기화
		mockLocalStorage.clear();
		vi.clearAllTimers();
		vi.clearAllMocks();
	});

	afterEach(() => {
		mockLocalStorage.clear();
		vi.restoreAllMocks();
	});

	describe('initBatchStorage', () => {
		it('새로운 스토리지를 초기화할 수 있어야 함', () => {
			const result = initBatchStorage();

			expect(result.success).toBe(true);
			expect(result.isNew).toBe(true);
			expect(result.message).toContain('새로 초기화');

			// 기본 상태가 저장되었는지 확인
			const stateJson = mockLocalStorage.getItem(BATCH_STORAGE_KEYS.STATE);
			expect(stateJson).not.toBeNull();

			const state = JSON.parse(stateJson!);
			expect(state.pallets).toEqual({});
			expect(state.totalCount).toBe(0);
			expect(state.pendingCount).toBe(0);
			expect(state.successCount).toBe(0);
			expect(state.failedCount).toBe(0);
			expect(state.currentPalletId).toBe('');
		});

		it('기존 스토리지가 있으면 유지해야 함', () => {
			// 기존 데이터 설정
			const existingState: BatchStorageState = {
				pallets: {
					'test-pallet': {
						products: [
							{
								id: 'test-1',
								qaid: 'QA001',
								status: 'pending',
								timestamp: Date.now(),
								palletId: 'test-pallet'
							}
						],
						palletInfo: {}
					}
				},
				totalCount: 1,
				pendingCount: 1,
				successCount: 0,
				failedCount: 0,
				lastUpdated: Date.now(),
				currentPalletId: 'test-pallet'
			};

			mockLocalStorage.setItem(BATCH_STORAGE_KEYS.STATE, JSON.stringify(existingState));
			mockLocalStorage.setItem(BATCH_STORAGE_KEYS.VERSION, CURRENT_STORAGE_VERSION);

			const result = initBatchStorage();

			expect(result.success).toBe(true);
			expect(result.isNew).toBe(false);
			expect(result.message).toContain('기존 배치 스토리지');

			// 기존 데이터가 유지되었는지 확인
			const state = getBatchState();
			expect(state.totalCount).toBe(1);
			expect(state.currentPalletId).toBe('test-pallet');
		});

		it('강제 초기화를 수행할 수 있어야 함', () => {
			// 기존 데이터 설정
			const existingState: BatchStorageState = {
				pallets: {
					'test-pallet': {
						products: [
							{
								id: 'test-1',
								qaid: 'QA001',
								status: 'pending',
								timestamp: Date.now(),
								palletId: 'test-pallet'
							}
						],
						palletInfo: {}
					}
				},
				totalCount: 1,
				pendingCount: 1,
				successCount: 0,
				failedCount: 0,
				lastUpdated: Date.now(),
				currentPalletId: 'test-pallet'
			};

			mockLocalStorage.setItem(BATCH_STORAGE_KEYS.STATE, JSON.stringify(existingState));

			const result = initBatchStorage(true);

			expect(result.success).toBe(true);
			expect(result.isNew).toBe(true);
			expect(result.message).toContain('강제 초기화');

			// 데이터가 초기화되었는지 확인
			const state = getBatchState();
			expect(state.totalCount).toBe(0);
			expect(state.currentPalletId).toBe('');
		});

		it('손상된 데이터가 있으면 안전하게 초기화해야 함', () => {
			// 손상된 JSON 데이터 설정
			mockLocalStorage.setItem(BATCH_STORAGE_KEYS.STATE, 'invalid json');

			const result = initBatchStorage();

			// 실제 동작: resetBatchStorage()가 성공하면 success: true가 반환됨
			expect(result.success).toBe(true);
			expect(result.isNew).toBe(true);
			expect(result.message).toContain('초기화');

			// 안전하게 초기화되었는지 확인
			const state = getBatchState();
			expect(state.totalCount).toBe(0);
		});
	});

	describe('getBatchState', () => {
		it('기본 상태를 반환할 수 있어야 함', () => {
			const state = getBatchState();

			expect(state.pallets).toEqual({});
			expect(state.totalCount).toBe(0);
			expect(state.pendingCount).toBe(0);
			expect(state.successCount).toBe(0);
			expect(state.failedCount).toBe(0);
			expect(state.currentPalletId).toBe('');
			expect(state.lastUpdated).toBeGreaterThan(0);
		});

		it('저장된 상태를 반환할 수 있어야 함', () => {
			const testState: BatchStorageState = {
				pallets: {
					'test-pallet': {
						products: [
							{
								id: 'test-1',
								qaid: 'QA001',
								status: 'pending',
								timestamp: Date.now(),
								palletId: 'test-pallet'
							}
						],
						palletInfo: {}
					}
				},
				totalCount: 1,
				pendingCount: 1,
				successCount: 0,
				failedCount: 0,
				lastUpdated: Date.now(),
				currentPalletId: 'test-pallet'
			};

			mockLocalStorage.setItem(BATCH_STORAGE_KEYS.STATE, JSON.stringify(testState));

			const state = getBatchState();

			expect(state.totalCount).toBe(1);
			expect(state.currentPalletId).toBe('test-pallet');
			expect(state.pallets['test-pallet'].products).toHaveLength(1);
		});

		it('손상된 데이터가 있으면 복구를 시도해야 함', () => {
			// 손상된 JSON 데이터 설정
			mockLocalStorage.setItem(BATCH_STORAGE_KEYS.STATE, 'invalid json');

			const state = getBatchState();

			// 복구 실패 시 기본 상태 반환
			expect(state.totalCount).toBe(0);
			expect(state.pallets).toEqual({});
		});
	});

	describe('saveBatchState', () => {
		it('상태를 저장할 수 있어야 함', () => {
			const testState: BatchStorageState = {
				pallets: {
					'test-pallet': {
						products: [
							{
								id: 'test-1',
								qaid: 'QA001',
								status: 'pending',
								timestamp: Date.now(),
								palletId: 'test-pallet'
							}
						],
						palletInfo: {}
					}
				},
				totalCount: 1,
				pendingCount: 1,
				successCount: 0,
				failedCount: 0,
				lastUpdated: Date.now(),
				currentPalletId: 'test-pallet'
			};

			saveBatchState(testState);

			const savedStateJson = mockLocalStorage.getItem(BATCH_STORAGE_KEYS.STATE);
			expect(savedStateJson).not.toBeNull();

			const savedState = JSON.parse(savedStateJson!);
			expect(savedState.totalCount).toBe(1);
			expect(savedState.currentPalletId).toBe('test-pallet');
		});

		it('저장 시 카운트를 재계산해야 함', () => {
			const testState: BatchStorageState = {
				pallets: {
					'test-pallet': {
						products: [
							{
								id: 'test-1',
								qaid: 'QA001',
								status: 'pending',
								timestamp: Date.now(),
								palletId: 'test-pallet'
							},
							{
								id: 'test-2',
								qaid: 'QA002',
								status: 'success',
								timestamp: Date.now(),
								palletId: 'test-pallet'
							}
						],
						palletInfo: {}
					}
				},
				// 잘못된 카운트 설정
				totalCount: 0,
				pendingCount: 0,
				successCount: 0,
				failedCount: 0,
				lastUpdated: Date.now(),
				currentPalletId: 'test-pallet'
			};

			saveBatchState(testState);

			const savedState = getBatchState();
			// 카운트가 재계산되어야 함
			expect(savedState.totalCount).toBe(2);
			expect(savedState.pendingCount).toBe(1);
			expect(savedState.successCount).toBe(1);
			expect(savedState.failedCount).toBe(0);
		});
	});

	describe('resetBatchStorage', () => {
		it('스토리지를 초기화할 수 있어야 함', () => {
			// 기존 데이터 설정
			const testState: BatchStorageState = {
				pallets: {
					'test-pallet': {
						products: [
							{
								id: 'test-1',
								qaid: 'QA001',
								status: 'pending',
								timestamp: Date.now(),
								palletId: 'test-pallet'
							}
						],
						palletInfo: {}
					}
				},
				totalCount: 1,
				pendingCount: 1,
				successCount: 0,
				failedCount: 0,
				lastUpdated: Date.now(),
				currentPalletId: 'test-pallet'
			};

			mockLocalStorage.setItem(BATCH_STORAGE_KEYS.STATE, JSON.stringify(testState));

			resetBatchStorage();

			const state = getBatchState();
			expect(state.totalCount).toBe(0);
			expect(state.pallets).toEqual({});
			expect(state.currentPalletId).toBe('');
		});
	});

	describe('checkStorageCapacity', () => {
		it('스토리지 용량 정보를 반환할 수 있어야 함', () => {
			// 테스트 데이터 추가
			mockLocalStorage.setItem('test-key', 'test-value');
			mockLocalStorage.setItem('batch_pallet_test', 'batch-data');

			const capacity = checkStorageCapacity(1024); // 1MB 테스트 쿼터

			expect(capacity.total).toBe(1024);
			expect(capacity.used).toBeGreaterThanOrEqual(0);
			expect(capacity.available).toBeLessThanOrEqual(1024);
			expect(capacity.percentUsed).toBeGreaterThanOrEqual(0);
			expect(capacity.batchUsed).toBeGreaterThanOrEqual(0);
			expect(capacity.batchPercentUsed).toBeGreaterThanOrEqual(0);
		});

		it('용량 경고 임계값을 확인할 수 있어야 함', () => {
			// 많은 데이터 추가하여 용량 임계값 초과 시뮬레이션
			const largeData = 'x'.repeat(1000);
			for (let i = 0; i < 100; i++) {
				mockLocalStorage.setItem(`large-data-${i}`, largeData);
			}

			const capacity = checkStorageCapacity(100); // 작은 쿼터로 테스트

			expect(capacity.isNearLimit).toBe(true);
			expect(capacity.percentUsed).toBeGreaterThan(80);
		});
	});

	describe('cleanupStorage', () => {
		beforeEach(() => {
			// 테스트용 상태 설정
			const testState: BatchStorageState = {
				pallets: {
					'test-pallet': {
						products: [
							{
								id: 'test-1',
								qaid: 'QA001',
								status: 'success',
								timestamp: Date.now(),
								palletId: 'test-pallet'
							},
							{
								id: 'test-2',
								qaid: 'QA002',
								status: 'pending',
								timestamp: Date.now(),
								palletId: 'test-pallet'
							},
							{
								id: 'test-3',
								qaid: 'QA003',
								status: 'failed',
								timestamp: Date.now(),
								palletId: 'test-pallet',
								errorMessage: '테스트 오류'
							}
						],
						palletInfo: {}
					}
				},
				totalCount: 3,
				pendingCount: 1,
				successCount: 1,
				failedCount: 1,
				lastUpdated: Date.now(),
				currentPalletId: 'test-pallet'
			};

			saveBatchState(testState);
		});

		it('성공한 상품을 정리할 수 있어야 함', () => {
			const result = cleanupStorage({ removeSuccess: true });

			expect(result.success).toBe(true);
			expect(result.message).toContain('성공한 상품');

			const state = getBatchState();
			const products = state.pallets['test-pallet'].products;
			expect(products.filter((p) => p.status === 'success')).toHaveLength(0);
			expect(products.filter((p) => p.status === 'pending')).toHaveLength(1);
			expect(products.filter((p) => p.status === 'failed')).toHaveLength(1);
		});

		it('오래된 백업을 정리할 수 있어야 함', () => {
			// 테스트용 백업 생성
			for (let i = 0; i < 10; i++) {
				const timestamp = new Date(Date.now() - i * 1000).toISOString().replace(/[:.]/g, '-');
				mockLocalStorage.setItem(`${BATCH_STORAGE_KEYS.STATE}_backup_${timestamp}`, '{}');
			}

			const result = cleanupStorage({
				removeOldBackups: true,
				maxBackupsToKeep: 3
			});

			expect(result.success).toBe(true);
			expect(result.message).toContain('오래된 백업');

			const backupKeys = getBackupList();
			expect(backupKeys.length).toBeLessThanOrEqual(3);
		});

		it('데이터를 최적화할 수 있어야 함', () => {
			const result = cleanupStorage({ compressData: true });

			expect(result.success).toBe(true);
		});
	});

	describe('checkAutoSaveNeeded', () => {
		it('상품 수가 많으면 자동 저장이 필요하다고 판단해야 함', () => {
			// 많은 상품 데이터 생성
			const products: BatchProductData[] = [];
			for (let i = 0; i < 150; i++) {
				products.push({
					id: `test-${i}`,
					qaid: `QA${i.toString().padStart(3, '0')}`,
					status: 'pending',
					timestamp: Date.now(),
					palletId: 'test-pallet'
				});
			}

			const testState: BatchStorageState = {
				pallets: {
					'test-pallet': {
						products,
						palletInfo: {}
					}
				},
				totalCount: products.length,
				pendingCount: products.length,
				successCount: 0,
				failedCount: 0,
				lastUpdated: Date.now(),
				currentPalletId: 'test-pallet'
			};

			saveBatchState(testState);

			const result = checkAutoSaveNeeded();

			expect(result.needed).toBe(true);
			expect(result.reason).toContain('개의 상품이 임시 저장');
		});

		it('상품 수가 적으면 자동 저장이 필요하지 않다고 판단해야 함', () => {
			const testState: BatchStorageState = {
				pallets: {
					'test-pallet': {
						products: [
							{
								id: 'test-1',
								qaid: 'QA001',
								status: 'pending',
								timestamp: Date.now(),
								palletId: 'test-pallet'
							}
						],
						palletInfo: {}
					}
				},
				totalCount: 1,
				pendingCount: 1,
				successCount: 0,
				failedCount: 0,
				lastUpdated: Date.now(),
				currentPalletId: 'test-pallet'
			};

			saveBatchState(testState);

			const result = checkAutoSaveNeeded();

			expect(result.needed).toBe(false);
			expect(result.reason).toBe('');
		});
	});

	describe('백업 및 복원 기능', () => {
		it('백업을 생성할 수 있어야 함', () => {
			const testState: BatchStorageState = {
				pallets: {
					'test-pallet': {
						products: [
							{
								id: 'test-1',
								qaid: 'QA001',
								status: 'pending',
								timestamp: Date.now(),
								palletId: 'test-pallet'
							}
						],
						palletInfo: {}
					}
				},
				totalCount: 1,
				pendingCount: 1,
				successCount: 0,
				failedCount: 0,
				lastUpdated: Date.now(),
				currentPalletId: 'test-pallet'
			};

			saveBatchState(testState);

			createBackup();

			const backupKeys = getBackupList();
			expect(backupKeys.length).toBeGreaterThan(0);
		});

		it('백업 목록을 가져올 수 있어야 함', () => {
			// 테스트용 백업 생성
			const timestamp1 = new Date(Date.now() - 1000).toISOString().replace(/[:.]/g, '-');
			const timestamp2 = new Date().toISOString().replace(/[:.]/g, '-');

			mockLocalStorage.setItem(`${BATCH_STORAGE_KEYS.STATE}_backup_${timestamp1}`, '{}');
			mockLocalStorage.setItem(`${BATCH_STORAGE_KEYS.STATE}_backup_${timestamp2}`, '{}');

			const backupKeys = getBackupList();

			expect(backupKeys.length).toBe(2);
			// 최신 백업이 먼저 와야 함
			expect(backupKeys[0]).toContain(timestamp2.substring(0, 10));
		});

		it('백업에서 복원할 수 있어야 함', () => {
			const testState: BatchStorageState = {
				pallets: {
					'test-pallet': {
						products: [
							{
								id: 'test-1',
								qaid: 'QA001',
								status: 'pending',
								timestamp: Date.now(),
								palletId: 'test-pallet'
							}
						],
						palletInfo: {}
					}
				},
				totalCount: 1,
				pendingCount: 1,
				successCount: 0,
				failedCount: 0,
				lastUpdated: Date.now(),
				currentPalletId: 'test-pallet'
			};

			const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
			const backupKey = `${BATCH_STORAGE_KEYS.STATE}_backup_${timestamp}`;

			mockLocalStorage.setItem(backupKey, JSON.stringify(testState));

			// 현재 상태를 다른 값으로 변경
			resetBatchStorage();

			const result = restoreFromBackup(backupKey);

			expect(result).toBe(true);

			const restoredState = getBatchState();
			expect(restoredState.totalCount).toBe(1);
			expect(restoredState.currentPalletId).toBe('test-pallet');
		});
	});

	describe('팔레트 관리 기능', () => {
		beforeEach(() => {
			const testState: BatchStorageState = {
				pallets: {
					'pallet-1': {
						products: [
							{
								id: 'test-1',
								qaid: 'QA001',
								status: 'pending',
								timestamp: Date.now(),
								palletId: 'pallet-1'
							},
							{
								id: 'test-2',
								qaid: 'QA002',
								status: 'success',
								timestamp: Date.now(),
								palletId: 'pallet-1'
							}
						],
						palletInfo: {}
					},
					'pallet-2': {
						products: [
							{
								id: 'test-3',
								qaid: 'QA003',
								status: 'failed',
								timestamp: Date.now(),
								palletId: 'pallet-2',
								errorMessage: '테스트 오류'
							}
						],
						palletInfo: {}
					}
				},
				totalCount: 3,
				pendingCount: 1,
				successCount: 1,
				failedCount: 1,
				lastUpdated: Date.now(),
				currentPalletId: 'pallet-1'
			};

			saveBatchState(testState);
		});

		it('현재 팔레트 ID를 설정할 수 있어야 함', () => {
			setCurrentPalletId('pallet-2');

			const currentPalletId = getCurrentPalletId();
			expect(currentPalletId).toBe('pallet-2');
		});

		it('팔레트별 상품 목록을 가져올 수 있어야 함', () => {
			const pallet1Products = getPalletProducts('pallet-1');
			expect(pallet1Products).toHaveLength(2);

			const pallet2Products = getPalletProducts('pallet-2');
			expect(pallet2Products).toHaveLength(1);

			const nonExistentProducts = getPalletProducts('non-existent');
			expect(nonExistentProducts).toHaveLength(0);
		});

		it('팔레트별 상품 수를 가져올 수 있어야 함', () => {
			const pallet1Count = getPalletProductCount('pallet-1');
			expect(pallet1Count.total).toBe(2);
			expect(pallet1Count.pending).toBe(1);
			expect(pallet1Count.success).toBe(1);
			expect(pallet1Count.failed).toBe(0);

			const pallet2Count = getPalletProductCount('pallet-2');
			expect(pallet2Count.total).toBe(1);
			expect(pallet2Count.pending).toBe(0);
			expect(pallet2Count.success).toBe(0);
			expect(pallet2Count.failed).toBe(1);
		});

		it('팔레트 존재 여부를 확인할 수 있어야 함', () => {
			expect(hasPallet('pallet-1')).toBe(true);
			expect(hasPallet('pallet-2')).toBe(true);
			expect(hasPallet('non-existent')).toBe(false);
		});

		it('모든 팔레트 목록을 가져올 수 있어야 함', () => {
			const allPallets = getAllPallets();

			expect(allPallets).toHaveLength(2);
			expect(allPallets.find((p) => p.palletId === 'pallet-1')).toBeDefined();
			expect(allPallets.find((p) => p.palletId === 'pallet-2')).toBeDefined();

			const pallet1Info = allPallets.find((p) => p.palletId === 'pallet-1')!;
			expect(pallet1Info.productCount).toBe(2);
			expect(pallet1Info.pendingCount).toBe(1);
			expect(pallet1Info.successCount).toBe(1);
			expect(pallet1Info.failedCount).toBe(0);
		});
	});

	describe('모니터링 기능', () => {
		it('스토리지 용량 모니터링을 설정할 수 있어야 함', () => {
			vi.useFakeTimers();

			const callback = vi.fn();
			const stopMonitoring = monitorStorageCapacity(callback, 1000);

			expect(typeof stopMonitoring).toBe('function');

			// 타이머 진행
			vi.advanceTimersByTime(1000);

			stopMonitoring();

			vi.useRealTimers();
		});

		it('자동 스토리지 최적화를 설정할 수 있어야 함', () => {
			vi.useFakeTimers();

			const callback = vi.fn();
			const stopOptimization = enableAutoStorageOptimization(50, callback);

			expect(typeof stopOptimization).toBe('function');

			stopOptimization();

			vi.useRealTimers();
		});
	});

	describe('스토리지 통계', () => {
		it('스토리지 사용량 통계를 가져올 수 있어야 함', () => {
			// 테스트 데이터 추가
			mockLocalStorage.setItem('test-key', 'test-value');
			mockLocalStorage.setItem('batch_pallet_test', 'batch-data');
			mockLocalStorage.setItem('other_key', 'other-value');

			const stats = getStorageStats();

			expect(stats.totalItems).toBeGreaterThan(0);
			expect(stats.batchItems).toBeGreaterThan(0);
			expect(stats.keysByPrefix).toBeDefined();
			expect(stats.largestItems).toBeDefined();
			expect(Array.isArray(stats.largestItems)).toBe(true);
		});
	});

	describe('데이터 복구', () => {
		it('손상된 데이터를 복구할 수 있어야 함', () => {
			// 부분적으로 손상된 데이터 설정
			const partiallyCorruptedState = {
				pallets: {
					'test-pallet': {
						products: [
							{
								id: 'test-1',
								qaid: 'QA001',
								status: 'pending',
								timestamp: Date.now(),
								palletId: 'test-pallet'
							},
							// 손상된 상품 데이터
							{
								id: null,
								qaid: null,
								status: 'invalid'
							}
						],
						palletInfo: {}
					}
				},
				// 일부 필수 속성 누락
				totalCount: 'invalid',
				currentPalletId: 'test-pallet'
			};

			mockLocalStorage.setItem(BATCH_STORAGE_KEYS.STATE, JSON.stringify(partiallyCorruptedState));

			const result = attemptDataRecovery();

			expect(result).toBe(true);

			const recoveredState = getBatchState();
			expect(recoveredState.pallets['test-pallet'].products).toHaveLength(1);
			expect(recoveredState.totalCount).toBe(1);
		});

		it('완전히 손상된 데이터는 복구할 수 없어야 함', () => {
			mockLocalStorage.setItem(BATCH_STORAGE_KEYS.STATE, 'completely invalid json');

			const result = attemptDataRecovery();

			expect(result).toBe(false);

			const state = getBatchState();
			expect(state.totalCount).toBe(0);
		});
	});
});
