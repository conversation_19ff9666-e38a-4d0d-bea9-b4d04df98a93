/**
 * 인증 관련 에러 처리 유틸리티
 *
 * JWT 인증 시스템에서 발생할 수 있는 모든 에러 타입을 정의하고
 * 사용자 친화적인 에러 메시지 및 처리 로직을 제공합니다.
 */

import type { AxiosError } from 'axios';

/**
 * 인증 에러 타입 정의
 */
export type AuthErrorType =
	// 로그인 관련 에러
	| 'INVALID_CREDENTIALS' // 잘못된 로그인 정보
	| 'ACCOUNT_LOCKED' // 계정 잠금
	| 'ACCOUNT_DISABLED' // 계정 비활성화
	| 'TOO_MANY_ATTEMPTS' // 로그인 시도 횟수 초과

	// 토큰 관련 에러
	| 'TOKEN_EXPIRED' // 토큰 만료
	| 'TOKEN_INVALID' // 유효하지 않은 토큰
	| 'TOKEN_MALFORMED' // 잘못된 형식의 토큰
	| 'REFRESH_FAILED' // 토큰 갱신 실패
	| 'TOKEN_REVOKED' // 토큰 취소됨

	// 권한 관련 에러
	| 'INSUFFICIENT_PERMISSIONS' // 권한 부족
	| 'ACCESS_DENIED' // 접근 거부
	| 'ROLE_REQUIRED' // 특정 역할 필요

	// 네트워크 관련 에러
	| 'NETWORK_ERROR' // 네트워크 연결 오류
	| 'TIMEOUT_ERROR' // 요청 시간 초과
	| 'CONNECTION_REFUSED' // 연결 거부

	// 서버 관련 에러
	| 'SERVER_ERROR' // 서버 내부 오류
	| 'SERVICE_UNAVAILABLE' // 서비스 이용 불가
	| 'MAINTENANCE_MODE' // 점검 모드

	// 클라이언트 관련 에러
	| 'VALIDATION_ERROR' // 입력 데이터 검증 오류
	| 'MISSING_REQUIRED_FIELD' // 필수 필드 누락
	| 'INVALID_FORMAT' // 잘못된 형식

	// 기타 에러
	| 'UNKNOWN_ERROR' // 알 수 없는 오류
	| 'INITIALIZATION_ERROR' // 초기화 오류
	| 'STORAGE_ERROR' // 저장소 오류
	| 'PLATFORM_ERROR'; // 플랫폼 관련 오류

/**
 * 인증 에러 심각도 레벨
 */
export type AuthErrorSeverity = 'low' | 'medium' | 'high' | 'critical';

/**
 * 인증 에러 카테고리
 */
export type AuthErrorCategory = 'auth' | 'network' | 'server' | 'client' | 'system';

/**
 * 인증 에러 인터페이스
 */
export interface AuthError {
	// 기본 정보
	type: AuthErrorType;
	message: string;
	code?: string;
	statusCode?: number;

	// 추가 정보
	severity: AuthErrorSeverity;
	category: AuthErrorCategory;
	timestamp: Date;

	// 상세 정보
	details?: any;
	originalError?: any;

	// 사용자 액션
	userMessage: string;
	actionRequired?: 'none' | 'retry' | 'login' | 'contact_support';
	retryable: boolean;

	// 디버그 정보
	debugInfo?: {
		stack?: string;
		context?: any;
		platform?: string;
		userAgent?: string;
	};
}

/**
 * 에러 타입별 메타데이터
 */
const errorMetadata: Record<
	AuthErrorType,
	{
		severity: AuthErrorSeverity;
		category: AuthErrorCategory;
		userMessage: string;
		actionRequired: AuthError['actionRequired'];
		retryable: boolean;
	}
> = {
	// 로그인 관련 에러
	INVALID_CREDENTIALS: {
		severity: 'medium',
		category: 'auth',
		userMessage: '아이디 또는 비밀번호가 올바르지 않습니다.',
		actionRequired: 'none',
		retryable: true
	},
	ACCOUNT_LOCKED: {
		severity: 'high',
		category: 'auth',
		userMessage: '계정이 잠겨있습니다. 관리자에게 문의하세요.',
		actionRequired: 'contact_support',
		retryable: false
	},
	ACCOUNT_DISABLED: {
		severity: 'high',
		category: 'auth',
		userMessage: '비활성화된 계정입니다. 관리자에게 문의하세요.',
		actionRequired: 'contact_support',
		retryable: false
	},
	TOO_MANY_ATTEMPTS: {
		severity: 'medium',
		category: 'auth',
		userMessage: '로그인 시도 횟수를 초과했습니다. 잠시 후 다시 시도하세요.',
		actionRequired: 'retry',
		retryable: true
	},

	// 토큰 관련 에러
	TOKEN_EXPIRED: {
		severity: 'low',
		category: 'auth',
		userMessage: '로그인이 만료되었습니다. 다시 로그인해주세요.',
		actionRequired: 'login',
		retryable: false
	},
	TOKEN_INVALID: {
		severity: 'medium',
		category: 'auth',
		userMessage: '인증 정보가 유효하지 않습니다. 다시 로그인해주세요.',
		actionRequired: 'login',
		retryable: false
	},
	TOKEN_MALFORMED: {
		severity: 'medium',
		category: 'auth',
		userMessage: '인증 정보 형식이 올바르지 않습니다.',
		actionRequired: 'login',
		retryable: false
	},
	REFRESH_FAILED: {
		severity: 'medium',
		category: 'auth',
		userMessage: '인증 갱신에 실패했습니다. 다시 로그인해주세요.',
		actionRequired: 'login',
		retryable: false
	},
	TOKEN_REVOKED: {
		severity: 'high',
		category: 'auth',
		userMessage: '인증이 취소되었습니다. 다시 로그인해주세요.',
		actionRequired: 'login',
		retryable: false
	},

	// 권한 관련 에러
	INSUFFICIENT_PERMISSIONS: {
		severity: 'medium',
		category: 'auth',
		userMessage: '이 기능을 사용할 권한이 없습니다.',
		actionRequired: 'contact_support',
		retryable: false
	},
	ACCESS_DENIED: {
		severity: 'medium',
		category: 'auth',
		userMessage: '접근이 거부되었습니다.',
		actionRequired: 'contact_support',
		retryable: false
	},
	ROLE_REQUIRED: {
		severity: 'medium',
		category: 'auth',
		userMessage: '이 기능은 특정 권한이 필요합니다.',
		actionRequired: 'contact_support',
		retryable: false
	},

	// 네트워크 관련 에러
	NETWORK_ERROR: {
		severity: 'medium',
		category: 'network',
		userMessage: '네트워크 연결을 확인해주세요.',
		actionRequired: 'retry',
		retryable: true
	},
	TIMEOUT_ERROR: {
		severity: 'medium',
		category: 'network',
		userMessage: '요청 시간이 초과되었습니다. 다시 시도해주세요.',
		actionRequired: 'retry',
		retryable: true
	},
	CONNECTION_REFUSED: {
		severity: 'high',
		category: 'network',
		userMessage: '서버에 연결할 수 없습니다. 잠시 후 다시 시도해주세요.',
		actionRequired: 'retry',
		retryable: true
	},

	// 서버 관련 에러
	SERVER_ERROR: {
		severity: 'high',
		category: 'server',
		userMessage: '서버 오류가 발생했습니다. 잠시 후 다시 시도해주세요.',
		actionRequired: 'retry',
		retryable: true
	},
	SERVICE_UNAVAILABLE: {
		severity: 'high',
		category: 'server',
		userMessage: '서비스를 일시적으로 이용할 수 없습니다.',
		actionRequired: 'retry',
		retryable: true
	},
	MAINTENANCE_MODE: {
		severity: 'medium',
		category: 'server',
		userMessage: '시스템 점검 중입니다. 잠시 후 다시 이용해주세요.',
		actionRequired: 'retry',
		retryable: true
	},

	// 클라이언트 관련 에러
	VALIDATION_ERROR: {
		severity: 'low',
		category: 'client',
		userMessage: '입력 정보를 확인해주세요.',
		actionRequired: 'none',
		retryable: true
	},
	MISSING_REQUIRED_FIELD: {
		severity: 'low',
		category: 'client',
		userMessage: '필수 입력 항목을 확인해주세요.',
		actionRequired: 'none',
		retryable: true
	},
	INVALID_FORMAT: {
		severity: 'low',
		category: 'client',
		userMessage: '입력 형식이 올바르지 않습니다.',
		actionRequired: 'none',
		retryable: true
	},

	// 기타 에러
	UNKNOWN_ERROR: {
		severity: 'medium',
		category: 'system',
		userMessage: '알 수 없는 오류가 발생했습니다.',
		actionRequired: 'retry',
		retryable: true
	},
	INITIALIZATION_ERROR: {
		severity: 'critical',
		category: 'system',
		userMessage: '시스템 초기화에 실패했습니다.',
		actionRequired: 'contact_support',
		retryable: true
	},
	STORAGE_ERROR: {
		severity: 'medium',
		category: 'system',
		userMessage: '데이터 저장 중 오류가 발생했습니다.',
		actionRequired: 'retry',
		retryable: true
	},
	PLATFORM_ERROR: {
		severity: 'medium',
		category: 'system',
		userMessage: '플랫폼 관련 오류가 발생했습니다.',
		actionRequired: 'contact_support',
		retryable: false
	}
};

/**
 * AuthError 객체를 생성합니다.
 */
export function createAuthError(
	type: AuthErrorType,
	message?: string,
	options: {
		code?: string;
		statusCode?: number;
		details?: any;
		originalError?: any;
		context?: any;
	} = {}
): AuthError {
	const metadata = errorMetadata[type];
	const timestamp = new Date();

	// 디버그 정보 수집
	const debugInfo: AuthError['debugInfo'] = import.meta.env.DEV
		? {
				stack: new Error().stack,
				context: options.context,
				platform: typeof window !== 'undefined' ? navigator.platform : 'server',
				userAgent: typeof window !== 'undefined' ? navigator.userAgent : undefined
			}
		: undefined;

	return {
		type,
		message: message || `Authentication error: ${type}`,
		code: options.code,
		statusCode: options.statusCode,
		severity: metadata.severity,
		category: metadata.category,
		timestamp,
		details: options.details,
		originalError: options.originalError,
		userMessage: metadata.userMessage,
		actionRequired: metadata.actionRequired,
		retryable: metadata.retryable,
		debugInfo
	};
}

/**
 * HTTP 에러를 AuthError로 변환합니다.
 */
export function parseHttpError(error: AxiosError | any): AuthError {
	// Axios 에러인 경우
	if (error.isAxiosError) {
		const axiosError = error as AxiosError;
		const status = axiosError.response?.status;
		const data = axiosError.response?.data as any;

		// 상태 코드별 에러 타입 결정
		let errorType: AuthErrorType;

		switch (status) {
			case 401:
				if (data?.error === 'token_expired') {
					errorType = 'TOKEN_EXPIRED';
				} else if (data?.error === 'invalid_token') {
					errorType = 'TOKEN_INVALID';
				} else {
					errorType = 'INVALID_CREDENTIALS';
				}
				break;

			case 403:
				if (data?.error === 'insufficient_permissions') {
					errorType = 'INSUFFICIENT_PERMISSIONS';
				} else {
					errorType = 'ACCESS_DENIED';
				}
				break;

			case 422:
				errorType = 'VALIDATION_ERROR';
				break;

			case 429:
				errorType = 'TOO_MANY_ATTEMPTS';
				break;

			case 500:
				errorType = 'SERVER_ERROR';
				break;

			case 502:
			case 503:
				errorType = 'SERVICE_UNAVAILABLE';
				break;

			case 504:
				errorType = 'TIMEOUT_ERROR';
				break;

			default:
				if (axiosError.code === 'NETWORK_ERROR' || !axiosError.response) {
					errorType = 'NETWORK_ERROR';
				} else if (axiosError.code === 'ECONNREFUSED') {
					errorType = 'CONNECTION_REFUSED';
				} else if (axiosError.code === 'TIMEOUT') {
					errorType = 'TIMEOUT_ERROR';
				} else {
					errorType = 'UNKNOWN_ERROR';
				}
		}

		return createAuthError(errorType, axiosError.message, {
			code: axiosError.code,
			statusCode: status,
			details: data,
			originalError: axiosError,
			context: {
				url: axiosError.config?.url,
				method: axiosError.config?.method
			}
		});
	}

	// 일반 에러인 경우
	if (error instanceof Error) {
		return createAuthError('UNKNOWN_ERROR', error.message, {
			originalError: error,
			context: { errorName: error.name }
		});
	}

	// 기타 에러
	return createAuthError('UNKNOWN_ERROR', String(error), {
		originalError: error
	});
}

/**
 * 에러 심각도에 따른 색상을 반환합니다.
 */
export function getErrorColor(severity: AuthErrorSeverity): string {
	switch (severity) {
		case 'low':
			return '#fbbf24'; // yellow-400
		case 'medium':
			return '#f97316'; // orange-500
		case 'high':
			return '#ef4444'; // red-500
		case 'critical':
			return '#dc2626'; // red-600
		default:
			return '#6b7280'; // gray-500
	}
}

/**
 * 에러 아이콘을 반환합니다.
 */
export function getErrorIcon(severity: AuthErrorSeverity): string {
	switch (severity) {
		case 'low':
			return '⚠️';
		case 'medium':
			return '❗';
		case 'high':
			return '🚨';
		case 'critical':
			return '💥';
		default:
			return 'ℹ️';
	}
}

/**
 * 에러 로깅 함수
 */
export function logAuthError(error: AuthError, context?: string): void {
	const logLevel = error.severity === 'critical' || error.severity === 'high' ? 'error' : 'warn';
	const prefix = context ? `[${context}]` : '[Auth Error]';

	console[logLevel](`${prefix} ${error.type}:`, {
		message: error.message,
		userMessage: error.userMessage,
		severity: error.severity,
		category: error.category,
		statusCode: error.statusCode,
		timestamp: error.timestamp,
		details: error.details,
		debugInfo: error.debugInfo
	});

	// 개발 환경에서 원본 에러도 출력
	if (import.meta.env.DEV && error.originalError) {
		console[logLevel](`${prefix} Original Error:`, error.originalError);
	}
}

/**
 * 에러 알림 표시 함수
 */
export function showErrorNotification(
	error: AuthError,
	options: {
		duration?: number;
		showRetryButton?: boolean;
		onRetry?: () => void;
	} = {}
): void {
	// 실제 알림 시스템과 연동하는 부분
	// 여기서는 콘솔 출력으로 대체
	console.log(`[Error Notification] ${error.userMessage}`, {
		severity: error.severity,
		actionRequired: error.actionRequired,
		retryable: error.retryable,
		...options
	});

	// TODO: 실제 토스트 알림이나 모달 표시 로직 구현
}

/**
 * 에러 복구 전략을 제안합니다.
 */
export function getRecoveryStrategy(error: AuthError): {
	action: string;
	description: string;
	autoExecute: boolean;
} {
	switch (error.actionRequired) {
		case 'retry':
			return {
				action: '다시 시도',
				description: '잠시 후 자동으로 다시 시도됩니다.',
				autoExecute: error.retryable
			};

		case 'login':
			return {
				action: '다시 로그인',
				description: '로그인 페이지로 이동하여 다시 로그인해주세요.',
				autoExecute: true
			};

		case 'contact_support':
			return {
				action: '지원팀 문의',
				description: '문제가 지속되면 관리자에게 문의하세요.',
				autoExecute: false
			};

		default:
			return {
				action: '확인',
				description: '입력 내용을 확인하고 다시 시도해주세요.',
				autoExecute: false
			};
	}
}

/**
 * 에러 통계 수집 (개발 환경에서만)
 */
const errorStats = new Map<AuthErrorType, number>();

export function trackError(error: AuthError): void {
	if (!import.meta.env.DEV) return;

	const count = errorStats.get(error.type) || 0;
	errorStats.set(error.type, count + 1);
}

export function getErrorStats(): Record<string, number> {
	if (!import.meta.env.DEV) return {};

	return Object.fromEntries(errorStats.entries());
}

export function resetErrorStats(): void {
	if (!import.meta.env.DEV) return;

	errorStats.clear();
}
