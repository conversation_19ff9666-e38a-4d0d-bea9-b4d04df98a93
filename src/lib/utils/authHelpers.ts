/**
 * 인증 관련 헬퍼 함수들
 *
 * JWT 인증 시스템에서 자주 사용되는 유틸리티 함수들을 제공합니다.
 * 이 함수들은 컴포넌트와 서비스에서 공통으로 사용할 수 있습니다.
 */

import { goto } from '$app/navigation';
import { browser } from '$app/environment';
import type { AuthError, AuthErrorType, LoginCredentials } from '$lib/types/auth';

/**
 * 사용자 친화적 에러 메시지 매핑
 * 각 에러 타입에 대응하는 한국어 메시지를 정의합니다.
 */
const ERROR_MESSAGES: Record<AuthErrorType, string> = {
	INVALID_CREDENTIALS: '아이디 또는 비밀번호가 올바르지 않습니다.',
	TOKEN_EXPIRED: '로그인이 만료되었습니다. 다시 로그인해주세요.',
	REFRESH_FAILED: '인증 갱신에 실패했습니다. 다시 로그인해주세요.',
	NETWORK_ERROR: '네트워크 연결을 확인해주세요.',
	VALIDATION_ERROR: '입력 정보를 확인해주세요.',
	SERVER_ERROR: '서버 오류가 발생했습니다. 잠시 후 다시 시도해주세요.',
	UNKNOWN_ERROR: '알 수 없는 오류가 발생했습니다.'
};

/**
 * 인증 에러 객체를 생성합니다.
 *
 * @param type - 에러 타입
 * @param message - 커스텀 에러 메시지 (선택적)
 * @param statusCode - HTTP 상태 코드 (선택적)
 * @param details - 추가 에러 세부사항 (선택적)
 * @returns 인증 에러 객체
 *
 * @example
 * ```typescript
 * const error = createAuthError('INVALID_CREDENTIALS', undefined, 401);
 * console.log(error.message); // "아이디 또는 비밀번호가 올바르지 않습니다."
 * ```
 */
export function createAuthError(
	type: AuthErrorType,
	message?: string,
	statusCode?: number,
	details?: any
): AuthError {
	return {
		type,
		message: message || ERROR_MESSAGES[type],
		statusCode,
		details
	};
}

/**
 * 인증 에러에서 사용자 친화적 메시지를 추출합니다.
 *
 * @param error - 인증 에러 객체
 * @returns 사용자 친화적 에러 메시지
 *
 * @example
 * ```typescript
 * const error = createAuthError('NETWORK_ERROR');
 * const message = getErrorMessage(error);
 * console.log(message); // "네트워크 연결을 확인해주세요."
 * ```
 */
export function getErrorMessage(error: AuthError): string {
	return error.message || ERROR_MESSAGES[error.type] || ERROR_MESSAGES.UNKNOWN_ERROR;
}

/**
 * HTTP 상태 코드를 기반으로 인증 에러 타입을 결정합니다.
 *
 * @param statusCode - HTTP 상태 코드
 * @param defaultType - 기본 에러 타입 (선택적)
 * @returns 적절한 인증 에러 타입
 *
 * @example
 * ```typescript
 * const errorType = getErrorTypeFromStatus(401);
 * console.log(errorType); // "INVALID_CREDENTIALS"
 * ```
 */
export function getErrorTypeFromStatus(
	statusCode: number,
	defaultType: AuthErrorType = 'UNKNOWN_ERROR'
): AuthErrorType {
	switch (statusCode) {
		case 401:
			return 'INVALID_CREDENTIALS';
		case 422:
			return 'VALIDATION_ERROR';
		case 500:
		case 502:
		case 503:
		case 504:
			return 'SERVER_ERROR';
		default:
			return defaultType;
	}
}

/**
 * Axios 에러를 인증 에러로 변환합니다.
 *
 * @param error - Axios 에러 객체
 * @returns 인증 에러 객체
 *
 * @example
 * ```typescript
 * try {
 *   await axios.post('/api/auth/login', credentials);
 * } catch (axiosError) {
 *   const authError = convertAxiosError(axiosError);
 *   console.log(authError.type); // "NETWORK_ERROR" 또는 다른 타입
 * }
 * ```
 */
export function convertAxiosError(error: any): AuthError {
	if (error.response) {
		// 서버에서 응답을 받은 경우
		const statusCode = error.response.status;
		const errorType = getErrorTypeFromStatus(statusCode);

		return createAuthError(
			errorType,
			error.response.data?.message,
			statusCode,
			error.response.data
		);
	} else if (error.request) {
		// 요청은 보냈지만 응답을 받지 못한 경우 (네트워크 오류)
		return createAuthError('NETWORK_ERROR', '네트워크 연결을 확인해주세요.');
	} else {
		// 요청 설정 중 오류가 발생한 경우
		return createAuthError('UNKNOWN_ERROR', error.message);
	}
}

/**
 * 로그인 자격 증명의 유효성을 검사합니다.
 *
 * @param credentials - 로그인 자격 증명
 * @returns 유효성 검사 결과와 에러 메시지
 *
 * @example
 * ```typescript
 * const result = validateCredentials({ username: '', password: '123' });
 * if (!result.isValid) {
 *   console.log(result.errors); // ['사용자명을 입력해주세요.', '비밀번호는 최소 6자 이상이어야 합니다.']
 * }
 * ```
 */
export function validateCredentials(credentials: LoginCredentials): {
	isValid: boolean;
	errors: string[];
} {
	const errors: string[] = [];

	// 사용자명 검증
	if (!credentials.username || credentials.username.trim().length === 0) {
		errors.push('사용자명을 입력해주세요.');
	}

	// 비밀번호 검증
	if (!credentials.password || credentials.password.length === 0) {
		errors.push('비밀번호를 입력해주세요.');
	} else if (credentials.password.length < 6) {
		errors.push('비밀번호는 최소 6자 이상이어야 합니다.');
	}

	return {
		isValid: errors.length === 0,
		errors
	};
}

/**
 * 안전한 페이지 리다이렉트를 수행합니다.
 * 브라우저 환경에서만 실행되며, 에러 발생 시 기본 경로로 이동합니다.
 *
 * @param path - 이동할 경로
 * @param fallbackPath - 에러 시 대체 경로 (기본값: '/')
 *
 * @example
 * ```typescript
 * // 로그인 성공 후 대시보드로 이동
 * await safeRedirect('/dashboard', '/');
 *
 * // 로그아웃 후 로그인 페이지로 이동
 * await safeRedirect('/login');
 * ```
 */
export async function safeRedirect(path: string, fallbackPath: string = '/'): Promise<void> {
	if (!browser) {
		return;
	}

	try {
		await goto(path);
	} catch (error) {
		console.error(`페이지 이동 실패 (${path}):`, error);
		try {
			await goto(fallbackPath);
		} catch (fallbackError) {
			console.error(`대체 페이지 이동도 실패 (${fallbackPath}):`, fallbackError);
		}
	}
}

/**
 * 로그인 페이지로 안전하게 리다이렉트합니다.
 * 현재 페이지 정보를 쿼리 파라미터로 전달하여 로그인 후 원래 페이지로 돌아갈 수 있도록 합니다.
 *
 * @param currentPath - 현재 페이지 경로 (선택적)
 *
 * @example
 * ```typescript
 * // 인증이 필요한 페이지에서 로그인 페이지로 이동
 * await redirectToLogin('/dashboard');
 *
 * // 단순히 로그인 페이지로 이동
 * await redirectToLogin();
 * ```
 */
export async function redirectToLogin(currentPath?: string): Promise<void> {
	let loginPath = '/login';

	if (currentPath && currentPath !== '/login' && currentPath !== '/') {
		// 현재 페이지 정보를 쿼리 파라미터로 추가
		const encodedPath = encodeURIComponent(currentPath);
		loginPath = `/login?redirect=${encodedPath}`;
	}

	await safeRedirect(loginPath);
}

/**
 * 로그인 후 적절한 페이지로 리다이렉트합니다.
 * 쿼리 파라미터의 redirect 값을 확인하여 원래 페이지로 돌아가거나 기본 페이지로 이동합니다.
 *
 * @param searchParams - URL 검색 파라미터 (URLSearchParams 객체)
 * @param defaultPath - 기본 이동 경로 (기본값: '/')
 *
 * @example
 * ```typescript
 * // 로그인 페이지에서 로그인 성공 후
 * const searchParams = new URLSearchParams(window.location.search);
 * await redirectAfterLogin(searchParams, '/dashboard');
 * ```
 */
export async function redirectAfterLogin(
	searchParams: URLSearchParams,
	defaultPath: string = '/'
): Promise<void> {
	const redirectPath = searchParams.get('redirect');

	if (redirectPath) {
		try {
			const decodedPath = decodeURIComponent(redirectPath);
			// 보안을 위해 상대 경로만 허용
			if (decodedPath.startsWith('/') && !decodedPath.startsWith('//')) {
				await safeRedirect(decodedPath, defaultPath);
				return;
			}
		} catch (error) {
			console.error('리다이렉트 경로 디코딩 실패:', error);
		}
	}

	await safeRedirect(defaultPath);
}

/**
 * 현재 페이지가 인증이 필요한 페이지인지 확인합니다.
 *
 * @param pathname - 현재 페이지 경로
 * @returns 인증 필요 여부
 *
 * @example
 * ```typescript
 * const needsAuth = isAuthRequiredPage('/dashboard');
 * console.log(needsAuth); // true
 *
 * const publicPage = isAuthRequiredPage('/login');
 * console.log(publicPage); // false
 * ```
 */
export function isAuthRequiredPage(pathname: string): boolean {
	// 공개 페이지 목록
	const publicPaths = ['/login', '/register', '/forgot-password', '/reset-password'];

	// 정확히 일치하는 공개 페이지인지 확인
	if (publicPaths.includes(pathname)) {
		return false;
	}

	// 공개 페이지의 하위 경로인지 확인
	const isPublicSubPath = publicPaths.some((publicPath) => pathname.startsWith(publicPath + '/'));

	return !isPublicSubPath;
}

/**
 * 토큰 만료 시간을 사람이 읽기 쉬운 형태로 변환합니다.
 *
 * @param expiresAt - 만료 시간 (Date 객체)
 * @returns 사람이 읽기 쉬운 만료 시간 문자열
 *
 * @example
 * ```typescript
 * const expiresAt = new Date(Date.now() + 15 * 60 * 1000); // 15분 후
 * const readable = formatTokenExpiry(expiresAt);
 * console.log(readable); // "15분 후 만료"
 * ```
 */
export function formatTokenExpiry(expiresAt: Date): string {
	const now = new Date();
	const diffMs = expiresAt.getTime() - now.getTime();

	if (diffMs <= 0) {
		return '만료됨';
	}

	const diffMinutes = Math.floor(diffMs / (1000 * 60));
	const diffHours = Math.floor(diffMinutes / 60);
	const diffDays = Math.floor(diffHours / 24);

	if (diffDays > 0) {
		return `${diffDays}일 후 만료`;
	} else if (diffHours > 0) {
		return `${diffHours}시간 후 만료`;
	} else if (diffMinutes > 0) {
		return `${diffMinutes}분 후 만료`;
	} else {
		return '곧 만료';
	}
}

/**
 * 디버그 모드에서 인증 정보를 콘솔에 출력합니다.
 * 프로덕션 환경에서는 아무것도 출력하지 않습니다.
 *
 * @param label - 디버그 라벨
 * @param data - 출력할 데이터
 *
 * @example
 * ```typescript
 * debugAuth('Login Success', { userId: '123', tokenExpiry: new Date() });
 * // 개발 환경에서만 콘솔에 출력됨
 * ```
 */
export function debugAuth(label: string, data: any): void {
	if (import.meta.env.DEV) {
		console.group(`🔐 [Auth Debug] ${label}`);
		console.log(data);
		console.groupEnd();
	}
}

/**
 * 로컬 스토리지에서 안전하게 값을 가져옵니다.
 * 브라우저 환경이 아니거나 에러가 발생하면 null을 반환합니다.
 *
 * @param key - 스토리지 키
 * @returns 저장된 값 또는 null
 *
 * @example
 * ```typescript
 * const theme = safeGetLocalStorage('theme');
 * console.log(theme); // 'dark' 또는 null
 * ```
 */
export function safeGetLocalStorage(key: string): string | null {
	if (!browser) {
		return null;
	}

	try {
		return localStorage.getItem(key);
	} catch (error) {
		console.error(`로컬 스토리지 읽기 실패 (${key}):`, error);
		return null;
	}
}

/**
 * 로컬 스토리지에 안전하게 값을 저장합니다.
 * 브라우저 환경이 아니거나 에러가 발생하면 조용히 실패합니다.
 *
 * @param key - 스토리지 키
 * @param value - 저장할 값
 *
 * @example
 * ```typescript
 * safeSetLocalStorage('theme', 'dark');
 * ```
 */
export function safeSetLocalStorage(key: string, value: string): void {
	if (!browser) {
		return;
	}

	try {
		localStorage.setItem(key, value);
	} catch (error) {
		console.error(`로컬 스토리지 저장 실패 (${key}):`, error);
	}
}

/**
 * 로컬 스토리지에서 안전하게 값을 제거합니다.
 * 브라우저 환경이 아니거나 에러가 발생하면 조용히 실패합니다.
 *
 * @param key - 스토리지 키
 *
 * @example
 * ```typescript
 * safeRemoveLocalStorage('theme');
 * ```
 */
export function safeRemoveLocalStorage(key: string): void {
	if (!browser) {
		return;
	}

	try {
		localStorage.removeItem(key);
	} catch (error) {
		console.error(`로컬 스토리지 삭제 실패 (${key}):`, error);
	}
}
