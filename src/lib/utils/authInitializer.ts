/**
 * 인증 초기화 유틸리티
 *
 * 앱 시작 시 인증 상태를 초기화하고 관리하는 유틸리티 함수들을 제공합니다.
 */

import { browser } from '$app/environment';
import { goto } from '$app/navigation';
import { authActions } from '$lib/stores/authStore';
import { tokenService } from '$lib/services/tokenService';

/**
 * 인증 초기화 옵션
 */
export interface AuthInitializerOptions {
	// 자동 토큰 갱신 여부
	autoRefreshTokens?: boolean;

	// 토큰 갱신 체크 간격 (분 단위)
	refreshCheckInterval?: number;

	// 토큰 만료 임박 시간 (분 단위)
	tokenExpiryBuffer?: number;

	// 자동 로그아웃 여부 (토큰 갱신 실패 시)
	autoLogoutOnRefreshFail?: boolean;

	// 페이지 가시성 변경 시 토큰 확인 여부
	checkTokenOnVisibilityChange?: boolean;

	// 디버그 모드
	debug?: boolean;
}

/**
 * 기본 초기화 옵션
 */
const defaultOptions: Required<AuthInitializerOptions> = {
	autoRefreshTokens: true,
	refreshCheckInterval: 5, // 5분
	tokenExpiryBuffer: 5, // 5분
	autoLogoutOnRefreshFail: true,
	checkTokenOnVisibilityChange: true,
	debug: import.meta.env.DEV
};

/**
 * 인증 초기화 상태
 */
interface AuthInitializerState {
	isInitialized: boolean;
	refreshInterval: ReturnType<typeof setInterval> | null;
	visibilityHandler: (() => void) | null;
	options: Required<AuthInitializerOptions>;
}

let state: AuthInitializerState = {
	isInitialized: false,
	refreshInterval: null,
	visibilityHandler: null,
	options: defaultOptions
};

/**
 * 로그 출력 헬퍼
 */
function log(message: string, ...args: any[]) {
	if (state.options.debug) {
		console.log(`[AuthInitializer] ${message}`, ...args);
	}
}

/**
 * 에러 로그 출력 헬퍼
 */
function logError(message: string, error?: any) {
	console.error(`[AuthInitializer] ${message}`, error);
}

/**
 * 토큰 자동 갱신 설정
 */
function setupTokenAutoRefresh(): void {
	if (!browser || !state.options.autoRefreshTokens) return;

	// 기존 인터벌 정리
	if (state.refreshInterval) {
		clearInterval(state.refreshInterval);
	}

	const intervalMs = state.options.refreshCheckInterval * 60 * 1000;

	state.refreshInterval = setInterval(async () => {
		try {
			// 인증 상태 확인
			const isAuthenticated = await tokenService.isAuthenticated();
			if (!isAuthenticated) {
				log('인증되지 않은 상태, 토큰 갱신 중단');
				clearTokenAutoRefresh();
				return;
			}

			// 토큰 만료 임박 확인
			const isExpiringSoon = await tokenService.isAccessTokenExpiringSoon(
				state.options.tokenExpiryBuffer
			);

			if (isExpiringSoon) {
				log('토큰 만료 임박, 자동 갱신 시도');

				try {
					const refreshed = await authActions.refreshAuth();

					if (!refreshed) {
						log('토큰 갱신 실패');

						// 리프레시 토큰도 확인해서 정말 만료된 경우에만 로그아웃
						const hasValidRefreshToken = await tokenService.isRefreshTokenValid();

						if (!hasValidRefreshToken && state.options.autoLogoutOnRefreshFail) {
							log('리프레시 토큰도 만료됨, 자동 로그아웃 처리');
							await authActions.logout();
						} else {
							log('리프레시 토큰은 유효함, 로그아웃하지 않음');
						}
					} else {
						log('토큰 갱신 성공');
					}
				} catch (error) {
					logError('토큰 갱신 중 예외 발생', error);
				}
			}
		} catch (error) {
			logError('토큰 자동 갱신 중 오류 발생', error);
		}
	}, intervalMs);

	log(`토큰 자동 갱신 설정 완료 (${state.options.refreshCheckInterval}분 간격)`);
}

/**
 * 토큰 자동 갱신 정리
 */
function clearTokenAutoRefresh(): void {
	if (state.refreshInterval) {
		clearInterval(state.refreshInterval);
		state.refreshInterval = null;
		log('토큰 자동 갱신 정리 완료');
	}
}

/**
 * 페이지 가시성 변경 핸들러 설정
 */
function setupVisibilityChangeHandler(): void {
	if (!browser || !state.options.checkTokenOnVisibilityChange) return;

	const handleVisibilityChange = async () => {
		if (document.visibilityState === 'visible') {
			try {
				log('페이지 가시성 복원, 토큰 상태 확인');

				const tokenStatus = await tokenService.getTokenStatus();

				if (tokenStatus.hasRefreshToken && !tokenStatus.isAccessTokenValid) {
					log('페이지 복귀 시 액세스 토큰 만료 확인, 갱신 시도');

					try {
						const refreshed = await authActions.refreshAuth();

						if (!refreshed) {
							log('페이지 복귀 시 토큰 갱신 실패');

							// 리프레시 토큰 유효성 재확인
							const hasValidRefreshToken = await tokenService.isRefreshTokenValid();

							if (!hasValidRefreshToken && state.options.autoLogoutOnRefreshFail) {
								log('리프레시 토큰도 만료됨, 로그아웃 처리');
								await authActions.logout();
							} else {
								log('리프레시 토큰은 유효함, 다음 기회에 재시도');
							}
						} else {
							log('페이지 복귀 시 토큰 갱신 성공');
						}
					} catch (error) {
						logError('페이지 복귀 시 토큰 갱신 중 예외 발생', error);
					}
				} else if (!tokenStatus.hasRefreshToken && !tokenStatus.isAccessTokenValid) {
					log('페이지 복귀 시 모든 토큰 만료, 로그아웃 처리');
					await authActions.logout();
				}
			} catch (error) {
				logError('페이지 가시성 변경 처리 중 오류 발생', error);
			}
		}
	};

	document.addEventListener('visibilitychange', handleVisibilityChange);
	state.visibilityHandler = () => {
		document.removeEventListener('visibilitychange', handleVisibilityChange);
	};

	log('페이지 가시성 변경 핸들러 설정 완료');
}

/**
 * 페이지 가시성 변경 핸들러 정리
 */
function clearVisibilityChangeHandler(): void {
	if (state.visibilityHandler) {
		state.visibilityHandler();
		state.visibilityHandler = null;
		log('페이지 가시성 변경 핸들러 정리 완료');
	}
}

/**
 * 인증 시스템을 초기화합니다.
 *
 * @param options 초기화 옵션
 * @returns 초기화 성공 여부
 */
export async function initializeAuth(options: AuthInitializerOptions = {}): Promise<boolean> {
	if (!browser) {
		log('브라우저 환경이 아님, 초기화 건너뜀');
		return false;
	}

	if (state.isInitialized) {
		log('이미 초기화됨, 건너뜀');
		return true;
	}

	try {
		// 옵션 병합
		state.options = { ...defaultOptions, ...options };

		log('인증 시스템 초기화 시작', state.options);

		// 인증 액션 초기화
		await authActions.initialize();

		// 토큰 상태 확인 및 갱신
		const tokenStatus = await tokenService.getTokenStatus();
		log('초기 토큰 상태', tokenStatus);

		// 리프레시 토큰이 있지만 액세스 토큰이 유효하지 않은 경우 갱신 시도
		if (tokenStatus.hasRefreshToken && !tokenStatus.isAccessTokenValid) {
			log('액세스 토큰 만료, 초기 갱신 시도');

			try {
				const refreshed = await authActions.refreshAuth();

				if (!refreshed) {
					log('초기 토큰 갱신 실패');

					// 리프레시 토큰 유효성 재확인
					const hasValidRefreshToken = await tokenService.isRefreshTokenValid();

					if (!hasValidRefreshToken && state.options.autoLogoutOnRefreshFail) {
						log('리프레시 토큰도 만료됨, 로그아웃 처리');
						await authActions.logout();
					} else {
						log('리프레시 토큰은 유효함, 나중에 재시도');
					}
				} else {
					log('초기 토큰 갱신 성공');
				}
			} catch (error) {
				logError('초기 토큰 갱신 중 예외 발생', error);
			}
		}

		// 자동 갱신 설정
		setupTokenAutoRefresh();

		// 페이지 가시성 변경 핸들러 설정
		setupVisibilityChangeHandler();

		state.isInitialized = true;
		log('인증 시스템 초기화 완료');

		return true;
	} catch (error) {
		logError('인증 시스템 초기화 실패', error);
		return false;
	}
}

/**
 * 인증 시스템을 정리합니다.
 */
export function cleanupAuth(): void {
	if (!state.isInitialized) return;

	log('인증 시스템 정리 시작');

	// 자동 갱신 정리
	clearTokenAutoRefresh();

	// 가시성 변경 핸들러 정리
	clearVisibilityChangeHandler();

	state.isInitialized = false;
	log('인증 시스템 정리 완료');
}

/**
 * 현재 초기화 상태를 반환합니다.
 */
export function getInitializationState(): Readonly<AuthInitializerState> {
	return { ...state };
}

/**
 * 인증이 필요한 페이지에서 사용할 가드 함수
 *
 * @param redirectTo 인증 실패 시 리다이렉트할 경로
 * @returns 인증 여부
 */
export async function requireAuth(redirectTo: string = '/login'): Promise<boolean> {
	if (!browser) return false;

	try {
		const isAuthenticated = await tokenService.isAuthenticated();

		if (!isAuthenticated) {
			log(`인증 필요, ${redirectTo}로 리다이렉트`);
			const currentPath = window.location.pathname + window.location.search;
			const loginUrl = `${redirectTo}?redirect=${encodeURIComponent(currentPath)}`;
			await goto(loginUrl);
			return false;
		}

		return true;
	} catch (error) {
		logError('인증 확인 중 오류 발생', error);
		return false;
	}
}

/**
 * 로그인 페이지에서 이미 인증된 사용자를 리다이렉트합니다.
 *
 * @param defaultRedirect 기본 리다이렉트 경로
 * @returns 리다이렉트 여부
 */
export async function redirectIfAuthenticated(defaultRedirect: string = '/'): Promise<boolean> {
	if (!browser) return false;

	try {
		const isAuthenticated = await tokenService.isAuthenticated();

		if (isAuthenticated) {
			const urlParams = new URLSearchParams(window.location.search);
			const redirectTo = urlParams.get('redirect') || defaultRedirect;

			log(`이미 인증됨, ${redirectTo}로 리다이렉트`);
			await goto(redirectTo);
			return true;
		}

		return false;
	} catch (error) {
		logError('인증 상태 확인 중 오류 발생', error);
		return false;
	}
}

/**
 * 토큰 상태를 수동으로 확인하고 필요시 갱신합니다.
 *
 * @returns 갱신 성공 여부
 */
export async function checkAndRefreshToken(): Promise<boolean> {
	if (!browser) return false;

	try {
		const isExpiringSoon = await tokenService.isAccessTokenExpiringSoon(
			state.options.tokenExpiryBuffer
		);

		if (isExpiringSoon) {
			log('수동 토큰 갱신 시도');
			return await authActions.refreshAuth();
		}

		return true;
	} catch (error) {
		logError('토큰 확인 및 갱신 중 오류 발생', error);
		return false;
	}
}

/**
 * 디버그 정보를 출력합니다.
 */
export async function debugAuthInitializer(): Promise<void> {
	if (!import.meta.env.DEV) return;

	const tokenStatus = await tokenService.getTokenStatus();

	console.log('[AuthInitializer Debug]', {
		initializerState: state,
		tokenStatus,
		currentUrl: browser ? window.location.href : 'N/A'
	});

	await authActions.debugAuth();
}
