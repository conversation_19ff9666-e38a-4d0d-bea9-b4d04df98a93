/**
 * 배치 팔레트 적재 기능 로컬스토리지 유틸리티
 *
 * 이 파일은 로컬스토리지를 사용하여 배치 상품 데이터를 관리하는 유틸리티 함수들을 제공합니다.
 * 데이터 초기화, 저장, 조회, 검증 및 복구 기능을 포함합니다.
 */

import type { BatchStorageState, StorageCapacityInfo } from '../types/batchTypes';
import { BATCH_STORAGE_KEYS, CURRENT_STORAGE_VERSION } from '../types/batchTypes';

/**
 * 기본 배치 스토리지 상태
 * 초기화 시 사용되는 기본 상태 값입니다.
 */
const DEFAULT_BATCH_STATE: BatchStorageState = {
	pallets: {},
	totalCount: 0,
	pendingCount: 0,
	successCount: 0,
	failedCount: 0,
	lastUpdated: Date.now(),
	currentPalletId: ''
};

/**
 * 스토리지 관련 상수 값
 */
const STORAGE_CONSTANTS = {
	/** 로컬스토리지 용량 경고 임계값 (%) */
	WARNING_THRESHOLD: 80,

	/** 최대 저장 상품 수 경고 임계값 */
	MAX_PRODUCTS_WARNING: 100,

	/** 자동 정리 임계값 (%) */
	AUTO_CLEANUP_THRESHOLD: 70,

	/** 보관할 최대 백업 수 */
	MAX_BACKUPS: 5,

	/** 기본 스토리지 용량 (KB) */
	DEFAULT_STORAGE_SIZE: 5 * 1024
};

/**
 * 배치 스토리지 초기화
 * 로컬스토리지에 기본 상태를 초기화합니다.
 * @param forceInit 강제 초기화 여부 (기본값: false)
 * @returns 초기화 성공 여부 및 상태 정보
 */
export function initBatchStorage(forceInit = false): {
	success: boolean;
	isNew: boolean;
	message: string;
} {
	try {
		// 이미 저장된 데이터가 있는지 확인
		const existingData = localStorage.getItem(BATCH_STORAGE_KEYS.STATE);

		// 데이터가 없거나 강제 초기화 요청 시 기본 상태 저장
		if (!existingData || forceInit) {
			// 기존 데이터가 있고 강제 초기화 요청인 경우 백업 생성
			if (existingData && forceInit) {
				createBackup();
			}

			localStorage.setItem(BATCH_STORAGE_KEYS.STATE, JSON.stringify(DEFAULT_BATCH_STATE));
			localStorage.setItem(BATCH_STORAGE_KEYS.VERSION, CURRENT_STORAGE_VERSION);
			localStorage.setItem(BATCH_STORAGE_KEYS.LAST_CHECK, Date.now().toString());

			return {
				success: true,
				isNew: true,
				message: forceInit
					? '배치 스토리지가 강제 초기화되었습니다.'
					: '배치 스토리지가 새로 초기화되었습니다.'
			};
		} else {
			// 기존 데이터 유효성 검증
			try {
				JSON.parse(existingData);
			} catch (parseError) {
				console.warn('손상된 스토리지 데이터 감지, 초기화 진행:', parseError);
				// 손상된 데이터 백업 후 초기화
				createBackup();
				localStorage.setItem(BATCH_STORAGE_KEYS.STATE, JSON.stringify(DEFAULT_BATCH_STATE));
				localStorage.setItem(BATCH_STORAGE_KEYS.VERSION, CURRENT_STORAGE_VERSION);
				localStorage.setItem(BATCH_STORAGE_KEYS.LAST_CHECK, Date.now().toString());

				return {
					success: true,
					isNew: true,
					message: '손상된 데이터로 인해 배치 스토리지가 초기화되었습니다.'
				};
			}

			// 버전 확인 및 마이그레이션 필요 시 처리
			const version = localStorage.getItem(BATCH_STORAGE_KEYS.VERSION) || '1.0';
			if (version !== CURRENT_STORAGE_VERSION) {
				// 마이그레이션 전 백업 생성
				createBackup();
				migrateStorageData(version);
				return {
					success: true,
					isNew: false,
					message: `배치 스토리지가 버전 ${version}에서 ${CURRENT_STORAGE_VERSION}으로 마이그레이션되었습니다.`
				};
			}

			// 마지막 확인 시간 업데이트
			localStorage.setItem(BATCH_STORAGE_KEYS.LAST_CHECK, Date.now().toString());

			return {
				success: true,
				isNew: false,
				message: '기존 배치 스토리지를 사용합니다.'
			};
		}
	} catch (error) {
		console.error('배치 스토리지 초기화 중 오류 발생:', error);
		// 오류 발생 시 안전하게 초기화
		resetBatchStorage();
		return {
			success: true,
			isNew: true,
			message: '오류로 인해 배치 스토리지가 초기화되었습니다.'
		};
	}
}

/**
 * 배치 스토리지 상태 가져오기
 * 로컬스토리지에서 현재 상태를 조회합니다.
 * @param skipRecovery 복구 시도를 건너뛸지 여부 (기본값: false)
 * @returns 배치 스토리지 상태 객체
 */
export function getBatchState(skipRecovery = false): BatchStorageState {
	try {
		const stateJson = localStorage.getItem(BATCH_STORAGE_KEYS.STATE);
		if (!stateJson) {
			// 데이터가 없으면 초기화 후 기본 상태 반환
			initBatchStorage();
			return { ...DEFAULT_BATCH_STATE };
		}

		let state: BatchStorageState;
		try {
			state = JSON.parse(stateJson) as BatchStorageState;
		} catch (e) {
			console.error('배치 스토리지 데이터 파싱 오류:', e);

			// 복구 시도
			if (!skipRecovery && attemptDataRecovery()) {
				// 복구 성공 시 재귀적으로 다시 시도 (복구 무한 루프 방지를 위해 skipRecovery=true)
				return getBatchState(true);
			}

			// 복구 실패 또는 건너뛰기 시 초기화
			resetBatchStorage();
			return { ...DEFAULT_BATCH_STATE };
		}

		// 데이터 유효성 검증
		if (!isValidBatchState(state)) {
			console.warn('배치 스토리지 데이터가 유효하지 않습니다.');

			// 복구 시도
			if (!skipRecovery && attemptDataRecovery()) {
				// 복구 성공 시 재귀적으로 다시 시도 (복구 무한 루프 방지를 위해 skipRecovery=true)
				return getBatchState(true);
			}

			// 복구 실패 또는 건너뛰기 시 초기화
			console.warn('데이터 복구 실패, 초기화합니다.');
			resetBatchStorage();
			return { ...DEFAULT_BATCH_STATE };
		}

		return state;
	} catch (error) {
		console.error('배치 스토리지 상태 조회 중 오류 발생:', error);

		// 복구 시도
		if (!skipRecovery && attemptDataRecovery()) {
			// 복구 성공 시 재귀적으로 다시 시도 (복구 무한 루프 방지를 위해 skipRecovery=true)
			return getBatchState(true);
		}

		// 오류 발생 시 안전하게 초기화 후 기본 상태 반환
		resetBatchStorage();
		return { ...DEFAULT_BATCH_STATE };
	}
}

/**
 * 배치 스토리지 상태 저장하기
 * 현재 상태를 로컬스토리지에 저장합니다.
 * @param state 저장할 배치 스토리지 상태
 */
export function saveBatchState(state: BatchStorageState): void {
	try {
		// 저장 전 카운트 값 재계산하여 정확성 보장
		const updatedState = recalculateCounts(state);

		// 마지막 업데이트 시간 갱신
		updatedState.lastUpdated = Date.now();

		// 로컬스토리지에 저장
		localStorage.setItem(BATCH_STORAGE_KEYS.STATE, JSON.stringify(updatedState));
		localStorage.setItem(BATCH_STORAGE_KEYS.LAST_CHECK, Date.now().toString());
	} catch (error) {
		console.error('배치 스토리지 상태 저장 중 오류 발생:', error);
		throw new Error('배치 데이터 저장에 실패했습니다.');
	}
}

/**
 * 배치 스토리지 초기화
 * 로컬스토리지의 배치 관련 데이터를 모두 초기화합니다.
 */
export function resetBatchStorage(): void {
	try {
		localStorage.setItem(BATCH_STORAGE_KEYS.STATE, JSON.stringify(DEFAULT_BATCH_STATE));
		localStorage.setItem(BATCH_STORAGE_KEYS.VERSION, CURRENT_STORAGE_VERSION);
		localStorage.setItem(BATCH_STORAGE_KEYS.LAST_CHECK, Date.now().toString());
	} catch (error) {
		console.error('배치 스토리지 초기화 중 오류 발생:', error);
	}
}

/**
 * 스토리지 용량 확인
 * 현재 로컬스토리지 사용량과 용량 정보를 반환합니다.
 * @param testQuota 테스트용 쿼터 크기 (KB, 기본값: 0 - 자동 감지)
 * @returns 스토리지 용량 정보
 */
export function checkStorageCapacity(testQuota = 0): StorageCapacityInfo {
	try {
		// 브라우저별 로컬스토리지 용량 제한 감지 시도
		let TOTAL_STORAGE = testQuota > 0 ? testQuota : STORAGE_CONSTANTS.DEFAULT_STORAGE_SIZE; // 기본값 5MB (KB 단위)

		// 브라우저별 스토리지 용량 감지 시도
		try {
			// Chrome, Firefox 등에서 지원하는 quota 관리 API 사용
			if (navigator.storage && navigator.storage.estimate) {
				const quota = localStorage.getItem('_storage_test_quota');
				if (quota) {
					TOTAL_STORAGE = parseInt(quota, 10);
				} else {
					// 스토리지 견적 비동기 요청 (다음 호출 시 사용)
					navigator.storage
						.estimate()
						.then((estimate) => {
							if (estimate.quota) {
								const quotaKB = Math.floor(estimate.quota / 1024);
								localStorage.setItem('_storage_test_quota', quotaKB.toString());
							}
						})
						.catch(() => {
							// 오류 무시
						});
				}
			}
		} catch (e) {
			// 용량 감지 실패 시 기본값 사용
			console.warn('스토리지 용량 감지 실패, 기본값 사용:', e);
		}

		// 현재 사용량 계산 (정확한 계산)
		let usedStorage = 0;
		let batchStorageSize = 0;

		for (let i = 0; i < localStorage.length; i++) {
			const key = localStorage.key(i);
			if (key) {
				const value = localStorage.getItem(key) || '';
				// UTF-16 문자열 길이 * 2 바이트로 대략적인 크기 계산
				const itemSize = (key.length + value.length) * 2;
				usedStorage += itemSize;

				// 배치 관련 스토리지 크기 별도 계산
				if (key.startsWith('batch_pallet_')) {
					batchStorageSize += itemSize;
				}
			}
		}

		// KB 단위로 변환
		const usedKB = Math.round(usedStorage / 1024);
		const batchUsedKB = Math.round(batchStorageSize / 1024);
		const availableKB = TOTAL_STORAGE - usedKB;
		const percentUsed = Math.round((usedKB / TOTAL_STORAGE) * 100);
		const batchPercentUsed = Math.round((batchUsedKB / TOTAL_STORAGE) * 100);

		return {
			used: usedKB,
			available: availableKB,
			total: TOTAL_STORAGE,
			percentUsed: percentUsed,
			isNearLimit: percentUsed >= STORAGE_CONSTANTS.WARNING_THRESHOLD,
			batchUsed: batchUsedKB,
			batchPercentUsed: batchPercentUsed
		};
	} catch (error) {
		console.error('스토리지 용량 확인 중 오류 발생:', error);
		// 오류 발생 시 기본값 반환
		return {
			used: 0,
			available: STORAGE_CONSTANTS.DEFAULT_STORAGE_SIZE,
			total: STORAGE_CONSTANTS.DEFAULT_STORAGE_SIZE,
			percentUsed: 0,
			isNearLimit: false,
			batchUsed: 0,
			batchPercentUsed: 0
		};
	}
}

/**
 * 스토리지 정리
 * 불필요한 데이터를 정리하여 스토리지 공간을 확보합니다.
 * @param options 정리 옵션
 * @returns 정리 결과 정보
 */
export function cleanupStorage(
	options: {
		removeSuccess?: boolean;
		removeOldBackups?: boolean;
		compressData?: boolean;
		maxBackupsToKeep?: number;
	} = {}
): { success: boolean; freedSpace: number; message: string } {
	try {
		// 기본 옵션 설정
		const opts = {
			removeSuccess: true,
			removeOldBackups: true,
			compressData: true,
			maxBackupsToKeep: STORAGE_CONSTANTS.MAX_BACKUPS,
			...options
		};

		// 정리 전 용량 확인
		const beforeCapacity = checkStorageCapacity();
		let cleanupActions: string[] = [];

		// 1. 성공한 상품 데이터 정리
		if (opts.removeSuccess) {
			const state = getBatchState();
			let successItemsRemoved = 0;

			Object.keys(state.pallets).forEach((palletId) => {
				const originalLength = state.pallets[palletId].products.length;
				state.pallets[palletId].products = state.pallets[palletId].products.filter(
					(product) => product.status !== 'success'
				);
				successItemsRemoved += originalLength - state.pallets[palletId].products.length;
			});

			// 빈 팔레트 제거
			let emptyPalletsRemoved = 0;
			Object.keys(state.pallets).forEach((palletId) => {
				if (state.pallets[palletId].products.length === 0) {
					delete state.pallets[palletId];
					emptyPalletsRemoved++;
				}
			});

			// 상태 저장
			saveBatchState(state);

			if (successItemsRemoved > 0) {
				cleanupActions.push(`성공한 상품 ${successItemsRemoved}개 제거`);
			}
			if (emptyPalletsRemoved > 0) {
				cleanupActions.push(`빈 팔레트 ${emptyPalletsRemoved}개 제거`);
			}
		}

		// 2. 오래된 백업 정리
		if (opts.removeOldBackups) {
			const backupKeys = getBackupList();
			if (backupKeys.length > opts.maxBackupsToKeep) {
				// 최신 백업 유지하고 나머지 삭제
				const keysToRemove = backupKeys.slice(opts.maxBackupsToKeep);
				keysToRemove.forEach((key) => {
					localStorage.removeItem(key);
				});

				cleanupActions.push(`오래된 백업 ${keysToRemove.length}개 제거`);
			}
		}

		// 3. 데이터 압축 (실제 압축은 아니지만 불필요한 정보 제거)
		if (opts.compressData) {
			const state = getBatchState();
			let optimizedCount = 0;

			// 불필요한 속성 제거 및 데이터 최적화
			Object.keys(state.pallets).forEach((palletId) => {
				state.pallets[palletId].products.forEach((product) => {
					// 성공 항목의 상세 정보 제거 (필수 정보만 유지)
					if (product.status === 'success' && product.productInfo) {
						product.productInfo = {
							qaid: product.qaid,
							timestamp: product.timestamp
						};
						optimizedCount++;
					}

					// 실패 항목은 오류 메시지만 유지
					if (product.status === 'failed' && product.productInfo) {
						const errorMsg = product.errorMessage;
						product.productInfo = {
							qaid: product.qaid,
							timestamp: product.timestamp
						};
						product.errorMessage = errorMsg;
						optimizedCount++;
					}
				});
			});

			if (optimizedCount > 0) {
				saveBatchState(state);
				cleanupActions.push(`${optimizedCount}개 항목 데이터 최적화`);
			}
		}

		// 정리 후 용량 확인
		const afterCapacity = checkStorageCapacity();
		const freedSpace = beforeCapacity.used - afterCapacity.used;

		// 결과 메시지 생성
		let resultMessage =
			cleanupActions.length > 0
				? `스토리지 정리 완료: ${cleanupActions.join(', ')}`
				: '정리할 항목이 없습니다.';

		if (freedSpace > 0) {
			resultMessage += `, ${freedSpace}KB 공간 확보`;
		}

		return {
			success: true,
			freedSpace: freedSpace,
			message: resultMessage
		};
	} catch (error) {
		console.error('스토리지 정리 중 오류 발생:', error);
		return {
			success: false,
			freedSpace: 0,
			message: `스토리지 정리 중 오류 발생: ${error}`
		};
	}
}

/**
 * 배치 상태 유효성 검증
 * 로컬스토리지에서 로드한 데이터의 유효성을 검증합니다.
 * @param state 검증할 배치 스토리지 상태
 * @returns 유효성 여부
 */
function isValidBatchState(state: any): state is BatchStorageState {
	// 필수 속성 존재 여부 확인
	if (
		!state ||
		typeof state !== 'object' ||
		!('pallets' in state) ||
		!('totalCount' in state) ||
		!('pendingCount' in state) ||
		!('successCount' in state) ||
		!('failedCount' in state) ||
		!('lastUpdated' in state) ||
		!('currentPalletId' in state)
	) {
		console.warn('배치 상태 유효성 검증 실패: 필수 속성 누락');
		return false;
	}

	// 타입 검증
	if (
		typeof state.totalCount !== 'number' ||
		typeof state.pendingCount !== 'number' ||
		typeof state.successCount !== 'number' ||
		typeof state.failedCount !== 'number' ||
		typeof state.lastUpdated !== 'number' ||
		typeof state.currentPalletId !== 'string'
	) {
		console.warn('배치 상태 유효성 검증 실패: 속성 타입 불일치');
		return false;
	}

	// pallets 객체 구조 확인
	if (typeof state.pallets !== 'object' || state.pallets === null) {
		console.warn('배치 상태 유효성 검증 실패: pallets 객체 구조 오류');
		return false;
	}

	// 각 팔레트의 products 배열 확인
	for (const palletId in state.pallets) {
		const pallet = state.pallets[palletId];
		if (!pallet || !Array.isArray(pallet.products)) {
			console.warn(`배치 상태 유효성 검증 실패: 팔레트 ${palletId}의 products 배열 오류`);
			return false;
		}

		// 각 상품의 필수 속성 확인
		for (const product of pallet.products) {
			if (
				!product.id ||
				!product.qaid ||
				!product.status ||
				!product.timestamp ||
				!product.palletId ||
				typeof product.id !== 'string' ||
				typeof product.qaid !== 'string' ||
				!['pending', 'submitting', 'success', 'failed'].includes(product.status) ||
				typeof product.timestamp !== 'number' ||
				typeof product.palletId !== 'string'
			) {
				console.warn(`배치 상태 유효성 검증 실패: 상품 ${product.id || '알 수 없음'}의 속성 오류`);
				return false;
			}
		}
	}

	return true;
}

/**
 * 카운트 재계산
 * 상품 목록을 기반으로 각 상태별 카운트를 재계산합니다.
 * @param state 배치 스토리지 상태
 * @returns 카운트가 업데이트된 상태
 */
function recalculateCounts(state: BatchStorageState): BatchStorageState {
	let totalCount = 0;
	let pendingCount = 0;
	let successCount = 0;
	let failedCount = 0;

	// 모든 팔레트의 상품을 순회하며 카운트 계산
	Object.values(state.pallets).forEach((pallet) => {
		pallet.products.forEach((product) => {
			totalCount++;

			switch (product.status) {
				case 'pending':
					pendingCount++;
					break;
				case 'success':
					successCount++;
					break;
				case 'failed':
					failedCount++;
					break;
			}
		});
	});

	return {
		...state,
		totalCount,
		pendingCount,
		successCount,
		failedCount
	};
}

/**
 * 스토리지 데이터 마이그레이션
 * 버전 변경 시 데이터 구조를 마이그레이션합니다.
 * @param oldVersion 이전 버전
 */
function migrateStorageData(oldVersion: string): void {
	try {
		console.log(`스토리지 데이터 마이그레이션: ${oldVersion} -> ${CURRENT_STORAGE_VERSION}`);

		// 현재 저장된 데이터 가져오기
		const stateJson = localStorage.getItem(BATCH_STORAGE_KEYS.STATE);
		if (!stateJson) {
			// 데이터가 없으면 초기화
			resetBatchStorage();
			return;
		}

		let state: any;
		try {
			state = JSON.parse(stateJson);
		} catch (e) {
			// JSON 파싱 오류 시 초기화
			resetBatchStorage();
			return;
		}

		// 버전별 마이그레이션 로직
		// 향후 버전 업그레이드 시 여기에 마이그레이션 로직 추가
		switch (oldVersion) {
			case '1.0':
				// 현재 버전과 같으므로 마이그레이션 필요 없음
				break;
			default:
				// 알 수 없는 버전은 안전하게 초기화
				console.warn(`알 수 없는 스토리지 버전: ${oldVersion}, 데이터를 초기화합니다.`);
				resetBatchStorage();
				return;
		}

		// 버전 업데이트
		localStorage.setItem(BATCH_STORAGE_KEYS.VERSION, CURRENT_STORAGE_VERSION);
	} catch (error) {
		console.error('스토리지 데이터 마이그레이션 중 오류 발생:', error);
		// 마이그레이션 실패 시 안전하게 초기화
		resetBatchStorage();
	}
}

/**
 * 자동 저장 필요 여부 확인
 * 상품 수가 많거나 스토리지 용량이 부족할 때 자동 저장이 필요한지 확인합니다.
 * @returns 자동 저장 필요 여부 및 이유
 */
export function checkAutoSaveNeeded(): { needed: boolean; reason: string } {
	const state = getBatchState();
	const capacity = checkStorageCapacity();

	if (capacity.isNearLimit) {
		return {
			needed: true,
			reason: `스토리지 용량이 ${capacity.percentUsed}% 사용되었습니다. 데이터 손실을 방지하기 위해 저장해주세요.`
		};
	}

	if (state.pendingCount > STORAGE_CONSTANTS.MAX_PRODUCTS_WARNING) {
		return {
			needed: true,
			reason: `${state.pendingCount}개의 상품이 임시 저장되어 있습니다. 데이터 안전을 위해 저장해주세요.`
		};
	}

	return { needed: false, reason: '' };
}

/**
 * 데이터 복구 시도
 * 손상된 데이터를 복구하려고 시도합니다.
 * @returns 복구 성공 여부
 */
export function attemptDataRecovery(): boolean {
	try {
		console.log('데이터 복구 시도 중...');

		// 로컬스토리지에서 원시 데이터 가져오기
		const stateJson = localStorage.getItem(BATCH_STORAGE_KEYS.STATE);
		if (!stateJson) {
			console.warn('복구할 데이터가 없습니다.');
			resetBatchStorage();
			return false;
		}

		let state: any;
		try {
			state = JSON.parse(stateJson);
		} catch (e) {
			console.error('JSON 파싱 오류로 복구 실패:', e);
			resetBatchStorage();
			return false;
		}

		// 기본 구조 확인 및 복구
		if (!state || typeof state !== 'object') {
			console.error('데이터 구조가 유효하지 않아 복구 실패');
			resetBatchStorage();
			return false;
		}

		// 필수 속성 복구
		const recoveredState: BatchStorageState = {
			pallets: {},
			totalCount: 0,
			pendingCount: 0,
			successCount: 0,
			failedCount: 0,
			lastUpdated: Date.now(),
			currentPalletId: ''
		};

		// pallets 복구 시도
		if (state.pallets && typeof state.pallets === 'object') {
			// 각 팔레트 복구
			for (const palletId in state.pallets) {
				const pallet = state.pallets[palletId];
				if (pallet && Array.isArray(pallet.products)) {
					// 유효한 상품만 필터링
					const validProducts = pallet.products.filter((product: any) => {
						return (
							product &&
							typeof product === 'object' &&
							product.id &&
							product.qaid &&
							product.status &&
							product.timestamp &&
							product.palletId
						);
					});

					if (validProducts.length > 0) {
						recoveredState.pallets[palletId] = {
							products: validProducts,
							palletInfo: pallet.palletInfo || {}
						};
					}
				}
			}
		}

		// 현재 팔레트 ID 복구
		if (typeof state.currentPalletId === 'string') {
			recoveredState.currentPalletId = state.currentPalletId;
		} else if (Object.keys(recoveredState.pallets).length > 0) {
			// 첫 번째 팔레트를 현재 팔레트로 설정
			recoveredState.currentPalletId = Object.keys(recoveredState.pallets)[0];
		}

		// 카운트 재계산
		const updatedState = recalculateCounts(recoveredState);

		// 복구된 데이터 저장
		localStorage.setItem(BATCH_STORAGE_KEYS.STATE, JSON.stringify(updatedState));
		localStorage.setItem(BATCH_STORAGE_KEYS.VERSION, CURRENT_STORAGE_VERSION);
		localStorage.setItem(BATCH_STORAGE_KEYS.LAST_CHECK, Date.now().toString());

		console.log('데이터 복구 완료:', updatedState);
		return true;
	} catch (error) {
		console.error('데이터 복구 중 오류 발생:', error);
		resetBatchStorage();
		return false;
	}
}

/**
 * 데이터 백업 생성
 * 현재 상태의 백업을 생성합니다.
 */
export function createBackup(): void {
	try {
		const state = getBatchState();
		const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
		localStorage.setItem(`${BATCH_STORAGE_KEYS.STATE}_backup_${timestamp}`, JSON.stringify(state));
		console.log('데이터 백업 생성 완료:', timestamp);
	} catch (error) {
		console.error('데이터 백업 생성 중 오류 발생:', error);
	}
}

/**
 * 백업 목록 가져오기
 * 저장된 백업 목록을 가져옵니다.
 * @returns 백업 키 목록
 */
export function getBackupList(): string[] {
	try {
		const backupKeys: string[] = [];
		for (let i = 0; i < localStorage.length; i++) {
			const key = localStorage.key(i);
			if (key && key.startsWith(`${BATCH_STORAGE_KEYS.STATE}_backup_`)) {
				backupKeys.push(key);
			}
		}
		return backupKeys.sort().reverse(); // 최신 백업이 먼저 오도록 정렬
	} catch (error) {
		console.error('백업 목록 조회 중 오류 발생:', error);
		return [];
	}
}

/**
 * 백업에서 복원
 * 지정된 백업에서 데이터를 복원합니다.
 * @param backupKey 백업 키
 * @returns 복원 성공 여부
 */
export function restoreFromBackup(backupKey: string): boolean {
	try {
		const backupData = localStorage.getItem(backupKey);
		if (!backupData) {
			console.error('지정된 백업을 찾을 수 없습니다:', backupKey);
			return false;
		}

		const state = JSON.parse(backupData);
		if (!isValidBatchState(state)) {
			console.error('백업 데이터가 유효하지 않습니다:', backupKey);
			return false;
		}

		// 현재 상태를 백업으로 대체
		localStorage.setItem(BATCH_STORAGE_KEYS.STATE, backupData);
		localStorage.setItem(BATCH_STORAGE_KEYS.LAST_CHECK, Date.now().toString());

		console.log('백업에서 복원 완료:', backupKey);
		return true;
	} catch (error) {
		console.error('백업에서 복원 중 오류 발생:', error);
		return false;
	}
}
/**
 * 스토리지 용량 모니터링
 * 주기적으로 스토리지 용량을 확인하고 필요시 경고를 표시합니다.
 * @param callback 용량 경고 시 호출할 콜백 함수
 * @param interval 확인 간격 (밀리초)
 * @returns 모니터링 중지 함수
 */
export function monitorStorageCapacity(
	callback: (info: StorageCapacityInfo) => void,
	interval = 60000 // 기본값 1분
): () => void {
	// 초기 확인
	const initialCheck = checkStorageCapacity();
	if (initialCheck.isNearLimit) {
		callback(initialCheck);
	}

	// 주기적 확인 설정
	const timerId = setInterval(() => {
		const capacityInfo = checkStorageCapacity();
		if (capacityInfo.isNearLimit) {
			callback(capacityInfo);
		}
	}, interval);

	// 모니터링 중지 함수 반환
	return () => {
		clearInterval(timerId);
	};
}

/**
 * 자동 스토리지 최적화
 * 스토리지 용량이 임계값에 도달하면 자동으로 정리합니다.
 * @param threshold 정리 시작 임계값 (%, 기본값: 70)
 * @param callback 정리 후 호출할 콜백 함수
 * @returns 자동 최적화 중지 함수
 */
export function enableAutoStorageOptimization(
	threshold = STORAGE_CONSTANTS.AUTO_CLEANUP_THRESHOLD,
	callback?: (result: { success: boolean; freedSpace: number; message: string }) => void
): () => void {
	// 자동 정리 함수
	const autoCleanup = () => {
		const capacityInfo = checkStorageCapacity();
		if (capacityInfo.percentUsed >= threshold) {
			console.log(`스토리지 용량 임계값(${threshold}%) 초과, 자동 정리 시작...`);
			const result = cleanupStorage({
				removeSuccess: true,
				removeOldBackups: true,
				compressData: true
			});

			if (callback) {
				callback(result);
			}
		}
	};

	// 초기 확인
	autoCleanup();

	// 이벤트 리스너 등록 (스토리지 변경 감지)
	const storageListener = (event: StorageEvent) => {
		if (event.key && event.key.startsWith('batch_pallet_')) {
			autoCleanup();
		}
	};

	window.addEventListener('storage', storageListener);

	// 주기적 확인 (5분마다)
	const timerId = setInterval(autoCleanup, 5 * 60 * 1000);

	// 자동 최적화 중지 함수 반환
	return () => {
		clearInterval(timerId);
		window.removeEventListener('storage', storageListener);
	};
}

/**
 * 현재 팔레트 ID 설정
 * 현재 작업 중인 팔레트 ID를 설정합니다.
 * @param palletId 설정할 팔레트 ID
 */
export function setCurrentPalletId(palletId: string): void {
	try {
		const state = getBatchState();
		state.currentPalletId = palletId;

		// 해당 팔레트가 존재하지 않으면 생성
		if (!state.pallets[palletId]) {
			state.pallets[palletId] = {
				products: [],
				palletInfo: {}
			};
		}

		saveBatchState(state);
		console.log('현재 팔레트 ID가 설정되었습니다:', palletId);
	} catch (error) {
		console.error('현재 팔레트 ID 설정 중 오류 발생:', error);
	}
}

/**
 * 현재 팔레트 ID 가져오기
 * 현재 작업 중인 팔레트 ID를 반환합니다.
 * @returns 현재 팔레트 ID
 */
export function getCurrentPalletId(): string {
	try {
		const state = getBatchState();
		return state.currentPalletId;
	} catch (error) {
		console.error('현재 팔레트 ID 조회 중 오류 발생:', error);
		return '';
	}
}

/**
 * 팔레트별 상품 목록 가져오기
 * 특정 팔레트의 상품 목록을 반환합니다.
 * @param palletId 팔레트 ID
 * @returns 해당 팔레트의 상품 목록
 */
export function getPalletProducts(palletId: string): BatchProductData[] {
	try {
		const state = getBatchState();

		if (!state.pallets[palletId]) {
			return [];
		}

		return state.pallets[palletId].products.sort((a, b) => b.timestamp - a.timestamp);
	} catch (error) {
		console.error('팔레트 상품 목록 조회 중 오류 발생:', error);
		return [];
	}
}

/**
 * 팔레트별 상품 수 가져오기
 * 특정 팔레트의 상태별 상품 수를 반환합니다.
 * @param palletId 팔레트 ID
 * @returns 상태별 상품 수
 */
export function getPalletProductCount(palletId: string): {
	total: number;
	pending: number;
	submitting: number;
	success: number;
	failed: number;
} {
	try {
		const state = getBatchState();

		if (!state.pallets[palletId]) {
			return { total: 0, pending: 0, submitting: 0, success: 0, failed: 0 };
		}

		const products = state.pallets[palletId].products;
		return {
			total: products.length,
			pending: products.filter((p) => p.status === 'pending').length,
			submitting: products.filter((p) => p.status === 'submitting').length,
			success: products.filter((p) => p.status === 'success').length,
			failed: products.filter((p) => p.status === 'failed').length
		};
	} catch (error) {
		console.error('팔레트 상품 수 조회 중 오류 발생:', error);
		return { total: 0, pending: 0, submitting: 0, success: 0, failed: 0 };
	}
}

/**
 * 팔레트 존재 여부 확인
 * 지정된 팔레트가 로컬스토리지에 존재하는지 확인합니다.
 * @param palletId 팔레트 ID
 * @returns 팔레트 존재 여부
 */
export function hasPallet(palletId: string): boolean {
	try {
		const state = getBatchState();
		return palletId in state.pallets && state.pallets[palletId].products.length > 0;
	} catch (error) {
		console.error('팔레트 존재 여부 확인 중 오류 발생:', error);
		return false;
	}
}

/**
 * 모든 팔레트 목록 가져오기
 * 로컬스토리지에 저장된 모든 팔레트 목록을 반환합니다.
 * @returns 팔레트 ID와 상품 수 정보
 */
export function getAllPallets(): Array<{
	palletId: string;
	productCount: number;
	pendingCount: number;
	successCount: number;
	failedCount: number;
	lastUpdated: number;
}> {
	try {
		const state = getBatchState();
		const pallets: Array<{
			palletId: string;
			productCount: number;
			pendingCount: number;
			successCount: number;
			failedCount: number;
			lastUpdated: number;
		}> = [];

		for (const palletId in state.pallets) {
			const products = state.pallets[palletId].products;
			if (products.length > 0) {
				const lastUpdated = Math.max(...products.map((p) => p.timestamp));
				pallets.push({
					palletId,
					productCount: products.length,
					pendingCount: products.filter((p) => p.status === 'pending').length,
					successCount: products.filter((p) => p.status === 'success').length,
					failedCount: products.filter((p) => p.status === 'failed').length,
					lastUpdated
				});
			}
		}

		// 마지막 업데이트 시간 기준 내림차순 정렬
		return pallets.sort((a, b) => b.lastUpdated - a.lastUpdated);
	} catch (error) {
		console.error('팔레트 목록 조회 중 오류 발생:', error);
		return [];
	}
}

/**
 * 스토리지 사용량 통계
 * 로컬스토리지 사용량에 대한 상세 통계를 제공합니다.
 * @returns 스토리지 사용량 통계 정보
 */
export function getStorageStats(): {
	totalItems: number;
	batchItems: number;
	keysByPrefix: Record<string, number>;
	largestItems: Array<{ key: string; sizeKB: number }>;
} {
	try {
		let totalItems = 0;
		let batchItems = 0;
		const keysByPrefix: Record<string, number> = {};
		const itemSizes: Array<{ key: string; sizeKB: number }> = [];

		// 모든 항목 순회
		for (let i = 0; i < localStorage.length; i++) {
			const key = localStorage.key(i);
			if (key) {
				totalItems++;
				const value = localStorage.getItem(key) || '';
				const sizeBytes = (key.length + value.length) * 2;
				const sizeKB = Math.round((sizeBytes / 1024) * 10) / 10; // 소수점 한 자리까지

				// 배치 관련 항목 카운트
				if (key.startsWith('batch_pallet_')) {
					batchItems++;
				}

				// 접두사별 그룹화
				const prefix = key.split('_')[0] || 'other';
				keysByPrefix[prefix] = (keysByPrefix[prefix] || 0) + 1;

				// 크기 정보 저장
				itemSizes.push({ key, sizeKB });
			}
		}

		// 크기 기준 내림차순 정렬 후 상위 10개 항목 반환
		const largestItems = itemSizes.sort((a, b) => b.sizeKB - a.sizeKB).slice(0, 10);

		return {
			totalItems,
			batchItems,
			keysByPrefix,
			largestItems
		};
	} catch (error) {
		console.error('스토리지 통계 수집 중 오류 발생:', error);
		return {
			totalItems: 0,
			batchItems: 0,
			keysByPrefix: {},
			largestItems: []
		};
	}
}

/**
 * 데이터 복원 감지 및 알림 정보 생성
 * 페이지 로드 시 복원 가능한 데이터가 있는지 확인하고 알림 정보를 생성합니다.
 * @param currentPalletId 현재 선택된 팔레트 ID (선택 사항)
 * @returns 복원 정보 또는 null
 */
export function detectDataRestore(currentPalletId?: string): {
	hasRestorableData: boolean;
	restoreInfo: {
		palletId: string;
		productCount: number;
		pendingCount: number;
		successCount: number;
		failedCount: number;
		lastUpdated: number;
		timeSinceLastUpdate: number;
		isCurrentPallet: boolean;
		shouldNotify: boolean;
	} | null;
	allPallets: Array<{
		palletId: string;
		productCount: number;
		pendingCount: number;
		successCount: number;
		failedCount: number;
		lastUpdated: number;
		timeSinceLastUpdate: number;
	}>;
} {
	try {
		const state = getBatchState();
		const currentTime = Date.now();
		const RESTORE_THRESHOLD = 5 * 60 * 1000; // 5분 (밀리초)

		let hasRestorableData = false;
		let restoreInfo: any = null;
		const allPallets: any[] = [];

		// 현재 팔레트 ID가 제공되지 않은 경우 스토리지에서 가져오기
		const targetPalletId = currentPalletId || state.currentPalletId;

		// 모든 팔레트 검사
		for (const palletId in state.pallets) {
			const pallet = state.pallets[palletId];
			const products = pallet.products;

			if (products.length === 0) {
				continue;
			}

			// 상태별 카운트 계산
			const pendingCount = products.filter((p) => p.status === 'pending').length;
			const successCount = products.filter((p) => p.status === 'success').length;
			const failedCount = products.filter((p) => p.status === 'failed').length;
			const lastUpdated = Math.max(...products.map((p) => p.timestamp));
			const timeSinceLastUpdate = currentTime - lastUpdated;

			const palletInfo = {
				palletId,
				productCount: products.length,
				pendingCount,
				successCount,
				failedCount,
				lastUpdated,
				timeSinceLastUpdate
			};

			allPallets.push(palletInfo);

			// 복원 대상 조건 확인
			const hasUnfinishedWork = pendingCount > 0 || failedCount > 0;
			const isOldEnough = timeSinceLastUpdate > RESTORE_THRESHOLD;
			const isCurrentPallet = palletId === targetPalletId;

			if (hasUnfinishedWork && isOldEnough) {
				hasRestorableData = true;

				// 현재 팔레트를 우선적으로 선택, 없으면 가장 최근 팔레트 선택
				if (
					!restoreInfo ||
					(isCurrentPallet && !restoreInfo.isCurrentPallet) ||
					(isCurrentPallet === restoreInfo.isCurrentPallet && lastUpdated > restoreInfo.lastUpdated)
				) {
					restoreInfo = {
						...palletInfo,
						isCurrentPallet,
						shouldNotify: true
					};
				}
			}
		}

		// 복원 대상이 없지만 현재 팔레트에 데이터가 있는 경우도 확인
		if (!hasRestorableData && targetPalletId && state.pallets[targetPalletId]) {
			const pallet = state.pallets[targetPalletId];
			const products = pallet.products;

			if (products.length > 0) {
				const pendingCount = products.filter((p) => p.status === 'pending').length;
				const successCount = products.filter((p) => p.status === 'success').length;
				const failedCount = products.filter((p) => p.status === 'failed').length;
				const lastUpdated = Math.max(...products.map((p) => p.timestamp));
				const timeSinceLastUpdate = currentTime - lastUpdated;

				// 미완료 작업이 있고 최근 데이터가 아닌 경우에만 복원 정보로 설정
				if ((pendingCount > 0 || failedCount > 0) && timeSinceLastUpdate > RESTORE_THRESHOLD) {
					hasRestorableData = true;
					restoreInfo = {
						palletId: targetPalletId,
						productCount: products.length,
						pendingCount,
						successCount,
						failedCount,
						lastUpdated,
						timeSinceLastUpdate,
						isCurrentPallet: true,
						shouldNotify: false // 현재 팔레트의 최근 데이터는 알림 표시하지 않음
					};
				}
			}
		}

		// 마지막 업데이트 시간 기준으로 팔레트 정렬
		allPallets.sort((a, b) => b.lastUpdated - a.lastUpdated);

		return {
			hasRestorableData,
			restoreInfo,
			allPallets
		};
	} catch (error) {
		console.error('데이터 복원 감지 중 오류 발생:', error);
		return {
			hasRestorableData: false,
			restoreInfo: null,
			allPallets: []
		};
	}
}

/**
 * 데이터 복원 상태 기록
 * 데이터 복원이 완료되었음을 기록하여 중복 알림을 방지합니다.
 * @param palletId 복원된 팔레트 ID
 */
export function markDataRestored(palletId: string): void {
	try {
		const restoreKey = `${BATCH_STORAGE_KEYS.STATE}_restored_${palletId}`;
		localStorage.setItem(restoreKey, Date.now().toString());
		console.log('데이터 복원 상태 기록됨:', palletId);
	} catch (error) {
		console.error('데이터 복원 상태 기록 중 오류 발생:', error);
	}
}

/**
 * 데이터 복원 상태 확인
 * 특정 팔레트의 데이터가 이미 복원되었는지 확인합니다.
 * @param palletId 확인할 팔레트 ID
 * @param maxAge 복원 기록의 최대 유효 시간 (밀리초, 기본값: 1시간)
 * @returns 복원 여부
 */
export function isDataRestored(palletId: string, maxAge = 60 * 60 * 1000): boolean {
	try {
		const restoreKey = `${BATCH_STORAGE_KEYS.STATE}_restored_${palletId}`;
		const restoredTime = localStorage.getItem(restoreKey);

		if (!restoredTime) {
			return false;
		}

		const timestamp = parseInt(restoredTime, 10);
		const currentTime = Date.now();

		// 복원 기록이 너무 오래된 경우 무효로 처리
		if (currentTime - timestamp > maxAge) {
			localStorage.removeItem(restoreKey);
			return false;
		}

		return true;
	} catch (error) {
		console.error('데이터 복원 상태 확인 중 오류 발생:', error);
		return false;
	}
}

/**
 * 복원 기록 정리
 * 오래된 복원 기록들을 정리합니다.
 * @param maxAge 복원 기록의 최대 유효 시간 (밀리초, 기본값: 24시간)
 */
export function cleanupRestoreRecords(maxAge = 24 * 60 * 60 * 1000): void {
	try {
		const currentTime = Date.now();
		const keysToRemove: string[] = [];

		// 복원 기록 키들 찾기
		for (let i = 0; i < localStorage.length; i++) {
			const key = localStorage.key(i);
			if (key && key.startsWith(`${BATCH_STORAGE_KEYS.STATE}_restored_`)) {
				const restoredTime = localStorage.getItem(key);
				if (restoredTime) {
					const timestamp = parseInt(restoredTime, 10);
					if (currentTime - timestamp > maxAge) {
						keysToRemove.push(key);
					}
				} else {
					keysToRemove.push(key);
				}
			}
		}

		// 오래된 기록 삭제
		keysToRemove.forEach((key) => {
			localStorage.removeItem(key);
		});

		if (keysToRemove.length > 0) {
			console.log(`${keysToRemove.length}개의 오래된 복원 기록이 정리되었습니다.`);
		}
	} catch (error) {
		console.error('복원 기록 정리 중 오류 발생:', error);
	}
}
