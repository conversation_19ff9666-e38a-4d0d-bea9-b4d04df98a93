/**
 * JWT 토큰 유틸리티 함수들
 * JWT 토큰 디코딩, 유효성 검사, 만료 확인 등의 기능을 제공합니다.
 */

import type { JWTPayload } from '$lib/types/auth';

/**
 * JWT 토큰을 디코딩하여 페이로드를 추출합니다.
 * @param token JWT 토큰 문자열
 * @returns 디코딩된 페이로드 또는 null (유효하지 않은 경우)
 */
export function decodeJWT(token: string): JWTPayload | null {
	try {
		if (!token) {
			return null;
		}

		// JWT는 header.payload.signature 형태로 구성됨
		const parts = token.split('.');
		if (parts.length !== 3) {
			return null;
		}

		// Base64URL 디코딩
		const payload = parts[1];
		const decoded = atob(payload.replace(/-/g, '+').replace(/_/g, '/'));

		return JSON.parse(decoded) as JWTPayload;
	} catch (error) {
		console.error('JWT 디코딩 실패:', error);
		return null;
	}
}

/**
 * JWT 토큰의 만료 시간을 확인합니다.
 * @param token JWT 토큰 문자열
 * @param bufferSeconds 여유 시간 (초 단위, 기본값: 0 - 정확한 만료 시간 사용)
 * @returns 만료 여부 (true: 만료됨, false: 유효함)
 */
export function isTokenExpired(token: string, bufferSeconds: number = 0): boolean {
	const payload = decodeJWT(token);
	if (!payload || !payload.exp) {
		return true; // 유효하지 않은 토큰은 만료된 것으로 처리
	}

	// 현재 시간과 비교 (버퍼 시간 적용)
	const currentTime = Math.floor(Date.now() / 1000);

	return payload.exp <= currentTime + bufferSeconds;
}

/**
 * JWT 토큰의 유효성을 검사합니다.
 * @param token JWT 토큰 문자열
 * @param bufferSeconds 만료 확인 시 여유 시간 (초 단위, 기본값: 0)
 * @returns 유효성 여부
 */
export function isTokenValid(token: string, bufferSeconds: number = 0): boolean {
	if (!token) {
		return false;
	}

	const payload = decodeJWT(token);
	if (!payload) {
		return false;
	}

	// 기본 필드 검증
	if (!payload.sub || !payload.iat || !payload.exp) {
		return false;
	}

	// 만료 시간 검증 (버퍼 시간 적용)
	return !isTokenExpired(token, bufferSeconds);
}

/**
 * 토큰 타입을 확인합니다.
 * @param token JWT 토큰 문자열
 * @returns 토큰 타입 ('access', 'refresh', 또는 null)
 */
export function getTokenType(token: string): 'access' | 'refresh' | null {
	const payload = decodeJWT(token);
	if (!payload) {
		return null;
	}

	return payload.type || null;
}

/**
 * 토큰의 남은 유효 시간을 초 단위로 반환합니다.
 * @param token JWT 토큰 문자열
 * @returns 남은 시간 (초) 또는 0 (만료된 경우)
 */
export function getTokenRemainingTime(token: string): number {
	const payload = decodeJWT(token);
	if (!payload || !payload.exp) {
		return 0;
	}

	const currentTime = Math.floor(Date.now() / 1000);
	const remainingTime = payload.exp - currentTime;

	return Math.max(0, remainingTime);
}

/**
 * 토큰에서 사용자 ID를 추출합니다.
 * @param token JWT 토큰 문자열
 * @returns 사용자 ID 또는 null
 */
export function getUserIdFromToken(token: string): string | null {
	const payload = decodeJWT(token);
	return payload?.sub || null;
}

/**
 * 개발 환경에서 토큰 정보를 콘솔에 출력합니다.
 * @param token JWT 토큰 문자열
 * @param label 로그 라벨
 */
export function debugToken(token: string, label: string = 'Token'): void {
	if (import.meta.env.DEV) {
		if (!token) {
			console.log(`[JWT Debug] ${label}: 토큰 없음`);
			return;
		}

		// JWT 형식인지 확인 (header.payload.signature)
		const parts = token.split('.');
		if (parts.length === 3) {
			// JWT 토큰인 경우
			const payload = decodeJWT(token);
			if (payload) {
				console.log(`[JWT Debug] ${label}:`, {
					format: 'JWT',
					userId: payload.sub,
					type: payload.type,
					issuedAt: new Date(payload.iat * 1000).toLocaleString(),
					expiresAt: new Date(payload.exp * 1000).toLocaleString(),
					isExpired: isTokenExpired(token),
					remainingTime: `${getTokenRemainingTime(token)}초`
				});
			} else {
				console.log(`[JWT Debug] ${label}: JWT 형식이지만 디코딩 실패`);
			}
		} else {
			// JWT가 아닌 토큰인 경우 (Refresh Token 등)
			console.log(`[JWT Debug] ${label}:`, {
				format: 'Non-JWT',
				length: token.length,
				preview:
					token.length > 20
						? `${token.substring(0, 10)}...${token.substring(token.length - 10)}`
						: token
			});
		}
	}
}
