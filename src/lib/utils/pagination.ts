// 개선된 간소화된 페이지네이션 인터페이스
export interface OptimizedPageData {
    total: number;           // 전체 데이터 수
    current_page: number;    // 현재 페이지
    last_page: number;       // 마지막 페이지
    per_page: number;        // 페이지당 데이터 수
    from: number;            // 현재 페이지 시작 번호
    to: number;              // 현재 페이지 끝 번호
    has_prev: boolean;       // 이전 페이지 존재 여부
    has_next: boolean;       // 다음 페이지 존재 여부
}

// 개선된 페이지네이션 처리 함수
export function processPageData(pageData: OptimizedPageData) {
    const pageCurrentPage = pageData.current_page;
    const pageTotal = pageData.total;
    const pageFrom = pageData.from;
    const pageTo = pageData.to;
    const pageLastPage = pageData.last_page;
    const pagePerPage = pageData.per_page;
    const pageStartNo = pageTotal - pageFrom + 1;
    const hasNext = pageData.has_next;
    const hasPrev = pageData.has_prev;

    // 클라이언트에서 페이지 링크 생성
    const pageLinks = generatePageLinks(pageCurrentPage, pageLastPage);

    return {
        pageTotal,
        pageCurrentPage,
        pageFrom,
        pageTo,
        pageLastPage,
        pagePerPage,
        pageStartNo,
        hasNext,
        hasPrev,
        pageLinks
    };
}

// 클라이언트에서 페이지 링크 생성 함수
export function generatePageLinks(currentPage: number, lastPage: number, maxLinks: number = 10) {
    const links = [];
    
    // 표시할 페이지 범위 계산
    const halfRange = Math.floor(maxLinks / 2);
    let startPage = Math.max(1, currentPage - halfRange);
    let endPage = Math.min(lastPage, startPage + maxLinks - 1);
    
    // 끝 페이지 기준으로 시작 페이지 재조정
    if (endPage - startPage + 1 < maxLinks) {
        startPage = Math.max(1, endPage - maxLinks + 1);
    }
    
    // 첫 페이지 추가 (1이 범위에 없을 경우)
    if (startPage > 1) {
        links.push({ label: '1', active: false, page: 1 });
        if (startPage > 2) {
            links.push({ label: '...', active: false, page: null });
        }
    }
    
    // 페이지 범위 추가
    for (let i = startPage; i <= endPage; i++) {
        links.push({
            label: i.toString(),
            active: i === currentPage,
            page: i
        });
    }
    
    // 마지막 페이지 추가 (lastPage가 범위에 없을 경우)
    if (endPage < lastPage) {
        if (endPage < lastPage - 1) {
            links.push({ label: '...', active: false, page: null });
        }
        links.push({ label: lastPage.toString(), active: false, page: lastPage });
    }
    
    return links;
}

// 페이지네이션 정보 요약 함수
export function getPaginationSummary(pageData: OptimizedPageData) {
    const { total, current_page, per_page, from, to } = pageData;
    
    return {
        showing: `${from}~${to}`,
        total: total,
        page: `${current_page}/${Math.ceil(total / per_page)}`,
        hasData: total > 0
    };
}