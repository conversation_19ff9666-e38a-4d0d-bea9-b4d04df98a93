/**
 * 팔레트 인쇄용 쿼리 파라미터를 구성합니다.
 * - 선택된 항목의 첫 번째 유효한 export_date를 사용합니다.
 * - 팔레트 코드는 '|' 로 연결합니다.
 */
export function buildPalletPrintParams(
  selectedIds: number[],
  items: Array<{ id: number; exported_at?: string | null; pallet_info: { code: string } }>
): { exportDateParam: string | null; palletCodeList: string } {
  // 방어 로직: 선택 항목 없음
  if (!Array.isArray(selectedIds) || selectedIds.length === 0) {
    return { exportDateParam: null, palletCodeList: '' };
  }

  // id -> item 매핑
  const idToItem = new Map<number, { exported_at?: string | null; pallet_info: { code: string } }>();
  items.forEach((it) => idToItem.set(it.id, it as any));

  // 선택된 순서대로 코드/날짜 수집
  const codes: string[] = [];
  let exportDateParam: string | null = null;

  selectedIds.forEach((id) => {
    const item = idToItem.get(id);
    if (!item) return;
    // 코드 수집
    const code = item.pallet_info?.code;
    if (code) codes.push(code);

    // 첫 유효한 export_date 저장
    if (exportDateParam === null && item.exported_at) {
      exportDateParam = item.exported_at;
    }
  });

  return {
    exportDateParam,
    palletCodeList: codes.join('|')
  };
}


