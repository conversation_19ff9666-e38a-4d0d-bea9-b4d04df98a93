/**
 * 빠른 토큰 상태 확인 유틸리티
 * 개발 환경에서 토큰 문제를 빠르게 진단하기 위한 도구
 */

import { tokenService } from '$lib/services/tokenService';
import { decodeJWT } from './jwtUtils';

/**
 * 현재 토큰 상태를 빠르게 확인합니다.
 */
export async function quickTokenCheck(): Promise<void> {
	if (!import.meta.env.DEV) {
		return;
	}

	console.group('🔍 빠른 토큰 상태 확인');

	try {
		// 토큰 서비스 초기화
		await tokenService.initialize();

		// 저장소에서 직접 토큰 조회
		const storage = await tokenService.ensureStorage();
		const rawAccessToken = await storage.getAccessToken();
		const rawRefreshToken = await storage.getRefreshToken();

		console.log('📊 토큰 존재 여부:');
		console.log('  - 액세스 토큰:', !!rawAccessToken);
		console.log('  - 리프레시 토큰:', !!rawRefreshToken);

		if (rawAccessToken) {
			const accessPayload = decodeJWT(rawAccessToken);
			if (accessPayload) {
				const now = Math.floor(Date.now() / 1000);
				const remaining = accessPayload.exp - now;
				console.log('⏰ 액세스 토큰:');
				console.log(`  - 만료까지: ${Math.floor(remaining / 60)}분 ${remaining % 60}초`);
				console.log(`  - 만료 시간: ${new Date(accessPayload.exp * 1000).toLocaleString()}`);
			}
		}

		if (rawRefreshToken) {
			if (rawRefreshToken.split('.').length === 3) {
				// JWT 형식 리프레시 토큰
				const refreshPayload = decodeJWT(rawRefreshToken);
				if (refreshPayload) {
					const now = Math.floor(Date.now() / 1000);
					const remaining = refreshPayload.exp - now;
					console.log('🔄 리프레시 토큰 (JWT):');
					console.log(`  - 만료까지: ${Math.floor(remaining / 60)}분 ${remaining % 60}초`);
					console.log(`  - 만료 시간: ${new Date(refreshPayload.exp * 1000).toLocaleString()}`);
					console.log(`  - 상태: ${remaining > 0 ? '✅ 유효' : '❌ 만료'}`);
				}
			} else {
				// Non-JWT 리프레시 토큰
				console.log('🔄 리프레시 토큰 (Non-JWT):');
				console.log(`  - 길이: ${rawRefreshToken.length}자`);
				console.log(`  - 미리보기: ${rawRefreshToken.substring(0, 20)}...`);
			}
		} else {
			console.log('❌ 리프레시 토큰이 없습니다!');
		}

		// 토큰 서비스를 통한 유효성 검사 결과
		const validAccessToken = await tokenService.getAccessToken();
		const validRefreshToken = await tokenService.getRefreshToken();

		console.log('✅ 토큰 서비스 유효성 검사:');
		console.log('  - 유효한 액세스 토큰:', !!validAccessToken);
		console.log('  - 유효한 리프레시 토큰:', !!validRefreshToken);

		// 인증 상태
		const isAuthenticated = await tokenService.isAuthenticated();
		console.log('🔐 인증 상태:', isAuthenticated ? '✅ 인증됨' : '❌ 미인증');
	} catch (error) {
		console.error('❌ 토큰 확인 중 오류:', error);
	}

	console.groupEnd();
}

/**
 * 토큰 갱신을 즉시 테스트합니다.
 */
export async function testTokenRefresh(): Promise<void> {
	if (!import.meta.env.DEV) {
		return;
	}

	console.group('🔄 토큰 갱신 테스트');

	try {
		console.log('갱신 전 토큰 상태:');
		await quickTokenCheck();

		console.log('\n토큰 갱신 시도 중...');
		const { tokenRefreshService } = await import('$lib/services/tokenRefreshService');
		const newToken = await tokenRefreshService.refreshAccessToken();

		if (newToken) {
			console.log('✅ 토큰 갱신 성공!');
			console.log('\n갱신 후 토큰 상태:');
			await quickTokenCheck();
		} else {
			console.log('❌ 토큰 갱신 실패');
		}
	} catch (error) {
		console.error('❌ 토큰 갱신 테스트 중 오류:', error);
	}

	console.groupEnd();
}

// 전역 함수로 등록 (개발 환경에서만)
if (import.meta.env.DEV && typeof window !== 'undefined') {
	(window as any).quickTokenCheck = quickTokenCheck;
	(window as any).testTokenRefresh = testTokenRefresh;
	console.log('🔧 토큰 디버그 함수들이 등록되었습니다:');
	console.log('- quickTokenCheck() : 토큰 상태 확인');
	console.log('- testTokenRefresh() : 토큰 갱신 테스트');
}
