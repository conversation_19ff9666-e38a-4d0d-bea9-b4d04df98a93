/**
 * 토큰 디버깅 유틸리티
 * 개발 환경에서 토큰 관련 문제를 진단하는 도구들을 제공합니다.
 */

import { tokenService } from '$lib/services/tokenService';
import { decodeJWT, debugToken, getTokenRemainingTime } from './jwtUtils';

/**
 * 현재 토큰 상태를 상세히 분석합니다.
 */
export async function analyzeTokens(): Promise<void> {
	if (!import.meta.env.DEV) {
		return;
	}

	console.group('[Token Debugger] 토큰 상태 분석');

	try {
		// 토큰 서비스 초기화 확인
		await tokenService.initialize();

		// 저장소에서 직접 토큰 조회
		const storage = await tokenService.ensureStorage();
		const rawAccessToken = await storage.getAccessToken();
		const rawRefreshToken = await storage.getRefreshToken();

		console.log('=== 저장소 원본 토큰 ===');
		console.log('액세스 토큰 존재:', !!rawAccessToken);
		console.log('액세스 토큰 길이:', rawAccessToken?.length || 0);
		console.log('리프레시 토큰 존재:', !!rawRefreshToken);
		console.log('리프레시 토큰 길이:', rawRefreshToken?.length || 0);

		if (rawRefreshToken) {
			console.log('리프레시 토큰 미리보기:', rawRefreshToken.substring(0, 50) + '...');
			console.log('리프레시 토큰 JWT 형식 여부:', rawRefreshToken.split('.').length === 3);
		} else {
			console.warn('⚠️ 리프레시 토큰이 저장소에 없습니다!');
		}

		if (rawAccessToken) {
			debugToken(rawAccessToken, '원본 액세스 토큰');
		}

		if (rawRefreshToken) {
			debugToken(rawRefreshToken, '원본 리프레시 토큰');
		}

		// 토큰 서비스를 통한 유효성 검사
		console.log('\n=== 토큰 서비스 유효성 검사 ===');
		const validAccessToken = await tokenService.getAccessToken();
		const validRefreshToken = await tokenService.getRefreshToken();

		console.log('유효한 액세스 토큰:', !!validAccessToken);
		console.log('유효한 리프레시 토큰:', !!validRefreshToken);

		// 토큰 상태 정보
		const status = await tokenService.getTokenStatus();
		console.log('\n=== 토큰 상태 정보 ===');
		console.table(status);

		// 만료 시간 분석
		if (rawAccessToken && rawAccessToken.split('.').length === 3) {
			const accessPayload = decodeJWT(rawAccessToken);
			if (accessPayload) {
				const now = Math.floor(Date.now() / 1000);
				const accessExpiry = new Date(accessPayload.exp * 1000);
				const accessRemaining = accessPayload.exp - now;

				console.log('\n=== 액세스 토큰 만료 분석 ===');
				console.log('현재 시간:', new Date().toLocaleString());
				console.log('만료 시간:', accessExpiry.toLocaleString());
				console.log(
					'남은 시간:',
					`${Math.floor(accessRemaining / 60)}분 ${accessRemaining % 60}초`
				);
				console.log('만료 여부:', accessRemaining <= 0 ? '만료됨' : '유효함');
			}
		}

		if (rawRefreshToken && rawRefreshToken.split('.').length === 3) {
			const refreshPayload = decodeJWT(rawRefreshToken);
			if (refreshPayload) {
				const now = Math.floor(Date.now() / 1000);
				const refreshExpiry = new Date(refreshPayload.exp * 1000);
				const refreshRemaining = refreshPayload.exp - now;

				console.log('\n=== 리프레시 토큰 만료 분석 ===');
				console.log('현재 시간:', new Date().toLocaleString());
				console.log('만료 시간:', refreshExpiry.toLocaleString());
				console.log(
					'남은 시간:',
					`${Math.floor(refreshRemaining / 60)}분 ${refreshRemaining % 60}초`
				);
				console.log('만료 여부:', refreshRemaining <= 0 ? '만료됨' : '유효함');
			}
		}

		// 시간 동기화 확인
		console.log('\n=== 시간 동기화 확인 ===');
		console.log('로컬 시간:', new Date().toISOString());
		console.log('Unix 타임스탬프:', Math.floor(Date.now() / 1000));
	} catch (error) {
		console.error('토큰 분석 중 오류:', error);
	}

	console.groupEnd();
}

/**
 * 토큰 갱신 과정을 모니터링합니다.
 */
export async function monitorTokenRefresh(): Promise<void> {
	if (!import.meta.env.DEV) {
		return;
	}

	console.log('[Token Debugger] 토큰 갱신 모니터링 시작');

	// 갱신 전 상태
	console.group('갱신 전 토큰 상태');
	await analyzeTokens();
	console.groupEnd();

	try {
		// 토큰 갱신 시도
		const { tokenRefreshService } = await import('$lib/services/tokenRefreshService');
		const newToken = await tokenRefreshService.refreshAccessToken();

		console.log('토큰 갱신 결과:', newToken ? '성공' : '실패');

		// 갱신 후 상태
		console.group('갱신 후 토큰 상태');
		await analyzeTokens();
		console.groupEnd();
	} catch (error) {
		console.error('토큰 갱신 모니터링 중 오류:', error);
	}
}

/**
 * 서버 시간과 로컬 시간의 차이를 확인합니다.
 */
export async function checkTimeSync(): Promise<void> {
	if (!import.meta.env.DEV) {
		return;
	}

	try {
		console.group('[Token Debugger] 시간 동기화 확인');

		const localTime = new Date();
		console.log('로컬 시간:', localTime.toISOString());

		// 서버에서 현재 시간 조회 (간단한 API 호출)
		const response = await fetch(`${import.meta.env.VITE_HOME_URL}/api/auth/time`, {
			method: 'GET',
			headers: {
				Accept: 'application/json'
			}
		});

		if (response.ok) {
			const data = await response.json();
			const serverTime = new Date(data.timestamp);
			const timeDiff = Math.abs(localTime.getTime() - serverTime.getTime()) / 1000;

			console.log('서버 시간:', serverTime.toISOString());
			console.log('시간 차이:', `${timeDiff}초`);

			if (timeDiff > 60) {
				console.warn('⚠️ 로컬 시간과 서버 시간의 차이가 1분 이상입니다!');
				console.warn('이는 토큰 만료 문제의 원인이 될 수 있습니다.');
			} else {
				console.log('✅ 시간 동기화 상태 양호');
			}
		} else {
			console.warn('서버 시간 조회 실패:', response.status);
		}

		console.groupEnd();
	} catch (error) {
		console.error('시간 동기화 확인 실패:', error);
	}
}

/**
 * 토큰 문제 진단 도구
 */
export async function diagnoseTokenIssues(): Promise<void> {
	if (!import.meta.env.DEV) {
		return;
	}

	console.group('[Token Debugger] 토큰 문제 진단');

	// 1. 토큰 상태 분석
	await analyzeTokens();

	// 2. 시간 동기화 확인
	await checkTimeSync();

	// 3. 네트워크 연결 확인
	try {
		const response = await fetch(`${import.meta.env.VITE_HOME_URL}/api/auth/ping`, {
			method: 'GET',
			headers: {
				Accept: 'application/json'
			}
		});

		console.log('서버 연결 상태:', response.ok ? '정상' : `오류 (${response.status})`);
	} catch (error) {
		console.error('서버 연결 실패:', error);
	}

	console.groupEnd();
}

// 전역 디버그 함수로 등록 (개발 환경에서만)
if (import.meta.env.DEV && typeof window !== 'undefined') {
	(window as any).tokenDebugger = {
		analyze: analyzeTokens,
		monitor: monitorTokenRefresh,
		checkTime: checkTimeSync,
		diagnose: diagnoseTokenIssues
	};

	console.log('🔧 토큰 디버거가 활성화되었습니다. 다음 명령어를 사용하세요:');
	console.log('- tokenDebugger.analyze() : 토큰 상태 분석');
	console.log('- tokenDebugger.monitor() : 토큰 갱신 모니터링');
	console.log('- tokenDebugger.checkTime() : 시간 동기화 확인');
	console.log('- tokenDebugger.diagnose() : 종합 진단');
}
