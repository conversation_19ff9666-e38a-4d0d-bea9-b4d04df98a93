<script lang="ts">
	import { onDestroy, onMount } from 'svelte';
	import { page } from '$app/state';

	import Pusher, { Channel } from 'pusher-js';

	import { Toasts } from 'svoast';

	import { executeAsk, executeMessage } from '$lib/Functions';
	import { getUser, type User } from '$lib/User';
	import { redirectToLogin } from '$lib/utils/authHelpers';
	import { goto } from '$app/navigation';
	import TokenExpiryWarning from '$lib/components/TokenExpiryWarning.svelte';

	interface Props {
		children?: import('svelte').Snippet;
	}

	let { children }: Props = $props();

	let user: boolean | User = false;

	const channelName = 'cnsprowms';
	const channelRole = ['Super-Admin', 'Admin'];
	let pusher: Pusher | null = null;
	let channel: Channel | null = null;

	async function messageHandler(data: any) {
		if (data.success) {
			let msg = data.message.replace(/\\n/g, '\n').replace(/<br>/g, '\n');

			if (data.redirect) {
				const answer = await executeAsk(msg);
				if (answer) {
					await goto('/dashboard').then(() => {
						goto(data.redirect);
					});
				}
			} else {
				await executeMessage(msg);
			}
		}
	}

	onMount(async () => {
		try {
			user = getUser();

			// 개발 환경에서 토큰 디버거 초기화
			if (import.meta.env.DEV) {
				import('$lib/utils/tokenDebugger');
				import('$lib/utils/quickTokenCheck');
			}

			Pusher.logToConsole = true;
			pusher = new Pusher(import.meta.env.VITE_PUSHER_APP_KEY, {
				cluster: import.meta.env.VITE_PUSHER_APP_CLUSTER
			});
			channel = pusher.subscribe(channelName);

			// 관리자 메시지만 수신
			if (channelRole.includes(user.role)) {
				channel.bind('admin-notification', (data: any) => messageHandler(data));
			}

			channel.bind('notification', (data: any) => messageHandler(data));
		} catch (error) {
			await executeMessage('회원정보 변환 실패', 'warning');

			await redirectToLogin(page.url.pathname);
		}
	});

	onDestroy(() => {
		console.log('로그아웃을 클릭할 때');
		if (channel) {
			channel.unbind('admin-notification');
			channel.unbind('notification');
			pusher?.unsubscribe(channelName);
		}

		channel = null;
		pusher?.disconnect();
		pusher = null;
	});
</script>

<Toasts position="bottom-right" />

<!-- 토큰 만료 경고 컴포넌트 -->
<TokenExpiryWarning
	warningMinutes={5}
	onextend={() => executeMessage('세션이 연장되었습니다.', 'success')}
	ondismiss={() => console.log('토큰 만료 경고 닫힘')}
/>

<div id="container">
	{@render children?.()}
</div>
