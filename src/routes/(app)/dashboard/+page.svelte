<script lang="ts">
	import { getUser, type User } from '$lib/User';
	import type { Breadcrumb } from '$lib/types/types';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';

	let user: User = getUser();

	const breadcrumbs: Breadcrumb[] = [
		{ title: '대시보드', url: '#' },
	];
</script>

<svelte:head>
	<title>대시보드</title>
</svelte:head>

<AppLayout {user}>
	{#snippet main()}
		<div >
			<TitleBar {breadcrumbs} />
			
			<section class="main-section">
				<div class="w-full p-2 flex flex-col-reverse lg:flex-row items-center justify-center xl:justify-between">
					
					<ul class="text-sm list-disc list-inside">
						<li>페이지 새로고침: F5키</li>
						<li>이전 페이지로 이동: <span class="kbd">Alt</span> + <span class="kbd">←</span>(왼쪽 방향 화살표) 키 / 마우스 좌측 1 버튼</li>
						<li>다음 페이지로 이동: <span class="kbd">Alt</span> + <span class="kbd">→</span>(오른쪽 방향 화살표) 키 / 마우스 좌측 2 버튼</li>
						<li>
							오류가 난다면 어떤 작업을 하다가 어떻게 오류가 났는지 상세하게 알려 주세요.(가능하면 오류 메시지를 캡쳐해서 보여 주세요.)
						</li>
					</ul>
				
				</div>
			</section>
		</div>
	{/snippet}
</AppLayout>