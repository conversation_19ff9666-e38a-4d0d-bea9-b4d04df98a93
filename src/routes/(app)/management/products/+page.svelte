<script lang="ts">
	import { getUser } from '$lib/User';

	const user = getUser();

	import type { Breadcrumb } from '$lib/types/types';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import AppLayout from '$components/Layouts/AppLayout.svelte';

	const breadcrumbs: Breadcrumb[] = [
		{ title: '관리', url: '/management/products' },
		{ title: '상품 관리', url: '/management/products' }
	];
</script>

<svelte:head>
	<title>관리 > 상품 관리</title>
</svelte:head>

<AppLayout {user}>
	{#snippet main()}
		<div >
			<TitleBar {breadcrumbs} />
			
			<section class="main-section">
				상품 관리 페이지
			</section>
		</div>
	{/snippet}
</AppLayout>