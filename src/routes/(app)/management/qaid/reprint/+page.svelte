<script lang="ts">
	import { getUser, type User } from '$lib/User';
	import { onMount } from 'svelte';
	import { page } from '$app/state';
	import type { Breadcrumb } from '$lib/types/types';
	import { authClient } from '$lib/services/AxiosBackend';
	import { isRePrintFromAdmin } from '$lib/utils/BarcodeUtils';

	import {
		dateFormat,
		executeMessage,
		formatDateToString,
		getBeginAt,
		getNumberFormat,
		getPayload,
		handleDownload
	} from '$lib/Functions';
	import { loadQaid, qaidStore } from '$stores/qaidStore';
	import { EXCEL_DOWNLOAD_URL } from '$stores/constant';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import Loading from '$components/Loading/Circle2.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import SearchUI from '$components/UI/SearchUI.svelte';
	import TableTop from '$components/UI/TableTop.svelte';
	import Paginate from '$components/UI/Paginate.svelte';
	import ButtonExcelDownload from '$components/Button/ExcelDownload.svelte';

	import Icon from 'svelte-awesome';
	import { faPrint } from '@fortawesome/free-solid-svg-icons/faPrint';

	let user: User = getUser();
	const apiUrl = '/api/wms/qaids';
	const localUrl = page.url.pathname;
	let isLoading = $state(false); // 로딩중 표시 여부

	// 검색 query string 시작 ==========
	let beginAt = $state(page.url.searchParams.get('beginAt') || getBeginAt(0));
	let endAt = $state(page.url.searchParams.get('endAt') || formatDateToString(new Date()));
	let keyword = $state(''); // 검색어
	let p = $state(page.url.searchParams.get('p') || '1');
	let pageSize = $state(page.url.searchParams.get('pageSize') || '16');
	let searchParams = $state('');
	let apiSearchParams = $state('');
	let startNo = $state(0);
	// 검색 query string 종료 ==========

	// =============== 페이지 내 필요한 변수들 시작 ===============
	let ids: [] = $state([]); // 전체 선택: 상품의 id를 모으는 변수
	// =============== 페이지 내 필요한 변수들 종료 ===============

	// 모달창: 미등록상품 등록/수정 창 시작========
	let modal: HTMLDialogElement;
	let histories: [] = $state([]);
	// 모달창: 미등록상품 등록/수정 창 종료========

	/**
	 * API로 부터 데이터를 가져온다.
	 */
	async function makeData(search = false) {
		isLoading = true;

		if (search) {
			p = '1';
		}

		const common_params = {
			beginAt: beginAt,
			endAt: endAt,
			keyword: keyword,
			pageSize: pageSize
		};

		const params = new URLSearchParams({ p: p, ...common_params });
		const apiParams = new URLSearchParams({ page: p, ...common_params });

		searchParams = params.toString(); // local
		apiSearchParams = apiParams.toString(); // api

		await loadQaid(`${apiUrl}?${apiSearchParams}`, user);

		if ($qaidStore) {
			startNo = $qaidStore.pageStartNo ?? 0;
		}

		isLoading = false;
	}

	/**
	 * 페이지 변경 핸들러 (최적화된 페이지네이션용)
	 */
	function handlePageChange(page: number) {
		p = page.toString();
		makeData();
	}

	/**
	 * 자식 컴포넌트에서 변경된 변수의 값을 매칭
	 *
	 * @param value
	 */
	function changeSearchParams(value: any) {
		if (value.detail.p) p = value.detail.p;
		if (value.detail.pageSize) pageSize = value.detail.pageSize;

		makeData();
	}

	/**
	 * QAID 재발행 내역
	 *
	 * @param id
	 */
	async function getReprintHistory(id: number) {
		const { status, data } = await authClient.get(`/api/wms/qaids/history/${id}`);

		if (status === 200 && data.success) {
			histories = data.data.items;
			modal.showModal();
		} else {
			await executeMessage(data.data.message, 'error');
		}
	}

	/**
	 * 엑셀 다운로드 핸들러
	 */
	async function handleExcelDownload(e: Event) {
		e.preventDefault();
		isLoading = true;

		const payload = {
			exportType: 'qaids',
			beginAt: beginAt,
			endAt: endAt,
			keyword: keyword
		};

		await handleDownload(EXCEL_DOWNLOAD_URL, payload);
		isLoading = false;
	}

	// onMount는 한 번 실행되고 나면 페이지가 변경되지 않는 한 다시 실행되지 않는다.
	onMount(async () => {
		qaidStore.set({});
		await makeData();

		modal = document.getElementById('my_modal_1') as HTMLDialogElement;
	});

	const breadcrumbs: Breadcrumb[] = [
		{ title: '관리', url: '/management/qaid/reprint' },
		{ title: 'QAID 재발행 리스트', url: '/management/qaid/reprint' }
	];
</script>

<svelte:head>
	<title>관리 > QAID 재발행 리스트</title>
</svelte:head>

{#if isLoading}
	<Loading size="60" unit="px" />
{/if}

<AppLayout {user}>
	{#snippet main()}
		<div>
			<TitleBar {breadcrumbs} />

			<section class="main-section">
				<SearchUI>
					<div class="pl-3 flex space-x-2">
						<input
							bind:value={beginAt}
							class="input input-bordered input-sm w-36 focus:ring-0 focus:outline-none"
							name="beginAt"
							onchange={async () => {
								await makeData();
							}}
							type="date"
						/>

						<span class="mx-2 font-bold text-xl"> ~ </span>

						<input
							bind:value={endAt}
							class="input input-bordered input-sm w-36 focus:ring-0 focus:outline-none"
							name="endAt"
							onchange={async () => {
								await makeData();
							}}
							type="date"
						/>

						<input
							bind:value={keyword}
							class="input input-bordered input-sm"
							onkeydown={async (e) => {
								if (e.key === 'Enter') {
									await makeData();
								}
							}}
							placeholder="QAID, 재발행자 검색"
							type="text"
						/>

						<ButtonExcelDownload onclick={handleExcelDownload} useTooltip={true} />
					</div>
				</SearchUI>

				<!-- 리스트 시작 -->
				{#snippet tableHeader()}
					<tr class="bg-base-content text-base-300 text-center h-[30px]">
						<th class="p-0.5">번호</th>
						<th class="p-0.5">QAID</th>
						<th class="p-0.5">프린트</th>
						<th class="p-0.5">상품명</th>
						<th class="p-0.5">재발행 횟수</th>
						<th class="p-0.5">재발행자</th>
						<th class="p-0.5">재발행 일자</th>
					</tr>
				{/snippet}
				<div class="px-2">
					<TableTop onUpdate={changeSearchParams} {pageSize} total={$qaidStore.pageTotal ?? 0} />

					<table class="table table-pin-rows table-zebra">
						<thead class="uppercase relative">
							{@render tableHeader()}
						</thead>

						<tfoot class="uppercase relative">
							{@render tableHeader()}
						</tfoot>

						<tbody>
							{#if $qaidStore.items}
								{#each $qaidStore.items as item, index}
									<tr class="hover:bg-base-content/10">
										<td class="p-0.5 text-center">
											{getNumberFormat(startNo - index)}
										</td>
										<td class="p-0.5 text-center">
											<span
												class="cursor-pointer"
												onclick={() => getReprintHistory(item.id)}
												role="presentation"
											>
												{item.qaid}
											</span>
										</td>
										<td class="p-0.5 text-center">
											<button
												class="btn btn-ghost btn-xs"
												onclick={async () => {
													await isRePrintFromAdmin(item.qaid, item.id);
													await makeData();
												}}
											>
												<Icon data={faPrint} class="mx-0.5 text-primary-700" />
											</button>
										</td>
										<td class="p-0.5">
											{item.latest_product.name}
										</td>
										<td class="p-0.5 text-center">
											{item.print_count}
										</td>
										<td class="p-0.5 text-center">
											{item.user.name}
										</td>
										<td class="p-0.5 text-center">
											{dateFormat(item.updated_at, 'ko', 'datetime')}
										</td>
									</tr>
								{/each}
							{/if}
						</tbody>
					</table>
				</div>

				<!-- Pagination -->
				{#if $qaidStore.pageTotal && $qaidStore.pageTotal > 0}
					<Paginate store={qaidStore} {localUrl} onPageChange={handlePageChange} {searchParams} />
				{/if}
			</section>

			<dialog class="modal" id="my_modal_1">
				<div class="modal-box w-1/2 max-w-2xl">
					<form method="dialog">
						<button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button>
					</form>

					<h3 class="font-bold text-lg">재발행 이력</h3>

					<table class="table table-xs table-pin-rows table-zebra">
						<thead class="uppercase relative">
							<tr class="bg-base-content text-base-300 text-center">
								<th class="p-0.5">번호</th>
								<th class="p-0.5">출력한 사람</th>
								<th class="p-0.5">내역</th>
								<th class="p-0.5">재발행 일자</th>
							</tr>
						</thead>

						<tbody>
							{#if histories}
								{#each histories as item, index}
									<tr class="hover:bg-base-content/10">
										<td class="p-0.5 text-center">
											{histories.length - index}
										</td>
										<td class="p-0.5 text-center">
											{item.user.name}
										</td>
										<td class="p-0.5">
											{item.memo}
										</td>
										<td class="p-0.5 text-center">
											{dateFormat(item.created_at, 'ko', 'datetime')}
										</td>
									</tr>
								{/each}
							{/if}
						</tbody>
					</table>
				</div>
			</dialog>
		</div>
	{/snippet}
</AppLayout>
