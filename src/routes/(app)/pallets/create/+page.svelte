<script lang="ts">
	import type {
		Breadcrumb,
		RepairGrade,
		RepairPart,
		RepairProcess,
		RepairSymptom,
		SelectedPart
	} from '$lib/types/types';

	import { onMount, tick } from 'svelte';
	import { getUser, type User } from '$lib/User';
	import { page } from '$app/state';

	import { openWebviewWindow } from '$lib/services/windowService';
	import { authClient } from '$lib/services/AxiosBackend';

	import { getProcessGradeName, PROCESS_GRADE_XL } from '$stores/processStore';
	import { getProductStatusName } from '$stores/productStore';
	import { loadedStore, loadItems } from '$stores/loadedStore';

	import {
		addMemo,
		executeMessage,
		getNumberFormat,
		handleCatch,
		preventKoreanInput,
		processBarcode,
		scrollToElement
	} from '$lib/Functions';
	import { AudioEvent, initAudio, playAudio } from '$lib/utils/AudioManager';
	import { errMessages } from '$lib/Messages';
	import { getFromLocalStorage, saveToLocalStorage } from '$lib/utils/StorageManager';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import ScanMessage from '$components/UI/ScanMessage.svelte';
	import ExampleBarcodeCommand from '$components/Snippets/ExampleBarcodeCommand.svelte';
	import MessageModal from '$components/UI/MessageModal.svelte';

	import Icon from 'svelte-awesome';
	import { faRedo } from '@fortawesome/free-solid-svg-icons/faRedo';
	import { faPrint } from '@fortawesome/free-solid-svg-icons/faPrint';
	import { faBarcode } from '@fortawesome/free-solid-svg-icons/faBarcode';
	import { faDolly } from '@fortawesome/free-solid-svg-icons/faDolly';
	import { faBan } from '@fortawesome/free-solid-svg-icons/faBan';
	import { faBoxesPacking } from '@fortawesome/free-solid-svg-icons/faBoxesPacking';
	import { faRocket } from '@fortawesome/free-solid-svg-icons/faRocket';
	import { faPlus } from '@fortawesome/free-solid-svg-icons/faPlus';
	import { faMinus } from '@fortawesome/free-solid-svg-icons/faMinus';
	import { faTrash } from '@fortawesome/free-solid-svg-icons/faTrash';

	const user: User = getUser();
	const apiUrl = '/api/wms/pallets/loaded';
	let isLoading = $state(false); // 로딩중 표시 여부

	// 필수 변수 세팅 시작==================
	let apiSearchParams = '';

	// 작업 중이던 location code
	let workedLocationCode = $state(getFromLocalStorage('worked_location_code'));

	let setLocation = $state(false); // 팔레트 확정
	let palletNumber = $state(page.url.searchParams.get('pallet_number') ?? ''); // 원래는 palletNo
	let locationCountry = $state('KR');
	let locationCity = $state('ESCS');
	let locationPlace = $derived(locationCountry + '-' + locationCity);
	let store = $state('A');
	let line = $state('1');
	let rack = $state('1');
	let level = $state('');
	let column = $state('');
	let locationCode = $state('');

	let prodScanMessage = $state('');
	let palletGradeCode = $state('');
	let palletProdCount = $state(0);
	let palletRecentProducts: any[] = $state([]);

	let checkingBarcode = $state('');
	let checkingProduct: any = $state(null);
	let selectedSymptomCode = $state('');
	let selectedProcessCode = $state('');
	let selectedGradeCode = $state('');
	let osReinstall = $state(false);
	// @todo: 구성품 시스템이 완성 될 때까지 사용 안 함
	// let isAppleProduct = $derived(checkingProduct?.req?.req_type === 2);
	let isAppleProduct = $state(false);
	let reqType = $derived(checkingProduct?.req?.req_type);

	let costType = $state('price');
	let costUnit = $state('won');
	let costRangeOptions = $state([{ id: 0, range: '비용청구기준범위' }]); // 수리비 기준에 따른 범위 옵션
	let costRangeSelected = $state(0);

	let invoice1 = $state(0);
	let invoice2 = $state(0); // 외주용 수리비
	let invoice3 = $state(0);
	let invoice1Readonly: HTMLInputElement | null = $state(null);
	let invoice3Readonly: HTMLInputElement | null = $state(null);
	let invoiceTotalReadonly: HTMLInputElement | null = $state(null);
	let formattedAmount = $state('0');
	let memo = $derived(checkingProduct?.memo);
	let commandVisible = $state(false); // 명령어 바코드
	// 필수 변수 세팅 종료==================

	// OS 재설치 체크박스 표시 조건
	let isOsReinstallAllowed = $derived(!selectedGradeCode.startsWith('ST_XL')); // 수리 등급 조건
	let isComputerCategory = $derived(
		checkingProduct?.cate4.name === '컴퓨터' &&
			['미니PC', '노트북', '브랜드PC', '일체형PC', '조립PC', '컴퓨터'].includes(
				checkingProduct?.cate5.name
			)
	); // 컴퓨터 카테고리 조건
	let isTabletCategory = $derived(
		checkingProduct?.cate4.name === '태블릿PC/액세서리' &&
			checkingProduct?.cate5.name === '태블릿PC'
	); // 태블릿 카테고리 조건

	// 모달창 관련 변수
	let modal: MessageModal;
	let modalType = $state<'error' | 'warning' | 'success'>('error');
	let modalTitle = $state('');
	let modalMessage = $state('');

	// 모달창 표시 함수
	function showModal(
		message: string,
		type: 'error' | 'warning' | 'success' = 'error',
		title: string = '알림'
	) {
		modalType = type;
		modalTitle = title;
		modalMessage = message;

		if (modal) {
			modal.show();
		} else {
			// 대안으로 alert 사용 (임시)
			prodScanMessage = message;
			executeMessage(`${title}: ${message}`);
		}
	}

	// 모달 닫힘 핸들러
	function handleModalClose() {
		setTimeout(() => {
			if (focusCheckingBarcode) {
				focusCheckingBarcode.focus();
			}
		}, 100);
	}

	/**
	 * 페이지 최초 접속시 실행
	 * 적재중인 팔레트 리스트
	 * 기존 loadPalletCode 함수
	 */
	async function makeData() {
		const common_params = {
			level: level,
			column: column
		};

		const api_params = new URLSearchParams({ ...common_params });
		apiSearchParams = api_params.toString(); // api

		await loadItems(`${apiUrl}?${apiSearchParams}`, user);
	}

	let focusCheckingBarcode: HTMLInputElement;
	let isCompleteButtonSuccess = $state(false); // 점검완료 버튼

	async function activeFocus() {
		checkingBarcode = '';

		// DOM 요소가 존재하는지 확인
		if (focusCheckingBarcode) {
			focusCheckingBarcode.value = '';
		}

		await tick();

		// 포커스 설정 전에 다시 확인
		if (focusCheckingBarcode) {
			try {
				focusCheckingBarcode.focus();
				console.log('포커스 설정 성공');

				// 추가 안전장치: requestAnimationFrame으로 한 번 더 시도
				requestAnimationFrame(() => {
					if (focusCheckingBarcode && document.activeElement !== focusCheckingBarcode) {
						focusCheckingBarcode.focus();
						console.log('requestAnimationFrame으로 포커스 재설정');
					}
				});
				scrollToElement('product_box');
			} catch (error) {
				console.error('포커스 설정 실패:', error);
			}
		} else {
			console.error('focusCheckingBarcode가 null입니다');
		}
	}

	function checkView() {
		if (!setLocation || locationCode === '' || locationCode === 'A-1-1--') {
			scrollToElement('location_box');
			isCompleteButtonSuccess = false;
			playAudio(AudioEvent.CHECK_LOCATION_STORE); // 오디오: 적재 창고를 확인해 주시기 바랍니다.
		} else {
			scrollToElement('product_box');
			isCompleteButtonSuccess = true;
			playAudio(AudioEvent.SCAN_BARCODE); // 오디오: 바코드를 스캔해 주시기 바랍니다.
		}

		prodScanMessage = '';
		tmpGradeCode = palletGradeCode;
		activeFocus();
	}

	/**
	 * 작업간 모두 바코드를 이용해 처리한다.
	 * 바코드를 찍으면 실제 이 곳에서 분기하여 각각 처리
	 */
	const handleBarcodeInput = async (event: Event) => {
		event.preventDefault();

		if (!setLocation) {
			showModal('적재할 팔레트를 먼저 확정해 주세요.');
			checkView();
			return;
		}

		await playAudio(AudioEvent.SCAN_COMPLETE); // 오디오: 스캔 완료

		const barcode = checkingBarcode.trim();
		if (barcode === '') {
			prodScanMessage = '바코드를 스캔(입력)해 주시기 바랍니다.';
			await activeFocus();
			return;
		}

		let prefixes = ['check', 'repair', 'grade', 'basistype', 'basisunit', 'fix', 'charge'];

		if (prefixes.some((prefix) => barcode.startsWith(prefix))) {
			selectProcess(barcode);
		} else if (barcode.startsWith('change_pallet')) {
			const pallet_no = barcode.split('/')[1];
			setPalletNo(pallet_no);
		} else if (barcode.startsWith('memo')) {
			const code = barcode.split('/')[1];
			memo = checkingProduct?.repair_product?.memo ?? '';
			memo = addMemo(code, memo);
		} else if (barcode === 'osinstall') {
			osReinstall = true;
			await checkInvoice('OS');
		} else if (barcode === 'calculate' || barcode === 'invoice') {
			await checkInvoice();
		} else if (barcode.startsWith('cancel')) {
			await cancelCheckIn();
		} else if (barcode === 'complete') {
			await completeCheckIn();
		} else {
			checkingBarcode = processBarcode(barcode);
			await getProductInfo();
		}

		await activeFocus();
	};

	let repairSymptomCodes: RepairSymptom[] = $state([]);
	let repairProcessCodes: RepairProcess[] = $state([]);
	let repairGradeCodes: RepairGrade[] = $state([]);

	let defaultRepairProcessId = $state(0);
	let defaultRepairGradeId = $state(0);

	// API에서 가져온 전체 수리 구성품 목록
	let repairParts: RepairPart[] = $state([]);
	let repairPartsCategories: any[] = $state([]);

	// 작업자가 선택한 구성품(parts)의 ID, 임시 저장
	let selectedParts = $state('');
	let selectedPartsCategory = $state('');

	// 서버에서 받아온 원본 구성품 목록 (변경하지 않음)
	let originalPartsList: SelectedPart[] = $state([]);
	// 화면에 표시할 전체 구성품 목록 (서버 데이터 + 새로 추가된 데이터)
	let selectedPartsList: SelectedPart[] = $state([]);
	// API 전송용 통합 구성품 리스트 (추가/수정/삭제 모두 포함)
	let partsChanges: Array<{
		id?: number; // 기존 구성품의 경우 repair_product_parts.id
		parts_id: number; // repair_parts.id
		quantity: number;
		price: number;
		action: 'add' | 'update' | 'delete'; // 작업 유형
	}> = $state([]);

	// 구성품 총 비용
	let totalPartsPrice = $derived(
		selectedPartsList.reduce((total, part) => total + part.price * part.quantity, 0)
	);

	// 수리비 총 비용
	let totalInvoice = $derived(invoice1 + invoice2 + invoice3);

	// API 서버로 넘길 데이터
	let sendSymptomId = $state(0);
	let sendProcessId = $state(0);
	let sendGradeId = $state(0);

	/**
	 * 입력된 바코드가 QAID일 경우 서버에서 상품정보를 가져온다.
	 */
	let tmpGradeCode = $state('');
	async function getProductInfo() {
		try {
			const { status, data } = await authClient.get(`${apiUrl}/check-product/${checkingBarcode}`);
			if (status === 200 && data.success) {
				checkingProduct = data.data.product;
				repairSymptomCodes = data.data.symptoms;
				repairProcessCodes = data.data.processes;
				repairGradeCodes = data.data.grades;
				costType = data.data?.cost_info?.cost_type ?? 'price';
				costUnit = data.data?.cost_info?.cost_unit ?? 'won';
				costRangeOptions = data.data?.cost_info?.cost_range_list;
				costRangeSelected = data.data?.cost_info?.cost_range_selected;

				if (!checkingProduct?.repair_product) {
					showModal(errMessages.no_repair_info, 'error', '수리 정보 없음');
					selectedGradeCode = '';
					checkingBarcode = '';
					checkingProduct = null;
					return;
				}

				// ST_XL로 시작하는 등급 코드는 강제로 ST_XL로 고정
				// 팔레트에는 XL 등급만 표시되도록 함
				tmpGradeCode = checkingProduct?.repair_product.repair_grade.code;
				if (tmpGradeCode && tmpGradeCode.startsWith('ST_XL')) {
					tmpGradeCode = 'ST_XL';
					console.log(
						'ST_XL로 시작하는 등급을 ST_XL로 강제 변경:',
						checkingProduct?.repair_product.repair_grade.code,
						'->',
						tmpGradeCode
					);
				}

				// 팔레트가 처음 생성되어 등급이 없거나, 상품 등급과 일치하는 경우에만 허용
				if (palletGradeCode !== '' && palletGradeCode !== tmpGradeCode) {
					showModal(errMessages.check_pallet_type, 'error', '팔레트 등급 불일치');
					selectedGradeCode = '';
					checkingBarcode = '';
					checkingProduct = null;
					return;
				}

				// 팔레트 등급이 없고 첫 번째 상품인 경우, 상품의 등급으로 팔레트 등급 설정
				if (palletGradeCode === '' && tmpGradeCode) {
					palletGradeCode = checkingProduct.repair_product.repair_grade.code;
					console.log(
						'팔레트 등급이 설정되었습니다: palletGradeCode=',
						palletGradeCode,
						' / tmpGradeCode=',
						tmpGradeCode
					);
				}

				// API 서버로 넘길 데이터
				sendSymptomId = checkingProduct?.repair_product.repair_symptom_id;
				sendProcessId = checkingProduct?.repair_product.repair_process_id;
				sendGradeId = checkingProduct?.repair_product.repair_grade_id;

				selectedSymptomCode = checkingProduct?.repair_product.repair_symptom.code;
				selectedProcessCode = checkingProduct?.repair_product.repair_process.code;
				selectedGradeCode = tmpGradeCode;

				// 서버에서 받아온 구성품 목록 설정
				originalPartsList = checkingProduct.repair_product.repair_product_parts.map((part) => ({
					id: part.id,
					product_id: part.repair_product_id,
					parts_id: part.repair_parts_id,
					name: part.repair_part?.name,
					price: part.price || 0,
					quantity: part.quantity || 1
				}));

				selectedPartsList = [...originalPartsList];

				// 구성품 변경사항 초기화 (기존 구성품들은 현재 상태로 설정)
				partsChanges = checkingProduct.repair_product.repair_product_parts.map((part) => ({
					id: part.id, // repair_product_parts.id
					parts_id: part.repair_parts_id, // repair_parts.id
					quantity: part.quantity || 1,
					price: part.price || 0,
					action: 'update' as const // 기존 구성품은 업데이트로 처리
				}));

				invoice1 = checkingProduct?.repair_product.invoice1 ?? 0;
				invoice2 = checkingProduct?.repair_product.invoice2 ?? 0;
				invoice3 = checkingProduct?.repair_product.invoice3 ?? 0;
				osReinstall = checkingProduct?.repair_product.is_os_install === 1;

				formattedAmount = getNumberFormat(checkingProduct?.amount);
				memo = checkingProduct?.repair_product?.memo ?? '';

				isCompleteButtonSuccess = true;
			} else {
				console.log('상품 정보 조회 실패:', data);
				checkingProduct = null;
				checkView();

				showModal(data.data.message, 'error', '상품 정보 오류');
			}
		} catch (e: any) {
			checkingProduct = null;

			const message = (await handleCatch(e, true)) as string;
			showModal(message, 'error', '상품 정보 오류');
		}
	}

	// 선택된 수리 등급에 해당하는 증상 내용을 가져온다.
	async function getRepairSymptoms() {
		try {
			selectedSymptomCode = ''; // 선택된 수리 등급이 바뀌면 증상 내용도 초기화
			selectedProcessCode = ''; // 증상 내용도 초기화되므로 처리 내용도 초기화

			const grade = repairGradeCodes.find((grade) => grade.code === selectedGradeCode);
			if (!grade) {
				repairSymptomCodes = [];
				return;
			}

			const { status, data } = await authClient.get(
				`/api/wms/repairs/code/symptoms-processes/grade/${grade.id}/${reqType}`
			);
			if (status === 200 && data.success) {
				repairSymptomCodes = data.data.symptoms;
				repairProcessCodes = data.data.processes;

				// 증상이 1개만 있다면 자동으로 선택
				if (repairSymptomCodes.length === 1) {
					selectedSymptomCode = repairSymptomCodes[0].code;
					sendSymptomId = repairSymptomCodes[0].id;

					// 처리 내용도 1개만 있다면 자동으로 선택
					if (repairProcessCodes.length === 1) {
						selectedProcessCode = repairProcessCodes[0].code;
						sendProcessId = repairProcessCodes[0].id;
					}
				}
			}
		} catch (e: any) {
			repairSymptomCodes = [];

			const message = (await handleCatch(e, true)) as string;
			showModal(message, 'error', '증상 내용 오류');
		}
	}

	// 선택된 증상에 해당하는 처리 내용을 가져온다.
	async function getRepairProcesses() {
		try {
			selectedProcessCode = ''; // 선택된 증상이 바뀌면 처리 내용도 초기화

			const symptom = repairSymptomCodes.find((symptom) => symptom.code === selectedSymptomCode);
			const grade = repairGradeCodes.find((grade) => grade.code === selectedGradeCode);
			// defaultRepairProcessId = symptom?.default_repair_process_id || 0;
			// defaultRepairGradeId = symptom?.default_repair_grade_id || 0;

			const { status, data } = await authClient.get(
				`/api/wms/repairs/code/processes/symptom/${symptom?.id}/${grade?.id}`
			);
			if (status === 200 && data.success) {
				repairProcessCodes = data.data.processes;

				if (repairProcessCodes.length === 1) {
					selectedProcessCode = repairProcessCodes[0].code;
					// API 서버로 넘길 데이터
					sendProcessId = repairProcessCodes[0].id;
				}
				// if (selectedProcessCode) {
				// 	await getRepairGrades();
				// }
			}
		} catch (e: any) {
			const message = (await handleCatch(e, true)) as string;
			showModal(message, 'error', '처리 내용 오류');
		}
	}

	// 선택된 처리 내용에 해당하는 수리 등급을 가져온다.
	async function getRepairGrades() {
		try {
			// Apple 제품이거나 가공불가 처리 시 모든 구성품 삭제
			if (isAppleProduct || selectedProcessCode === 'RP_ABANDON') {
				selectedPartsList = [];
				// 기존 구성품이 있다면 모두 삭제 상태로 변경
				if (originalPartsList.length > 0) {
					partsChanges = originalPartsList.map((p) => ({
						id: p.id,
						parts_id: p.parts_id,
						quantity: 0,
						price: p.price,
						action: 'delete' as const
					}));
				} else {
					partsChanges = [];
				}
			}

			const process_id = repairProcessCodes.find(
				(process) => process.code === selectedProcessCode
			)?.id;
			const { status, data } = await authClient.get(
				`/api/wms/repairs/code/grade/process/${process_id}`
			);
			if (status === 200 && data.success) {
				repairGradeCodes = data.data.grades;

				// 기본 등급이 등록되어 있을 경우
				selectedGradeCode =
					repairGradeCodes.find((grade) => grade.id === defaultRepairGradeId)?.code || '';
			}
		} catch (e: any) {
			const message = (await handleCatch(e, true)) as string;
			showModal(message, 'error', '수리 등급 오류');
		}
	}

	async function getPartsCategories() {
		try {
			const { status, data } = await authClient.get(`/api/wms/repairs/parts-categories`);
			if (status === 200 && data.success) {
				repairPartsCategories = data.data.categories;
			}
		} catch (e) {
			const message = (await handleCatch(e, true)) as string;
			showModal(message, 'error', '구성품 카테고리 오류');
		}
	}

	async function getParts() {
		if (selectedPartsCategory === '') {
			return;
		}

		try {
			const { status, data } = await authClient.get(`/api/wms/repairs/parts/${selectedPartsCategory}`);
			if (status === 200 && data.success) {
				repairParts = data.data.parts;
			}
		} catch (e) {
			const message = (await handleCatch(e, true)) as string;
			showModal(message, 'error', '구성품 정보 오류');
		}
	}

	// 구성품 추가
	function handleAddParts() {
		if (selectedParts === '') {
			showModal('구성품을 선택해 주세요.', 'warning', '구성품 오류');
			return;
		}

		// 선택된 구성품 정보 찾기
		const selectedPartId = Number(selectedParts);
		const selectedPart = repairParts.find((p) => p.id === Number(selectedPartId));

		if (!selectedPart) {
			showModal('선택된 구성품이 없습니다.', 'warning', '구성품 오류');
			return;
		}

		if (selectedPart.is_purchasable === 'N') {
			showModal(errMessages.not_purchasable, 'error', '구성품 구매 불가');

			// 가공불가로 처리
			selectedProcessCode = 'RP_ABANDON';
			getRepairGrades();

			// 변수 초기화
			selectedParts = '';
			selectedPartsCategory = '';
			repairParts = [];
			return;
		}

		// 이미 존재하는 구성품인지 확인 (parts_id 기준)
		const existingPartIndex = selectedPartsList.findIndex(
			(p) => Number(p.parts_id) === selectedPartId
		);

		if (existingPartIndex !== -1) {
			// 기존 구성품의 수량 증가
			selectedPartsList[existingPartIndex].quantity += 1;

			// 변경사항 업데이트
			const existingChangeIndex = partsChanges.findIndex(
				(p) => Number(p.parts_id) === selectedPartId
			);
			if (existingChangeIndex !== -1) {
				partsChanges[existingChangeIndex].quantity += 1;
			}

			// 반응성을 위해 배열 재할당
			selectedPartsList = [...selectedPartsList];
			partsChanges = [...partsChanges];

			console.log(`${selectedPart.name}의 수량이 증가되었습니다.`);
		} else {
			// 새로운 구성품 추가
			const newPartId = Date.now(); // 임시 ID (화면 표시용)

			selectedPartsList = [
				...selectedPartsList,
				{
					id: newPartId,
					product_id: checkingProduct.id,
					parts_id: selectedPartId,
					name: selectedPart.name,
					price: selectedPart.price,
					quantity: 1
				}
			];

			partsChanges = [
				...partsChanges,
				{
					parts_id: selectedPartId,
					quantity: 1,
					price: selectedPart.price,
					action: 'add'
				}
			];

			console.log(`${selectedPart.name}이 추가되었습니다.`);
		}
		console.log('partsChanges: ', partsChanges);

		// 선택 초기화
		selectedParts = '';
		selectedPartsCategory = '';
		repairParts = [];
	}

	// 구성품 수량 증가 함수
	function handleIncreaseQuantity(partId: number) {
		const partIndex = selectedPartsList.findIndex((p) => p.id === Number(partId));
		if (partIndex === -1) return;

		const part = selectedPartsList[partIndex];

		// 화면 표시용 수량 증가
		selectedPartsList[partIndex].quantity += 1;

		// 변경사항 업데이트 (parts_id 기준으로 찾기)
		const changeIndex = partsChanges.findIndex((p) => Number(p.parts_id) === Number(part.parts_id));
		if (changeIndex !== -1) {
			partsChanges[changeIndex].quantity += 1;
		}

		// 반응성을 위해 배열 재할당
		selectedPartsList = [...selectedPartsList];
		partsChanges = [...partsChanges];

		console.log(`${part.name}의 수량이 증가되었습니다.`);
		console.log('partsChanges: ', partsChanges);
	}

	// 구성품 삭제 함수 추가
	function handleRemoveParts(partId: number) {
		const partIndex = selectedPartsList.findIndex((p) => p.id === partId);
		if (partIndex === -1) return;

		const part = selectedPartsList[partIndex];

		if (part.quantity > 1) {
			// 수량이 1보다 크면 수량만 감소
			selectedPartsList[partIndex].quantity -= 1;

			// 변경사항 업데이트 (parts_id 기준으로 찾기)
			const changeIndex = partsChanges.findIndex(
				(p) => Number(p.parts_id) === Number(part.parts_id)
			);
			if (changeIndex !== -1) {
				partsChanges[changeIndex].quantity -= 1;
			}

			// 반응성을 위해 배열 재할당
			selectedPartsList = [...selectedPartsList];
			partsChanges = [...partsChanges];

			console.log(`${part.name}의 수량이 감소되었습니다.`);
		} else {
			// 수량이 1이면 완전히 제거
			const isOriginalPart = originalPartsList.some((p) => p.parts_id === part.parts_id);

			if (isOriginalPart) {
				// 기존 구성품인 경우 삭제 표시
				const changeIndex = partsChanges.findIndex(
					(p) => Number(p.parts_id) === Number(part.parts_id)
				);
				if (changeIndex !== -1) {
					partsChanges[changeIndex].action = 'delete';
					partsChanges[changeIndex].quantity = 0;
				}
			} else {
				// 새로 추가된 구성품인 경우 변경사항에서 제거
				partsChanges = partsChanges.filter((p) => Number(p.parts_id) !== Number(part.parts_id));
			}

			// 화면에서 제거
			selectedPartsList = selectedPartsList.filter((p) => p.id !== partId);

			console.log(`${part.name}이 삭제되었습니다.`);
		}

		console.log('partsChanges: ', partsChanges);
	}

	// 새로운 팔레트 번호를 생성
	async function reloadLocationCode() {
		try {
			const { status, data } = await authClient.get(
				`/api/wms/locations/generate/code/${locationPlace}`
			);
			if (status === 200 && data.success) {
				level = data.data.level;
				column = data.data.column;

				palletNumber = `${level}-${column}`;
				locationCode = `A-1-1-${palletNumber}`;

				const new_item = {
					pallet_no: palletNumber,
					isLocation: false,
					palletGradeCode: '', // 새로운 팔레트는 등급이 없음
					palletProdCount: 0,
					palletRecentProducts: []
				};
				loadedStore.update((currentData: any) => {
					return { ...currentData, items: [...currentData.items, new_item] };
				});

				setLocation = false;
				checkingProduct = null;
				invoice3 = 0;
				invoice2 = 0;
				invoice1 = 0;
				selectedGradeCode = '';
				selectedProcessCode = '';
				selectedSymptomCode = '';
				palletGradeCode = ''; // 새로운 팔레트 생성 시 등급 초기화

				checkView();
			} else {
				showModal('팔레트 번호 생성 실패', 'error', '오류: 팔레트 번호 생성 오류');
			}
		} catch (e: any) {
			const message = (await handleCatch(e, true)) as string;
			showModal(message, 'error', '오류: 팔레트 번호 생성 오류');
		}
	}

	function setPalletNo(pallet_no: string) {
		if (pallet_no !== '' && pallet_no !== '-') {
			palletNumber = pallet_no;

			const pallet_no_arr = pallet_no.split('-');
			level = pallet_no_arr[0];
			column = pallet_no_arr[1];

			setLocationCode();
		} else {
			level = '';
			column = '';
			palletNumber = '';
			locationCode = '';
			checkingProduct = null;
			invoice3 = 0;
			invoice2 = 0;
			invoice1 = 0;
			selectedGradeCode = '';
			selectedProcessCode = '';
			selectedSymptomCode = '';
			palletGradeCode = ''; // 팔레트 선택 해제 시 등급 초기화
		}
	}

	// 팔레트 확정
	async function setLocationCode() {
		if (!palletNumber) {
			showModal(
				'확정할 팔레트가 없습니다.<br>팔레트를 선택해 주세요.',
				'warning',
				'확인 필요: 팔레트 선택'
			);
			return;
		}

		checkingProduct = null;
		invoice3 = 0;
		invoice2 = 0;
		invoice1 = 0;

		if (
			locationCountry !== '' &&
			locationCity !== '' &&
			store !== '' &&
			line !== '' &&
			rack !== '' &&
			level !== '' &&
			column !== ''
		) {
			if (!locationCode || locationCode === 'A-1-1--') {
				locationCode = store + '-' + line + '-' + rack + '-' + level + '-' + column;
			}

			try {
				const { status, data } = await authClient.get(
					`${apiUrl}/set/${locationPlace}/${locationCode}`
				);

				if (status === 200 && data.success) {
					const item = data.data;

					// 팔레트 등급 초기화 로직 개선
					if (item.palletGradeCode !== '' && typeof item.palletGradeCode !== 'undefined') {
						// 기존 팔레트에 등급이 있는 경우
						selectedGradeCode = item.palletGradeCode; // 팔레트 상품의 grade 코드
						palletGradeCode = item.palletGradeCode; // 팔레트의 grade 코드

						if (palletGradeCode === PROCESS_GRADE_XL) {
							selectProcess('grade/' + selectedGradeCode);
						} else {
							selectedProcessCode = '';
							selectedSymptomCode = '';
						}
					} else {
						// 새로운 팔레트인 경우 등급을 빈 값으로 초기화
						palletGradeCode = '';
						selectedGradeCode = '';
						selectedProcessCode = '';
						selectedSymptomCode = '';
						console.log('새로운 팔레트가 생성되었습니다. 등급은 첫 번째 상품 적재 시 설정됩니다.');
					}

					saveToLocalStorage('worked_location_code', locationCode, (value) => {
						workedLocationCode = value;
					});
					setLocation = true;
					palletProdCount = item.palletProdCount;
					palletRecentProducts = item.palletRecentProducts.reverse();
					checkView();
				} else {
					showModal(data.message, 'error', '팔레트 확정 실패');
				}
			} catch (e: any) {
				const message = (await handleCatch(e, true)) as string;
				showModal(message, 'error', '팔레트 확정 실패');
			}
		}
	}

	function printPalletNumber() {
		const grade_name = getProcessGradeName(palletGradeCode);
		const url = `/print/label?level=${level}&column=${column}&grade_name=${grade_name}`;
		openWebviewWindow(url, 'Print');
	}

	function handleError(message: string) {
		prodScanMessage = message;
		invoice3 = invoice2 = invoice1 = 0;

		checkView();
		return true; // 에러 발생 시 true 반환
	}

	const processHandlers = {
		change_pallet: (process_code: string) => {
			setPalletNo(process_code);
		},

		check: (process_code: string) => {
			if (!checkingProduct || typeof checkingProduct.id === 'undefined') {
				if (handleError(errMessages.check_product)) {
					checkingProduct = null;
					selectedGradeCode = selectedProcessCode = selectedSymptomCode = '';
					return;
				}
			}
			selectedSymptomCode = process_code;
			checkingBarcode = '';
			checkInvoice();
		},

		repair: (process_code: string) => {
			if (!checkingProduct || typeof checkingProduct.id === 'undefined') {
				if (handleError(errMessages.check_product)) {
					selectedGradeCode = selectedProcessCode = selectedSymptomCode = '';
					return;
				}
			}

			if (!selectedSymptomCode && handleError(errMessages.check_symptom)) {
				selectedGradeCode = selectedProcessCode = '';
				return;
			}
			selectedProcessCode = process_code;
			checkingBarcode = '';
			checkInvoice();
		},

		grade: (process_code: string) => {
			// 팔레트 등급 초기화 로직 개선
			if (palletGradeCode !== '' && palletGradeCode !== process_code && process_code !== '') {
				// 기존 팔레트에 등급이 있고, 새로운 등급과 다른 경우
				if (handleError(errMessages.check_pallet_type2)) {
					selectedGradeCode = '';
					return;
				}
			} else {
				// 상품 정보가 없는 경우
				if (!checkingProduct || typeof checkingProduct.id === 'undefined') {
					if (handleError(errMessages.check_product)) {
						selectedGradeCode = selectedProcessCode = selectedSymptomCode = '';
						return;
					}
				}

				// 선택된 증상이 없는 경우
				if (!selectedSymptomCode && handleError(errMessages.check_symptom)) {
					selectedGradeCode = selectedProcessCode = '';
					return;
				}

				// 선택된 처리 내용이 없는 경우
				if (!selectedProcessCode && handleError(errMessages.check_process)) {
					selectedGradeCode = '';
					return;
				}

				selectedGradeCode = process_code;

				// 팔레트의 첫 번째 상품(팔레트 등급이 없음)인 경우, 현재 상품의 등급으로 팔레트 등급 설정
				if (palletGradeCode === '' && process_code !== '') {
					palletGradeCode = process_code;
					console.log('팔레트 등급이 바코드 스캔으로 설정되었습니다:', palletGradeCode);
				}
			}
		},

		basistype: (process_code: string) => {
			if (!checkingProduct || typeof checkingProduct.id === 'undefined') {
				if (handleError(errMessages.check_product)) {
					selectedGradeCode = selectedProcessCode = selectedSymptomCode = '';
					return;
				}
			}

			if (!selectedSymptomCode && handleError(errMessages.check_symptom)) {
				selectedGradeCode = selectedProcessCode = '';
				return;
			}

			if (!selectedProcessCode && handleError(errMessages.check_process)) {
				selectedGradeCode = '';
				return;
			}

			if (!selectedGradeCode && handleError(errMessages.check_grade)) return;

			invoice3 = invoice2 = invoice1 = 0;
			costType = process_code;
		},

		// 다른 프로세스 타입에 대한 핸들러도 유사한 방식으로 추가
		basisunit: (process_code: string) => {
			if (!checkingProduct || typeof checkingProduct.id === 'undefined') {
				if (handleError(errMessages.check_product)) {
					selectedGradeCode = selectedProcessCode = selectedSymptomCode = '';
					return;
				}
			}

			if (!selectedSymptomCode && handleError(errMessages.check_symptom)) {
				selectedGradeCode = selectedProcessCode = '';
				return;
			}

			if (!selectedProcessCode && handleError(errMessages.check_process)) {
				selectedGradeCode = '';
				return;
			}

			if (!selectedGradeCode && handleError(errMessages.check_grade)) return;
			if (!costType && handleError(errMessages.check_cost_type)) return;

			invoice3 = invoice2 = invoice1 = 0;
			costUnit = process_code;
		},

		fix: (process_code: string) => {
			if (!checkingProduct || typeof checkingProduct.id === 'undefined') {
				if (handleError(errMessages.check_product)) {
					selectedGradeCode = selectedProcessCode = selectedSymptomCode = '';
					return;
				}
			}

			if (!selectedSymptomCode && handleError(errMessages.check_symptom)) {
				selectedGradeCode = selectedProcessCode = '';
				return;
			}

			if (!selectedProcessCode && handleError(errMessages.check_process)) {
				selectedGradeCode = '';
				return;
			}

			if (!selectedGradeCode && handleError(errMessages.check_grade)) return;
			if (!costType && handleError(errMessages.check_cost_type)) return;

			addInvoiceMemo('fix', process_code);
		},

		charge: (process_code: string) => {
			if (!checkingProduct || typeof checkingProduct.id === 'undefined') {
				if (handleError(errMessages.check_product)) {
					selectedGradeCode = selectedProcessCode = selectedSymptomCode = '';
					return;
				}
			}

			if (!selectedSymptomCode && handleError(errMessages.check_symptom)) {
				selectedGradeCode = selectedProcessCode = '';
				return;
			}

			if (!selectedProcessCode && handleError(errMessages.check_process)) {
				selectedGradeCode = '';
				return;
			}

			if (!selectedGradeCode && handleError(errMessages.check_grade)) return;
			if (!costType && handleError(errMessages.check_cost_type)) return;

			addInvoiceMemo('charge', process_code);
		}
	};

	function selectProcess(process_command: string) {
		prodScanMessage = '';

		const [process_type, process_code] = process_command.split('/');
		const handler = processHandlers[process_type as keyof typeof processHandlers];
		if (handler) {
			handler(process_code);
		} else {
			console.error('Unknown process type:', process_type);
		}
	}

	/**
	 * 바코드로 추가 수리비를 등록
	 * EX) fix/{수리코드}@{금액}, charge/{구성품코드}@{금액}
	 *
	 * @param process_type
	 * @param charge_fee
	 */
	async function addInvoiceMemo(process_type: string, charge_fee: string) {
		const [process_code, amount] = charge_fee.split('@');

		try {
			const { status, data } = await authClient.get(
				`${apiUrl}/other-expenses/${process_type}/${process_code}`
			);

			if (status === 200 && data.success) {
				// 별도 수리비
				if (process_type === 'fix') {
					invoice2 += parseInt(amount, 10);
				}

				// 구성품
				if (process_type === 'charge') {
					invoice3 += parseInt(amount, 10);
				}

				const process_name = data.data.name ?? process_code.substring(3);
				memo += `${process_name} : ${amount} 원\n`;
			} else {
				showModal(data.data.message, 'error', '메모 적용 오류');
			}
		} catch (e: any) {
			const message = (await handleCatch(e, true)) as string;
			showModal(message, 'error', '메모 적용 오류');
		}
	}

	let repair_cost_id = $state(null);
	let calculation_basis = $state(null);
	let calculation_details = $state(null);

	/**
	 * grade/ST_REFURB(INV), osinstall(OS) 같은 바코드가 들어올 때 서버에서 수리비 계산
	 *
	 * @param invoice_type  INV, OS
	 */
	async function checkInvoice(invoice_type: string = 'INV') {
		if (invoice_type === 'OS') {
			if (!(isOsReinstallAllowed && (isComputerCategory || isComputerCategory))) {
				showModal('OS 설치비를 계산할 수 없는 상품 입니다.', 'error', 'sss');
			}
		}

		const product_id = checkingProduct?.id;

		const filter = [product_id, selectedGradeCode, costType, costUnit];
		if (filter.every((value) => value !== undefined && value !== '')) {
			try {
				let os_install = osReinstall ? 'true' : 'false'; // os 재설치

				if (selectedGradeCode.startsWith('ST_XL')) {
					costType = 'common';
					costUnit = 'won';
					invoice1 = invoice2 = invoice3 = 0;
					costRangeOptions = [];
					return;
				}

				const { status, data } = await authClient.get(
					`${apiUrl}/check-invoice/${product_id}/${selectedProcessCode}/${os_install}`
				);

				if (status === 200 && data.success) {
					const result = data.data;

					if (result.isAlreadyChecked === false) {
						costType = result?.cost_info?.cost_type;
						costUnit = result?.cost_info?.cost_unit;
						costRangeOptions = result?.cost_info?.cost_range_list;
						costRangeSelected = result?.cost_info?.cost_range_selected;
						invoice1 = result.invoice1;
						invoice3 = result.invoice3;
						repair_cost_id = result?.cost_info?.cost_id; // 수리비 인덱스
						calculation_basis = result?.cost_info?.cost_basis; // 수리비 계산 기준
						calculation_details = result?.cost_info?.cost_details; // 수리비 계산 상세(JSON)

						if (invoice3 > 0) {
							memo += 'OS설치 : ' + invoice3 + ' 원\n';
							osReinstall = true;
						}

						if (invoice1 > 0 && invoice1Readonly) {
							invoice1Readonly.readOnly = false; // 또는 false
						}
					}
				} else {
					showModal(data.data.message, 'error', '비용 조회 오류');
				}
			} catch (e: any) {
				const message = (await handleCatch(e, true)) as string;
				showModal(message, 'error', '비용 조회 오류');
			}
		}
		checkView();
	}

	async function completeCheckIn() {
		const product_id = checkingProduct?.id;
		const qaid = checkingProduct?.qaid;
		const repair_product_id = checkingProduct?.repair_product?.id;

		// product_id는 number 타입이므로 null 또는 undefined 체크만 수행
		if (
			product_id != null && // product_id가 null 또는 undefined가 아닌지 확인
			sendSymptomId !== 0 &&
			sendProcessId !== 0 &&
			sendGradeId !== 0 &&
			locationCountry !== '' &&
			locationCity !== ''
		) {
			// 서버 전송용 데이터 분리
			const addParts = partsChanges
				.filter((p) => p.action === 'add')
				.map((p) => ({
					parts_id: p.parts_id,
					quantity: p.quantity,
					price: p.price
				}));

			const updateParts = partsChanges
				.filter((p) => p.action === 'update')
				.map((p) => ({
					id: p.id, // repair_product_parts.id
					parts_id: p.parts_id,
					quantity: p.quantity,
					price: p.price
				}));

			const removeParts = partsChanges
				.filter((p) => p.action === 'delete')
				.map((p) => p.id)
				.filter((id) => id !== undefined);

			let payload = {
				location_place: locationPlace,
				location_code: locationCode,
				product_id: product_id,
				qaid: qaid,
				repair_product_id,
				symptom_id: sendSymptomId,
				process_id: sendProcessId,
				grade_id: sendGradeId,
				os_reinstall: osReinstall,
				add_parts: addParts,
				update_parts: updateParts,
				remove_parts: removeParts,
				invoice1: invoice1,
				invoice2: invoice2,
				invoice3: invoice3,
				repair_cost_id: repair_cost_id,
				calculation_basis: calculation_basis,
				// JSON 객체를 문자열로 변환하여 서버로 전송
				calculation_details: calculation_details || null,
				memo: memo,
				pallet_grade_code: palletGradeCode // 팔레트 등급 정보 추가
			};

			await playAudio(AudioEvent.COMPLETING_REPAIR); // 오디오: 검수완료 합니다.

			try {
				const { status, data } = await authClient.post(`${apiUrl}/save-on-pallet`, payload);
				if (status === 200 && data.success) {
					await playAudio(AudioEvent.REGISTER_COMPLETE); // 오디오: 등록 완료
					// 작업중인 로케이션을 저장해 둔다.
					saveToLocalStorage('worked_location_code', locationCode, (value) => {
						workedLocationCode = value;
					});
					await setLocationCode();

					// 팔레트를 계속 유지하기 위해
					const pallet_no = palletNumber;
					clearValues();

					palletNumber = pallet_no;
					level = pallet_no.split('-')[0];
					column = pallet_no.split('-')[1];

					await makeData();

					// 성공 시 구성품 초기화
					originalPartsList = [];
					selectedPartsList = [];
					partsChanges = [];
				} else {
					await playAudio(AudioEvent.FAIL_AND_RETRY); // 오디오: 실패하였습니다. 다시 진행해 주시기 바랍니다.
					showModal(data.data.message, 'error', '팔레트 적재 실패');
				}
			} catch (error) {
				await playAudio(AudioEvent.FAIL_AND_RETRY); // 오디오: 실패하였습니다. 다시 진행해 주시기 바랍니다.
			} finally {
				isCompleteButtonSuccess = false;
			}
		} else {
			checkView();
			return;
		}
	}

	async function cancelCheckIn() {
		clearValues();
	}

	function clearValues() {
		level = '';
		column = '';
		palletNumber = '';
		locationCode = '';
		checkingBarcode = '';
		checkingProduct = null;
		invoice3 = 0;
		invoice2 = 0;
		invoice1 = 0;
		formattedAmount = '0';
		selectedGradeCode = '';
		selectedProcessCode = '';
		selectedSymptomCode = '';
		memo = '';
		prodScanMessage = '';
		osReinstall = false;
		originalPartsList = [];
		selectedPartsList = [];
		partsChanges = [];
		isCompleteButtonSuccess = false;

		// 작업 위치 코드도 정리 (선택적)
		// clearWorkedLocationCode(); // 필요시 주석 해제
	}

	function handleClick(event: MouseEvent) {
		const target = event.target as HTMLElement;
		let target_name = target?.getAttribute('name');
		let target_id = target?.getAttribute('id');

		if (
			(typeof target_name != 'undefined' && target_name === 'checking_barcode') ||
			[
				'complete_btn',
				'cancel_btn',
				'rp_bid_id',
				'level',
				'column',
				'pallet_no',
				'symptom_code',
				'process_code',
				'grade_code',
				'parts',
				'parts_category',
				'button_add_parts',
				'cost_type',
				'cost_unit',
				'cost_range',
				'invoice1',
				'invoice2',
				'invoice3',
				'total_invoice',
				'memo',
				'button_barcode_box',
				'load_lc_code_btn',
				'set_lc_cd_btn',
				'command_barcode_title',
				'checking_barcode_button'
			].includes(target_id)
		) {
			const path = event.composedPath();
			const matching_element = path.find((el) => {
				el.matches &&
					el.matches('input, div, select, table, tr, td, span, body, image, textarea, button');
			});
			if (matching_element) matching_element.focus();
		} else {
			checkView();
		}
	}

	// 팔레트 상세 정보 업데이트 공통 함수
	function updatePalletDetails(pallet_number: string) {
		if (pallet_number && pallet_number.includes('-')) {
			const split_code = pallet_number.split('-');
			level = split_code[0];
			column = split_code[1];
			locationCode = `A-1-1-${level}-${column}`;
		}
	}

	// 팔레트 변경 처리 함수
	async function handleChangePallet() {
		try {
			// store 구독하여 새로운 값 확인
			loadedStore.subscribe((value) => {
				if (value && value.items && Array.isArray(value.items)) {
					const palletDetails = value.items.find((item) => item.pallet_no === palletNumber);

					if (palletDetails) {
						updatePalletDetails(palletNumber);

						// updatePalletDetails는 이미 위에서 호출했으므로 제거
						palletGradeCode = palletDetails.palletGradeCode;
						palletProdCount = palletDetails.palletProdCount;
						palletRecentProducts = palletDetails.palletRecentProducts.reverse();

						checkView();
					}
				}
			});

			// 상태 초기화
			setLocation = false;
			selectedGradeCode = '';
			costRangeSelected = 0;
			invoice1 = 0;
			invoice2 = 0;
			invoice3 = 0;
		} catch (error) {
			console.error('Failed to handle pallet change:', error);
		}
	}

	$effect(() => {
		if (palletNumber && palletNumber.includes('-') && !setLocation) {
			// URL 파라미터로 넘어온 팔레트 번호인지 확인
			const urlPalletNumber = page.url.searchParams.get('pallet_number');
			if (urlPalletNumber && urlPalletNumber === palletNumber) {
				console.log('URL 파라미터로 넘어온 팔레트 번호를 자동으로 확정합니다:', palletNumber);
				// 비동기로 팔레트 확정 실행
				setTimeout(() => {
					setLocationCode();
				}, 100);
			}
		}

		// 팔레트 코드 파싱
		if (palletNumber && palletNumber.includes('-')) {
			const splitCode = palletNumber.split('-');
			level = splitCode[0];
			column = splitCode[1];
		} else if (level === '' && workedLocationCode !== '' && workedLocationCode !== 'A-1-1-') {
			// 저장된 작업 위치에서 팔레트 정보 복원
			const splitCode = workedLocationCode.split('-');
			if (splitCode.length >= 5) {
				level = splitCode[3];
				column = splitCode[4];
				palletNumber = `${level}-${column}`;
				console.log('저장된 작업 위치에서 팔레트 정보를 복원했습니다:', palletNumber);
			}
		}

		// 위치 코드 업데이트
		locationCode = `${store}-${line}-${rack}-${level}-${column}`;

		// 구성품 총 비용
		invoice2 = totalPartsPrice;

		// 한글 입력 방지
		preventKoreanInput(checkingBarcode, (newValue) => {
			checkingBarcode = newValue;
		});
	});

	onMount(async () => {
		// 오디오 시스템 초기화
		initAudio({
			enabled: true,
			volume: 1.0,
			preloadAll: true
		});

		loadedStore.set({ items: [] });
		await makeData();

		if (palletNumber) {
			await tick();

			loadedStore.subscribe((value: any) => {
				if (value && value.items && Array.isArray(value.items)) {
					const pallet_details = value.items.find((item: any) => item.pallet_no === palletNumber);

					if (pallet_details) {
						updatePalletDetails(palletNumber);

						palletGradeCode = pallet_details.palletGradeCode;
						palletProdCount = pallet_details.palletProdCount;
						palletRecentProducts = pallet_details.palletRecentProducts.reverse();
					}
				}
			});
		}

		await getPartsCategories();
		checkView();

		if (invoice1Readonly) {
			invoice1Readonly.readOnly = true;
		}

		if (invoice3Readonly) {
			invoice3Readonly.readOnly = true;
		}

		if (invoiceTotalReadonly) {
			invoiceTotalReadonly.readOnly = true;
		}
	});

	const breadcrumbs: Breadcrumb[] = [
		{ title: '출고', url: '/pallets/list' },
		{ title: '출고 팔레트 목록', url: '/pallets/list' },
		{ title: '출고상품 적재', url: '/pallets/create' }
	];
</script>

<svelte:head>
	<title>출고상품 적재</title>
</svelte:head>

<AppLayout {user}>
	{#snippet main()}
		<div>
			<TitleBar {breadcrumbs} />

			<section class="main-section">
				<div
					class="w-full px-3 flex flex-row justify-start"
					onclick={handleClick}
					role="presentation"
				>
					<div class="w-[800px] flex flex-col">
						<div class="w-full">
							<!-- 점검완료 후 적재위치 -->
							<div class="scan-container" id="location_box">
								<div class="scan-header">
									<div class="flex items-center gap-3">
										<Icon data={faDolly} scale={1.5} />
										<span class="text-xl font-bold">점검완료 후 적재위치</span>
									</div>
								</div>
								<div class="scan-content">
									<div class="flex flex-col lg:flex-row gap-4">
										<div class="w-40 space-y-2">
											<div class="text-sm font-semibold text-gray-700 dark:text-gray-300">
												창고 지역
											</div>
											<div
												class="flex items-center h-10 px-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600"
											>
												<input bind:value={locationCountry} name="location_country" type="hidden" />
												<input bind:value={locationCity} name="location_city" type="hidden" />
												<span class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate"
													>코너스톤 C동</span
												>
											</div>
										</div>

										<div class="w-48 space-y-2">
											<div class="text-sm font-semibold text-gray-700 dark:text-gray-300">
												존-층(단)-칸
											</div>
											<div class="flex items-center gap-1">
												<input
													bind:value={store}
													class="w-10 h-10 text-center text-lg font-bold bg-gray-700 text-gray-300 border border-gray-600 rounded-lg"
													id="store"
													name="store"
													readonly
													type="text"
												/>
												<span class="text-lg font-bold text-gray-700 dark:text-gray-300">-</span>
												<input
													bind:value={line}
													class="w-10 h-10 text-center text-lg font-bold bg-gray-700 text-gray-300 border border-gray-600 rounded-lg"
													id="line"
													name="line"
													readonly
													type="text"
												/>
												<span class="text-lg font-bold text-gray-700 dark:text-gray-300">-</span>
												<input
													bind:value={rack}
													class="w-10 h-10 text-center text-lg font-bold bg-gray-700 text-gray-300 border border-gray-600 rounded-lg"
													id="rack"
													name="rack"
													readonly
													type="text"
												/>
											</div>
										</div>

										<div class="flex-1 space-y-2">
											<div class="text-sm font-semibold text-gray-700 dark:text-gray-300">
												팔레트 번호
											</div>
											<div class="flex items-center gap-2">
												<select
													bind:value={palletNumber}
													class="flex-1 px-3 py-2 text-sm font-medium bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg focus:border-blue-500 dark:focus:border-blue-400 focus:outline-none"
													id="pallet_no"
													onchange={async () => {
														await handleChangePallet();
													}}
												>
													<option value="">선택</option>
													{#if $loadedStore.items && Array.isArray($loadedStore.items)}
														{#each $loadedStore.items as item (item.pallet_no || '')}
															<option value={item.pallet_no}>
																{item.pallet_no} [ {getProcessGradeName(item.palletGradeCode)} ]
															</option>
														{/each}
													{/if}
												</select>

												<button
													class="p-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors duration-200"
													onclick={reloadLocationCode}
													type="button"
												>
													<Icon data={faRedo} />
												</button>

												<button
													class="w-24 h-10 bg-green-500 hover:bg-green-600 text-white text-sm font-medium rounded-lg transition-colors duration-200 flex items-center justify-center"
													onclick={setLocationCode}
													type="button"
												>
													팔레트확정
												</button>

												{#if palletNumber}
													<button
														class="p-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors duration-200"
														onclick={printPalletNumber}
													>
														<Icon data={faPrint} scale={1.5} />
													</button>
												{/if}
											</div>
										</div>
									</div>
								</div>
							</div>

							<div class="py-3"></div>

							<!--바코드 스캔-->
							<div class="w-full">
								<div class="scan-container">
									<div class="scan-header">
										<div class="flex items-center gap-3">
											<Icon data={faBarcode} class="text-xl" />
											<span class="text-xl font-bold">바코드 스캔</span>
										</div>
									</div>

									<div class="scan-content" id="product_box">
										<!-- 스캔 메시지 -->
										<ScanMessage
											message={!setLocation
												? '적재할 팔레트를 먼저 확정해 주세요.'
												: prodScanMessage || '바코드 값을 입력(스캔)해 주시기 바랍니다.'}
											type={!setLocation || prodScanMessage ? 'error' : 'info'}
											show={true}
										/>

										<!-- 바코드 입력 -->
										<div class="flex flex-row gap-4">
											<div class="flex-1 scan-input-group">
												<input
													bind:this={focusCheckingBarcode}
													bind:value={checkingBarcode}
													class="scan-input"
													id="checking_barcode"
													onkeydown={async (e) => {
														if (e.key === 'Enter') {
															await handleBarcodeInput(e);
														}
													}}
													placeholder="바코드 입력 또는 스캔"
													type="text"
												/>

												<button
													class="scan-button"
													id="checking_barcode_button"
													onclick={async (e) => await handleBarcodeInput(e)}
													type="button"
												>
													확인
												</button>
											</div>

											<!-- 팔레트 정보 표시 -->
											<div class="flex-1">
												{#if palletNumber}
													<div class="pallet-display">
														<div class="pallet-code">
															{#if palletGradeCode}
																<span class="pallet-grade">{getProcessGradeName(tmpGradeCode)}</span
																>
															{:else}
																<span class="text-2xl font-bold text-gray-500 italic"
																	>등급 미설정</span
																>
															{/if}
															{palletNumber}
														</div>
														{#if !palletGradeCode}
															<div class="text-xs text-gray-500 mt-1">
																첫 번째 상품 적재 시 등급이 설정됩니다
															</div>
														{/if}
													</div>
												{:else}
													<div class="pallet-warning">팔레트 확정 필수</div>
												{/if}
											</div>
										</div>

										<!-- 상품 정보 -->
										{#if checkingProduct && checkingProduct.id}
											<div class="product-info">
												<div class="product-info-row">
													<div class="product-info-label">QAID</div>
													<div class="product-info-value">
														{#if checkingProduct.qaid}
															<span class="font-bold text-2xl">{checkingProduct.qaid}</span>

															{#if checkingProduct.rg === 'Y'}
																<Icon data={faRocket} scale={1.5} class="ml-1 text-red-500" />
															{/if}
														{/if}
													</div>
													<div class="product-info-label">상태</div>
													<div class="product-info-value">
														{#if checkingProduct.status > 0}
															{getProductStatusName(checkingProduct.status)}
														{/if}
													</div>
												</div>

												<div class="product-info-row">
													<div class="product-info-label">카테고리</div>
													<div class="product-info-value">
														{#if checkingProduct.cate4}
															{checkingProduct.cate4.name}
															{#if checkingProduct.cate5}
																> {checkingProduct.cate5.name}
															{/if}
														{/if}
													</div>
												</div>

												<div class="product-info-row">
													<div class="product-info-label">상품명</div>
													<div class="product-info-value">
														{checkingProduct.name}
													</div>
												</div>

												<div class="product-info-row">
													<div class="product-info-label">판매가</div>
													<div class="product-info-value">
														{formattedAmount}원
													</div>
													<div class="product-info-label">점검 수량</div>
													<div class="product-info-value">
														{#if checkingProduct.quantity}
															{checkingProduct.quantity}
														{/if}
													</div>
												</div>

												<div class="product-info-row">
													<div class="product-info-label">수리 상태</div>
													<div class="product-info-value">
														<select
															bind:value={selectedGradeCode}
															class="product-info-select"
															id="grade_code"
															onchange={async () => {
																// 선택된 옵션에 해당하는 item.id 값을 찾기
																const selectedItem = repairGradeCodes.find(
																	(item) => item.code === selectedGradeCode
																);
																if (selectedItem) {
																	sendGradeId = selectedItem.id;
																}
																await getRepairSymptoms();
															}}
														>
															<option value="">선택</option>
															{#if repairGradeCodes}
																{#each repairGradeCodes as item (item.code)}
																	<option value={item.code}>{item.name}</option>
																{/each}
															{/if}
														</select>
													</div>
												</div>

												<div class="product-info-row">
													<div class="product-info-label">증상 내용</div>
													<div class="product-info-value">
														<select
															bind:value={selectedSymptomCode}
															class="product-info-select"
															id="symptom_code"
															onchange={async () => {
																// 선택된 옵션에 해당하는 item.id 값을 찾기
																const selectedItem = repairSymptomCodes.find(
																	(item) => item.code === selectedSymptomCode
																);
																if (selectedItem) {
																	sendSymptomId = selectedItem.id;
																}

																await getRepairProcesses();
															}}
														>
															<option value="">선택</option>
															{#if repairSymptomCodes}
																{#each repairSymptomCodes as item (item.code)}
																	<option value={item.code}>{item.name}</option>
																{/each}
															{/if}
														</select>
													</div>
												</div>

												<div class="product-info-row">
													<div class="product-info-label">처리 내용</div>
													<div class="product-info-value">
														<select
															bind:value={selectedProcessCode}
															class="product-info-select flex-1 max-w-[calc(100%-150px)]"
															id="process_code"
															onchange={async () => {
																// 선택된 옵션에 해당하는 item.id 값을 찾기
																const selectedItem = repairProcessCodes.find(
																	(item) => item.code === selectedProcessCode
																);
																if (selectedItem) {
																	sendProcessId = selectedItem.id;
																}
															}}
														>
															<option value="">선택</option>
															{#if repairProcessCodes}
																{#each repairProcessCodes as item (item.code)}
																	<option value={item.code}>{item.name}</option>
																{/each}
															{/if}
														</select>

														{#if isOsReinstallAllowed && (isComputerCategory || isTabletCategory)}
															<div class="checkbox-container ml-4 whitespace-nowrap">
																<input
																	bind:checked={osReinstall}
																	class="checkbox-input"
																	type="checkbox"
																/>
																<label class="checkbox-label">OS 재설치</label>
															</div>
														{/if}
													</div>
												</div>

												{#if isAppleProduct}
													<div class="product-info-row">
														<div class="product-info-label">구성품 추가</div>
														<div class="product-info-value">
															<div class="parts-controls">
																<div class="flex gap-2 mb-3">
																	<select
																		bind:value={selectedPartsCategory}
																		class="product-info-select flex-1"
																		id="parts_category"
																		onchange={async () => {
																			await getParts();
																		}}
																	>
																		<option value="">분류 선택</option>
																		{#if repairPartsCategories}
																			{#each repairPartsCategories as item (item.id)}
																				<option value={item.id}>{item.name}</option>
																			{/each}
																		{/if}
																	</select>

																	<select
																		bind:value={selectedParts}
																		class="product-info-select flex-1"
																		id="parts"
																		disabled={selectedPartsCategory === ''}
																	>
																		<option value="">구성품 선택</option>
																		{#if repairParts}
																			{#each repairParts as item (item.id)}
																				<option value={item.id}>{item.name}</option>
																			{/each}
																		{/if}
																	</select>

																	<button
																		class="parts-add-button"
																		id="button_add_parts"
																		onclick={handleAddParts}
																		type="button"
																	>
																		추가
																	</button>
																</div>

																{#if selectedPartsList.length > 0}
																	<div class="parts-list">
																		{#each selectedPartsList as part (part.id)}
																			<div class="parts-item">
																				<div class="flex-1">
																					<span class="font-medium">{part.name}</span>
																					<span class="text-sm text-gray-500 ml-2">
																						{getNumberFormat(part.price)}원
																					</span>
																					<span class="badge badge-primary badge-sm ml-2">
																						수량: {part.quantity}
																					</span>
																				</div>
																				<div class="flex items-center space-x-2">
																					<!-- 수량 증가 버튼 -->
																					<button
																						class="btn btn-sm btn-ghost"
																						onclick={() => handleIncreaseQuantity(part.id)}
																					>
																						<Icon data={faPlus} />
																					</button>

																					<!-- 수량 감소/삭제 버튼 -->
																					<button
																						class="btn btn-sm btn-error"
																						onclick={() => handleRemoveParts(part.id)}
																					>
																						{#if part.quantity > 1}
																							<Icon data={faMinus} />
																						{:else}
																							<Icon data={faTrash} />
																						{/if}
																					</button>
																				</div>
																			</div>
																		{/each}
																	</div>
																{:else}
																	<div class="parts-empty">선택된 구성품이 없습니다.</div>
																{/if}
															</div>
														</div>
													</div>
												{/if}

												<div class="product-info-row">
													<div class="product-info-label">수리비용 기준</div>
													<div class="product-info-value">
														<div class="w-full flex gap-2">
															<div class="w-40">
																<select
																	bind:value={costType}
																	class="product-info-select"
																	id="cost_type"
																>
																	<option value="">선택</option>
																	<option value="size">크기</option>
																	<option value="price">판매가</option>
																	<option value="common">공통</option>
																</select>
															</div>
															<div class="w-60">
																<select
																	bind:value={costRangeSelected}
																	class="product-info-select"
																	id="cost_range"
																>
																	{#if (costRangeOptions as any[]).length > 0}
																		{#each costRangeOptions as item}
																			<option value={item.id}>
																				{item.range}
																			</option>
																		{/each}
																	{/if}
																</select>
															</div>
															<div class="w-28">
																<select
																	bind:value={costUnit}
																	class="product-info-select"
																	id="cost_unit"
																>
																	<option value="">선택</option>
																	<option value="inch">inch</option>
																	<option value="cm">cm</option>
																	<option value="won">원</option>
																</select>
															</div>
														</div>
													</div>
												</div>

												<div class="product-info-row">
													<div class="product-info-label">수리비용1</div>
													<div class="product-info-value">
														<input
															bind:this={invoice1Readonly}
															bind:value={invoice1}
															class="product-info-input"
															id="invoice1"
															max="9999999"
															min="0"
															name="invoice1"
															step="10"
															type="number"
														/>
													</div>
													<div class="product-info-label">수리비용2</div>
													<div class="product-info-value">
														<input
															bind:value={invoice2}
															class="product-info-input"
															id="invoice2"
															max="9999999"
															min="0"
															name="invoice2"
															step="10"
															type="number"
														/>
													</div>
												</div>

												<div class="product-info-row">
													<div class="product-info-label">추가비용</div>
													<div class="product-info-value">
														<input
															bind:this={invoice3Readonly}
															bind:value={invoice3}
															class="product-info-input"
															id="invoice3"
															max="9999999"
															min="0"
															name="invoice3"
															step="10"
															type="number"
														/>
													</div>
													<div class="product-info-label text-red-600 text-xl">총 청구 비용</div>
													<div class="product-info-value">
														<input
															bind:this={invoiceTotalReadonly}
															class="product-info-input"
															id="total_invoice"
															max="99999999"
															min="0"
															name="total_invoice"
															step="10"
															type="number"
															value={totalInvoice}
														/>
													</div>
												</div>

												<div class="product-info-row">
													<div class="product-info-label">메모</div>
													<div class="product-info-value">
														<textarea
															bind:value={memo}
															class="product-info-textarea"
															id="memo"
															name="memo"
															rows="3"
															placeholder="작성내용은 처리내용 뒤에 첨부됩니다"
														></textarea>
													</div>
												</div>
											</div>

											<!-- 액션 버튼 -->
											<div class="action-buttons">
												<button
													class="action-button"
													class:action-button-success={isCompleteButtonSuccess}
													class:action-button-default={!isCompleteButtonSuccess}
													id="complete_btn"
													onclick={completeCheckIn}
													type="button"
												>
													<Icon data={faBoxesPacking} class="mr-2" />
													팔레트에 적재
												</button>

												<button
													class="action-button action-button-neutral"
													id="cancel_btn"
													onclick={cancelCheckIn}
													type="button"
												>
													<Icon data={faBan} class="mr-2" />
													적재 취소
												</button>
											</div>
										{/if}
									</div>
								</div>
							</div>
						</div>

						<ExampleBarcodeCommand {commandVisible} page="pallet" />
					</div>

					<div class="w-[400px] pt-48 pl-4 flex flex-col">
						<!-- 최근 적재 -->
						<div class="scan-container">
							<div class="scan-header">
								<div class="flex items-center gap-2">
									<Icon data={faDolly} scale={1.5} />
									<span class="text-xl font-bold">
										{#if palletNumber}
											<span class="underline">{palletNumber}</span>
										{:else}
											-
										{/if}
										최근 적재
									</span>
								</div>
							</div>

							<div class="scan-content">
								<div class="text-center py-6">
									<div class="text-8xl font-bold text-red-600 dark:text-red-400 mb-4">
										{#if palletProdCount}
											{palletProdCount}
										{:else}
											0
										{/if}
									</div>
									<div class="text-sm text-gray-600 dark:text-gray-400">적재된 상품 수</div>
								</div>

								{#if palletRecentProducts.length > 0}
									<div class="space-y-2">
										<div class="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
											최근 적재 목록
										</div>
										{#each palletRecentProducts as product, i}
											<div
												class="flex items-center p-2 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600"
											>
												<span class="text-xs font-mono text-gray-600 dark:text-gray-400 w-8">
													[{palletProdCount - i}]
												</span>
												<span class="text-sm text-gray-900 dark:text-gray-100">
													{product}
												</span>
											</div>
										{/each}
									</div>
								{:else}
									<div class="text-center py-4 text-gray-500 dark:text-gray-400 text-sm">
										적재된 상품이 없습니다
									</div>
								{/if}
							</div>
						</div>
					</div>
				</div>
			</section>

			<MessageModal
				bind:this={modal}
				type={modalType}
				title={modalTitle}
				message={modalMessage}
				onClose={handleModalClose}
			/>
		</div>
	{/snippet}
</AppLayout>
