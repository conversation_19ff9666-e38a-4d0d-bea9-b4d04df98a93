<script lang="ts">
	import { onMount } from 'svelte';
	import { getUser, type User } from '$lib/User';
	import { page } from '$app/state';

	import { openWebviewWindow } from '$lib/services/windowService';
	import { authClient } from '$lib/services/AxiosBackend';
	import { toast } from 'svoast';
	import { removeFromLocalStorage } from '$lib/utils/StorageManager';

	import {
		executeAsk,
		executeMessage,
		formatDateTimeToString,
		getNumberFormat,
		getPayload,
		handleCatch,
		handleDownload
	} from '$lib/Functions';
	import { getPalletStatusName, PALLET_STATUS_CLOSED } from '$stores/palletStore';
	import { getProcessGradeColorButton } from '$stores/processStore';
	import {
		getPalletProductCheckedStatusName,
		loadItems,
		palletInfoStore,
		palletProductStore
	} from '$stores/palletProductStore';
	import { EXCEL_DOWNLOAD_URL } from '$stores/constant';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import SearchUI from '$components/UI/SearchUI.svelte';
	import SearchField from '$components/Snippets/SearchField.svelte';
	import SearchFieldTitle from '$components/Snippets/SearchFieldTitle.svelte';
	import SearchFieldContent from '$components/Snippets/SearchFieldContent.svelte';
	import Loading from '$components/Loading/Circle2.svelte';
	import SearchQaid from '$components/Snippets/SearchQaid.svelte';
	import SearchPalletInspection from '$components/Snippets/SearchPalletInspection.svelte';
	import ButtonExcelDownload from '$components/Button/ExcelDownload.svelte';

	import Icon from 'svelte-awesome';
	import { faPrint } from '@fortawesome/free-solid-svg-icons/faPrint';
	import { faDeleteLeft } from '@fortawesome/free-solid-svg-icons/faDeleteLeft';
	import { faCheckDouble } from '@fortawesome/free-solid-svg-icons/faCheckDouble';
	import { faBackspace } from '@fortawesome/free-solid-svg-icons/faBackspace';
	import { faLock } from '@fortawesome/free-solid-svg-icons/faLock';
	import { faUnlock } from '@fortawesome/free-solid-svg-icons/faUnlock';
	import { faRocket } from '@fortawesome/free-solid-svg-icons/faRocket';
	import { faXmark } from '@fortawesome/free-solid-svg-icons/faXmark';
	import { faFolder } from '@fortawesome/free-regular-svg-icons/faFolder';
	import MessageModal from '$components/UI/MessageModal.svelte';

	let user: User = getUser();
	const apiUrl = `/api/wms/pallets/products`;
	const localUrl = page.url.pathname;
	let isLoading = $state(false); // 로딩중 표시 여부

	// 검색 query string 시작 ==========
	let palletId = $state(page.url.searchParams.get('id') ?? '');
	let checkedStatus = $state(page.url.searchParams.get('checkedStatus') ?? '');
	let prevPalletStatus = $state(page.url.searchParams.get('palletStatus') ?? '');
	let searchType = $state('qaid');
	let keyword = $state(''); // 검색어
	let p = $state(page.url.searchParams.get('p') || '1');
	let pageSize = $state(page.url.searchParams.get('pageSize') || '800');
	let searchParams = $state('');
	let apiSearchParams = $state('');
	let startNo = $state(0);
	// 검색 query string 종료 ==========

	// =============== 페이지 내 필요한 변수들 시작 ===============
	let allChecked = $state(false); // 전체 선택: 체크박스
	let idChecked: boolean[] = $state([]); // 전체 선택: 모든 체크박스의 상태를 참조하는 reactive 변수
	let productIds: number[] = $state([]); // 점검 취소할 때 상품 id를 담는 배열

	let pageLocationCountry = $state('');
	let pageLocationCity = $state('');
	let pageLocationStore = $state('');
	let pageLocationLine = $state('');
	let pageLocationRack = $state('');
	let pageLocationLevel = $state('');
	let pageLocationColumn = $state('');
	let pageLocationName = $state('');
	let pagePalletCode = $state('');
	let pagePalletStatus = $state(0);
	let pagePalletStatusName = $state('');
	let pageExportDate = $state('');
	let pagePalletGradeName = $state('');
	let pagePalletRegisteredUserName = $state('');
	let pagePalletCheckoutAt = $state('');
	let pageTotalQuantity = $state(0);
	let pageTotalAmount = $state(0);
	let pageTotalInvoice1 = $state(0);
	let pageTotalInvoice2 = $state(0);
	let pageTotalInvoice3 = $state(0);
	let pageInvoiceTotal = $state(0);

	let focusCheckQaidInput: HTMLInputElement;
	let checkQaid = $state(''); // 검수할 QAID

	let showProductName = $state('출고검수를 하면 상품 이름이 나옵니다.');
	// =============== 페이지 내 필요한 변수들 종료 ===============

	// 모달창 관련 변수
	let modal: MessageModal;
	let modalType = $state<'error' | 'warning' | 'success'>('error');
	let modalTitle = $state('');
	let modalMessage = $state('');

	// 모달창 표시 함수
	function showModal(
		message: string,
		type: 'error' | 'warning' | 'success' = 'error',
		title: string = '알림'
	) {
		modalType = type;
		modalTitle = title;
		modalMessage = message;

		if (modal) {
			modal.show();
		} else {
			executeMessage(`${title}: ${message}`);
		}
	}

	// 모달 닫힘 핸들러
	function handleModalClose() {
		checkQaid = '';
		setTimeout(() => {
			if (focusCheckQaidInput) {
				focusCheckQaidInput.focus();
			}
		}, 100);
	}

	/**
	 * API로 부터 데이터를 가져온다.
	 */
	async function makeData() {
		isLoading = true;

		const common_params = {
			exportType: 'pallets',
			pallet_id: palletId,
			checked_status: checkedStatus,
			search_type: searchType,
			keyword: keyword,
			pageSize: pageSize
		};

		const params = new URLSearchParams({ p: p, ...common_params });
		const api_params = new URLSearchParams({ page: p, ...common_params });

		searchParams = params.toString(); // local
		apiSearchParams = api_params.toString(); // api

		await loadItems(`${apiUrl}?${apiSearchParams}`, user);

		if ($palletInfoStore) {
			pageLocationCountry = $palletInfoStore.pallet_info.country;
			pageLocationCity = $palletInfoStore.pallet_info.city;
			pageLocationStore = $palletInfoStore.pallet_info.store;
			pageLocationLine = $palletInfoStore.pallet_info.line;
			pageLocationRack = $palletInfoStore.pallet_info.rack;
			pageLocationLevel = $palletInfoStore.pallet_info.level;
			pageLocationColumn = $palletInfoStore.pallet_info.column;
			pageLocationName = $palletInfoStore.pallet_info.location_name;
			pagePalletCode = $palletInfoStore.pallet_info.code;
			pagePalletStatus = $palletInfoStore.status;
			pagePalletStatusName = getPalletStatusName($palletInfoStore.status);
			pageExportDate = formatDateTimeToString($palletInfoStore.exported_at ?? '') ?? '';
			pagePalletGradeName = $palletInfoStore.palletGradeName ?? '';
			pagePalletRegisteredUserName = $palletInfoStore.registered_user?.name ?? '';
			pagePalletCheckoutAt = $palletInfoStore.checked_at ?? '';
			pageTotalQuantity = $palletInfoStore.totalQuantity;
			pageTotalAmount = $palletInfoStore.totalAmount;
			pageTotalInvoice1 = $palletInfoStore.totalInvoice1;
			pageTotalInvoice2 = $palletInfoStore.totalInvoice2;
			pageTotalInvoice3 = $palletInfoStore.totalInvoice3;
			pageInvoiceTotal = $palletInfoStore.invoiceTotal;
		}

		if ($palletProductStore) {
			startNo = $palletProductStore.items.length;
		}

		if (pagePalletStatus < PALLET_STATUS_CLOSED) {
			await handleModalClose();
		}

		isLoading = false;
	}

	/**
	 * 자식 컴포넌트에서 변경된 변수의 값을 매칭
	 *
	 * @param value
	 */
	function changeSearchParams(value: any) {
		if (value.detail.p) p = value.detail.p;
		if (value.detail.checkedStatusGroup || value.detail.checkedStatusGroup === '')
			checkedStatus = value.detail.checkedStatusGroup;
		if (value.detail.searchType) searchType = value.detail.searchType;
		if (value.detail.keyword || value.detail.keyword === '') keyword = value.detail.keyword;
		if (value.detail.pageSize) pageSize = value.detail.pageSize;

		makeData();
	}

	// 전체 선택
	const toggleAllCheck = (items: any) => {
		allChecked = !allChecked;
		idChecked = items.map(() => allChecked);
		productIds = allChecked ? items.map((item: any) => item.product_id) : [];
	};

	// 선택 또는 단일 항목 점검 취소 (상태 기반)
	async function cancelPalletProducts(idsToCancel?: number[]) {
		// 1) 호출자가 지정했으면 그 값 사용, 아니면 현재 선택(ids) 사용
		const palletProductIds = idsToCancel?.length ? idsToCancel : productIds;

		if (!palletProductIds.length) {
			showModal('삭제(점검취소)할 상품을 선택해주시기 바랍니다.', 'warning', '점검 취소');
			return false;
		}

		const msg =
			palletProductIds.length > 1
				? `선택한 ${palletProductIds.length}개의 상품을 [점검 취소] 하시겠습니까?`
				: '이 상품의 점검내역을 취소하시겠습니까?';
		const ask = await executeAsk(`팔레트[ ${pagePalletCode} ]에서 상품을 삭제합니다.\n\n${msg}`);
		if (!ask) return false;

		try {
			// 백엔드 계약에 맞는 키 이름으로 그대로 전송
			const payload = {
				_method: 'PATCH',
				productIds: palletProductIds
			};

			const { status, data } = await authClient.post(
				`/api/wms/pallets/products/exclude-from-pallet`,
				payload
			);
			if (status === 200 && data.success) {
				toast.success('점검 취소 완료');

				// 선택 상태 초기화 후 목록 갱신
				const itemsList = ($palletProductStore.items ?? []) as any[];
				idChecked = Array(itemsList.length).fill(false);
				productIds = [];

				await makeData();
			} else {
				const message = data.data.message.replace(/\\n/g, '<br>');
				showModal(message, 'error', '점검 취소 오류');
			}
		} catch (e: any) {
			const message = (await handleCatch(e, true)) as string;
			showModal(message, 'error', '점검 취소 오류');
		}
	}

	/**
	 * 팔레트 상품 출고검수 처리<br>
	 * 기존 함수: checkQaid
	 */
	async function deliveryInspection() {
		try {
			const payload = {
				_method: 'PATCH',
				pallet_id: palletId,
				pallet_code: pagePalletCode,
				qaid: checkQaid
			};

			const { status, data } = await authClient.post(apiUrl, payload);

			if (status === 200 && data.success) {
				toast.success(`${checkQaid} 출고검수 완료`);
				showProductName = data.data.product_name;

				await makeData();
			} else {
				const message = data.data.message.replace(/\\n/g, '<br>');
				showModal(message, 'error', '출고 검수 오류');
			}
		} catch (e: any) {
			const message = (await handleCatch(e, true)) as string;
			showModal(message, 'error', '점검 취소 오류');
		}
	}

	// 점검 마감(출고 대기)
	async function closePallet() {
		const ask = await executeAsk(`팔레트[ ${pagePalletCode} ]의 모든 상품을 마감하시겠습니까?`);
		if (!ask) {
			return false;
		}

		try {
			const payload = {
				pallet_id: palletId
			};
			const { status, data } = await authClient.put(`/api/wms/pallets/close`, payload);

			if (status === 200 && data.success) {
				toast.success(`팔레트[ ${pagePalletCode} ] 마감 완료`);

				removeFromLocalStorage('worked_location_code');

				await makeData();
			} else {
				const message = data.data.message.replace(/\\n/g, '<br>');
				showModal(message, 'error', '점검 마감(출고 대기) 오류');
			}
		} catch (e: any) {
			const message = (await handleCatch(e, true)) as string;
			showModal(message, 'error', '점검 취소 오류');
		}
	}

	// 점검 마감 취소(적재 가능)
	async function openPallet() {
		const ask = await executeAsk(`팔레트[ ${pagePalletCode} ]의 마감을 취소하시겠습니까?`);
		if (!ask) {
			return false;
		}

		try {
			const payload = {
				pallet_id: palletId
			};
			const { status, data } = await authClient.put(`/api/wms/pallets/open`, payload);

			if (status === 200 && data.success) {
				toast.success(`팔레트[ ${pagePalletCode} ] 마감 취소 완료`);

				await makeData();
			} else {
				const message = data.data.message.replace(/\\n/g, '<br>');
				showModal(message, 'error', '점검 마감 취소(적재 가능) 오류');
			}
		} catch (e: any) {
			const message = (await handleCatch(e, true)) as string;
			showModal(message, 'error', '점검 취소 오류');
		}
	}

	// 팔레트 라벨 프린트 (명시적 인자 전달)
	async function printPalletCode(
		gradeName: string,
		level: string,
		column: string,
		exportDate?: string
	) {
		const exported =
			exportDate && exportDate.length
				? exportDate
				: (formatDateTimeToString(pagePalletCheckoutAt ?? '') ?? '');

		const url = `/print/label?level=${level}&column=${column}&grade_name=${gradeName}&export_date=${exported}`;

		await openWebviewWindow(url, 'Print', { width: 1280, height: 800 });
	}

	onMount(async () => {
		// 초기화는 타입에 맞는 초기값으로 설정
		palletInfoStore.set({
			pallet_info: {
				code: '',
				store: '',
				line: '',
				rack: '',
				level: '',
				column: '',
				location_name: '',
				country: '',
				city: ''
			},
			status: 0,
			exported_at: null,
			registered_user: { name: '' },
			checked_at: null,
			totalQuantity: 0,
			totalAmount: 0,
			totalInvoice1: 0,
			totalInvoice2: 0,
			totalInvoice3: 0,
			invoiceTotal: 0,
			palletGradeName: ''
		});
		palletProductStore.set({ items: [] });

		await makeData();
	});
</script>

<svelte:head>
	<title>출고 > 팔레트 작업목록</title>
</svelte:head>

{#if isLoading}
	<Loading size="60" unit="px" />
{/if}

<AppLayout {user}>
	{#snippet main()}
		<section class="pl-4">
			<div class="breadcrumbs">
				<ul>
					<li>
						<a href="/pallets">
							<Icon data={faFolder} /> 출고
						</a>
					</li>
					<li>
						<a href="/pallets/list">
							<Icon data={faFolder} /> 팔레트 작업 목록
						</a>
					</li>
					<li>
						<span>{pagePalletCode}</span>
						<span class="ml-2">{pagePalletStatusName}</span>
					</li>
				</ul>
			</div>
		</section>

		<section class="main-section">
			<SearchUI>
				<SearchField>
					<SearchFieldTitle title="팔레트 위치" />
					<SearchFieldContent>
						<button
							class="btn btn-sm btn-ghost"
							onclick={() =>
								printPalletCode(
									pagePalletGradeName,
									pageLocationLevel,
									pageLocationColumn,
									pageExportDate
								)}
						>
							{pagePalletCode}
							<Icon data={faPrint} />
						</button>
					</SearchFieldContent>
				</SearchField>

				{#if pagePalletStatus < PALLET_STATUS_CLOSED}
					<SearchPalletInspection
						onUpdate={changeSearchParams}
						checkedStatusGroup={checkedStatus}
					/>
				{/if}

				<SearchQaid
					{keyword}
					onUpdate={changeSearchParams}
					option1="QAID/바코드/상품명"
					option2="점검자"
					{searchType}
				>
					<ButtonExcelDownload
						onclick={async (e: MouseEvent) => {
							e.preventDefault();
							isLoading = true;

							const payload = getPayload(apiSearchParams);
							payload.palletCode = pagePalletCode;
							payload.palletIds = [Number(palletId)];

							await handleDownload(EXCEL_DOWNLOAD_URL, payload);
							isLoading = false;
						}}
						useTooltip={true}
					/>
				</SearchQaid>
			</SearchUI>

			{#if pagePalletStatus < PALLET_STATUS_CLOSED}
				<div
					class="w-full px-2 flex flex-col-reverse lg:flex-row items-center justify-center xl:justify-between"
				>
					<div class="w-full">
						<div class="w-full flex">
							<div class="flex items-center w-48 p-1 bg-orange-700 text-white">출고검수</div>
							<div class="flex flex-grow items-center pl-5 p-3 bg-orange-200">
								<label class="input input-bordered input-sm flex items-center justify-center gap-2">
									<input
										bind:this={focusCheckQaidInput}
										bind:value={checkQaid}
										class="grow bg-base-100"
										onkeydown={(e: KeyboardEvent) => {
											if (e.key === 'Enter') {
												deliveryInspection();
											}
										}}
										type="text"
									/>

									<span
										onclick={() => {
											checkQaid = '';
										}}
										role="presentation"
									>
										<Icon class="cursor-pointer" data={faXmark} />
									</span>
								</label>

								<button
									class="btn btn-sm bg-orange-500 ml-3 hover:bg-orange-700 text-white"
									onclick={deliveryInspection}
								>
									<Icon data={faCheckDouble} />
									출고검수
								</button>

								<span class="ml-1 text-xs text-black">
									* 상품 QAID를 스캔하면 빠르게 출고검수를 진행할 수 있습니다.
								</span>
							</div>
						</div>

						<div
							class="p-2.5 bg-neutral-950 text-white text-5xl font-bold max-w-full break-words leading-tight"
						>
							{showProductName}
						</div>
					</div>
				</div>

				<div class="pt-3"></div>
			{/if}

			<!-- 리스트 시작 -->
			<div class="px-2">
				<div class="row" style="padding-bottom: 5px;">
					{#if pagePalletStatus < PALLET_STATUS_CLOSED}
						<button onclick={closePallet} class="btn btn-info btn-xs">
							<Icon data={faLock} />
							점검마감(출고대기)
						</button>
					{/if}

					{#if pagePalletStatus === PALLET_STATUS_CLOSED}
						<button onclick={openPallet} class="btn btn-warning btn-xs">
							<Icon data={faUnlock} />
							마감취소(적재가능)
						</button>
					{/if}
				</div>

				<table class="table text-xs table-pin-rows table-zebra">
					<thead class="uppercase">
						<tr class="h-8 bg-base-content text-base-300 text-center">
							{#if pagePalletStatus <= PALLET_STATUS_CLOSED}
								<th class="p-0.5">
									<input
										checked={allChecked}
										onchange={() => toggleAllCheck($palletProductStore.items)}
										type="checkbox"
									/>
								</th>
							{/if}
							<th class="p-0.5">번호</th>
							<th class="p-0.5">QAID</th>
							<th class="p-0.5">카테고리</th>
							<th class="p-0.5">상품명</th>
							<th class="p-0.5">판매가</th>
							<th class="p-0.5">컨디션</th>
							<th class="p-0.5">
								<p class="text-red-600">증상내용</p>
							</th>
							<th class="p-0.5">점검자</th>
							<th class="p-0.5">
								<p class="text-green-700">처리내용</p>
							</th>
							<th class="p-0.5">수리비용1</th>
							<th class="p-0.5">수리비용2</th>
							<th class="p-0.5">추가비용</th>
							<th class="p-0.5">수리일자</th>
							<th class="p-0.5">출고검수</th>
							<th class="p-0.5">검수일자</th>
							<th class="p-0.5">검수자</th>
							<th class="p-0.5">출고일자</th>
							{#if pagePalletStatus <= PALLET_STATUS_CLOSED}
								<th class="min-w-24 p-0.5 text-right">
									<button
										type="button"
										class="btn btn-error btn-xs"
										onclick={() => cancelPalletProducts()}
									>
										<Icon data={faBackspace} />
										선택취소
									</button>
								</th>
							{/if}
						</tr>
					</thead>

					<tfoot class="uppercase">
						<tr class="h-12 bg-base-content text-base-300">
							{#if pagePalletStatus <= PALLET_STATUS_CLOSED}
								<th class="p-0.5 text-right" colspan="4">합계</th>
							{:else}
								<th class="p-0.5 text-right" colspan="3">합계</th>
							{/if}
							<th class="p-0.5 text-center">{getNumberFormat(pageTotalQuantity)} 개 상품</th>
							<th class="p-0.5 text-right">{getNumberFormat(pageTotalAmount)} 원</th>
							<th class="p-0.5 text-right" colspan="4">수리비: </th>
							<th class="p-0.5 text-right">{getNumberFormat(pageTotalInvoice1)} 원</th>
							<th class="p-0.5 text-right">{getNumberFormat(pageTotalInvoice2)} 원</th>
							<th class="p-0.5 text-right">{getNumberFormat(pageTotalInvoice3)} 원</th>
							{#if pagePalletStatus <= PALLET_STATUS_CLOSED}
								<th class="p-0.5 text-right" colspan="5"
									>총수리비(수리비용 + 추가비용) = {getNumberFormat(pageInvoiceTotal)} 원</th
								>
								<th class="p-0.5"></th>
							{:else}
								<th class="p-0.5 text-right" colspan="4"
									>총수리비(수리비용 + 추가비용) = {getNumberFormat(pageInvoiceTotal)} 원</th
								>
								<th class="p-0.5"></th>
							{/if}
						</tr>
					</tfoot>

					<tbody>
						{#if $palletProductStore.items}
							{#each $palletProductStore.items as item, index}
								<tr class="hover:bg-base-content/10">
									{#if pagePalletStatus <= PALLET_STATUS_CLOSED}
										<td class="w-[20px] min-w-[20px] max-w-[20px] p-0.5 text-center">
											<input
												bind:checked={idChecked[index]}
												bind:group={productIds}
												value={item.product_id}
												type="checkbox"
											/>
										</td>
									{/if}
									<td class="w-[30px] min-w-[30px] max-w-[30px] p-0.5 text-center">
										<input type="hidden" name="pallet_product_id" bind:value={item.product_id} />
										<input
											type="hidden"
											name="pallet_product_name"
											bind:value={item.product.name}
										/>
										{getNumberFormat(startNo - index)}
									</td>
									<td class="w-[100px] min-w-[100px] max-w-[100px] p-0.5 text-center">
										<span class="flex justify-center">
											{item.product.qaid}

											{#if item.product.rg === 'Y'}
												<Icon data={faRocket} class="mx-0.5 text-red-700" />
											{/if}
										</span>
									</td>
									<td class="w-[180px] min-w-[120px] max-w-[180px] p-0.5 text-center">
										<div class="flex items-center">
											<div class="w-1/2 p-0">{item.product.cate4.name}</div>
											{#if item.product.cate5}
												<div class="w-1/2 p-0">{item.product.cate5.name}</div>
											{/if}
										</div>
									</td>
									<td
										class="w-[100px] min-w-[100px] max-w-[100px] p-0.5 tooltip tooltip-top"
										data-tip={item.product.name}
									>
										<p class="line-clamp-2 cursor-help">
											{item.product.name}
										</p>
									</td>
									<td class="w-[80px] min-w-[80px] max-w-[80px] p-0.5 text-right">
										{getNumberFormat(item.product.amount)} 원
									</td>
									<td class="w-[45px] min-w-[45px] max-w-[45px] p-0.5 text-center">
										{@html getProcessGradeColorButton(item)}
									</td>
									<td class="w-[90px] min-w-[90px] max-w-[90px] p-0.5">
										<p class="text-red-600">{item.repair_symptom.name}</p>
									</td>
									<td class="w-[45px] min-w-[45px] max-w-[45px] p-0.5 text-center">
										{pagePalletRegisteredUserName}
									</td>
									<td class="w-[90px] min-w-[90px] max-w-[90px] p-0.5">
										<p class="text-green-700">{item.repair_process.name}</p>
										<!-- 여기에 메모가 들어가야 한다는데 메모를 뽑아오지를 않음 아마 상품의 메모겠지??? -->
									</td>
									<td class="w-[60px] min-w-[60px] max-w-[60px] p-0.5 text-right"
										>{getNumberFormat(item.invoice1)} 원</td
									>
									<td class="w-[60px] min-w-[60px] max-w-[60px] p-0.5 text-right"
										>{getNumberFormat(item.invoice2)} 원</td
									>
									<td class="w-[60px] min-w-[60px] max-w-[60px] p-0.5 text-right"
										>{getNumberFormat(item.invoice3)} 원</td
									>
									<td class="w-[80px] min-w-[80px] max-w-[80px] p-0.5 text-center"
										>{formatDateTimeToString(item.registered_at ?? '')}</td
									>
									<td class="w-[75px] min-w-[75px] max-w-[75px] p-0.5 text-center">
										{getPalletProductCheckedStatusName(item.checked_status)}
									</td>
									<td class="w-[80px] min-w-[80px] max-w-[80px] p-0.5 text-center"
										>{formatDateTimeToString(item.checked_at ?? '')}</td
									>
									<td class="w-[45px] min-w-[45px] max-w-[45px] p-0.5 text-center">
										{#if item.checked_user}
											{item.checked_user.name}
										{/if}
									</td>
									<td class="w-[80px] mix-w-[80px] max-w-[80px] p-0.5 text-center">
										{formatDateTimeToString(pagePalletCheckoutAt)}
									</td>
									{#if pagePalletStatus <= PALLET_STATUS_CLOSED}
										<td class="min-w-24 p-0.5 text-right">
											<button
												class="btn btn-warning btn-xs"
												onclick={() => cancelPalletProducts([item.product_id])}
											>
												<Icon data={faDeleteLeft} />
												점검취소
											</button>
										</td>
									{/if}
								</tr>
							{/each}
						{/if}
					</tbody>
				</table>
			</div>
		</section>

		<MessageModal
			bind:this={modal}
			type={modalType}
			title={modalTitle}
			message={modalMessage}
			onClose={handleModalClose}
		/>
	{/snippet}
</AppLayout>
