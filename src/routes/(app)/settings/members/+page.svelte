<script lang="ts">
	import { getUser, type User } from '$lib/User';
	import { onMount, tick } from 'svelte';
	import { page } from '$app/state';
	import type { Breadcrumb, Member } from '$lib/types/types';

	import { authClient } from '$lib/services/AxiosBackend';
	import { toast } from 'svoast';

	import {
		checkFileSize,
		copyToClipboard,
		executeAsk,
		executeMessage,
		getNumberFormat,
		handleCatch
	} from '$lib/Functions';

	import {
		getMemberStatusName,
		getRoleName,
		loadMembers,
		MEMBER_STATUS_DELETED,
		memberRoles,
		memberStatuses,
		memberStore
	} from '$stores/memberStore';
	import { MENUS } from '$lib/Menu';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import Loading from '$components/Loading/Circle2.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import SearchUI from '$components/UI/SearchUI.svelte';
	import SearchField from '$components/Snippets/SearchField.svelte';
	import SearchFieldTitle from '$components/Snippets/SearchFieldTitle.svelte';
	import SearchFieldContent from '$components/Snippets/SearchFieldContent.svelte';
	import TableTop from '$components/UI/TableTop.svelte';
	import Paginate from '$components/UI/Paginate.svelte';
	import SearchButton from '$components/Button/Search.svelte';
	import ResetButton from '$components/Button/Reset.svelte';

	import Icon from 'svelte-awesome';
	import { faPlus } from '@fortawesome/free-solid-svg-icons/faPlus';
	import { faPenToSquare } from '@fortawesome/free-regular-svg-icons/faPenToSquare';
	import { faTrashCan } from '@fortawesome/free-regular-svg-icons/faTrashCan';
	import { faMobileScreenButton } from '@fortawesome/free-solid-svg-icons/faMobileScreenButton';
	import { faAt } from '@fortawesome/free-solid-svg-icons/faAt';
	import { faXmark } from '@fortawesome/free-solid-svg-icons/faXmark';
	import { faSave } from '@fortawesome/free-regular-svg-icons/faSave';
	import { faQuestionCircle } from '@fortawesome/free-regular-svg-icons/faQuestionCircle';
	import { faArrowRotateRight } from '@fortawesome/free-solid-svg-icons/faArrowRotateRight';
	import { faEyeSlash } from '@fortawesome/free-solid-svg-icons/faEyeSlash';
	import { faEye } from '@fortawesome/free-solid-svg-icons/faEye';

	const user: User = getUser();
	const apiUrl = '/api/wms/settings/members';
	const localUrl = page.url.pathname;
	let isLoading = $state(false); // 로딩중 표시

	// 검색 query string 시작 ==========
	let roleGroup = $state('');
	let statusGroup = $state('1');
	let keyword = $state(''); // 검색어
	let p = $state(page.url.searchParams.get('p') || '1');
	let pageSize = $state(page.url.searchParams.get('pageSize') || '16');
	let searchParams = $state('');
	let apiSearchParams = '';
	let startNo = $state(0);
	// 검색 query string 종료 ==========

	// 선택된 메뉴를 저장하는 상태 변수
	interface MenuSelections {
		[key: string]: boolean;
	}
	let menuSelections = $state<MenuSelections>({});

	// 모달창: 미등록상품 등록/수정 창 시작========
	let modal1: HTMLDialogElement;
	let modal2: HTMLDialogElement;
	let modalTypeText = $state('등록');

	let modalMemberId: number;
	let modalMemberRole = $state('Employee');
	let modalMemberPart = $state('일반가전');
	let modalMemberPosition = $state('팀원');
	let modalMemberStatus = $state('1');
	let modalMemberUsername = $state('');
	let modalMemberCapsId = $state('');
	let modalMemberPassword = $state('');
	let modalMemberName = $state('');
	let modalMemberEmail = $state('');
	let modalMemberCellphone = $state('');
	let showPassword = $state(false);
	// 모달창: 미등록상품 등록/수정 창 종료========

	async function makeData() {
		isLoading = true;

		const common_params = {
			role: roleGroup,
			status: statusGroup,
			keyword: keyword,
			pageSize: pageSize
		};

		const params = new URLSearchParams({ p: p, ...common_params });
		const api_params = new URLSearchParams({ page: p, ...common_params });

		searchParams = params.toString(); // local
		apiSearchParams = api_params.toString(); // api

		await loadMembers(`${apiUrl}?${apiSearchParams}`, user);

		if ($memberStore) {
			startNo = $memberStore.pageStartNo ?? 0;
		}

		isLoading = false;
	}

	/**
	 * 페이지 변경 핸들러 (최적화된 페이지네이션용)
	 */
	function handlePageChange(page: number) {
		p = page.toString();
		makeData();
	}

	/**
	 * 자식 컴포넌트에서 변경된 변수의 값을 매칭
	 *
	 * @param value
	 */
	function changeSearchParams(value: any) {
		if (value.detail.p) p = value.detail.p;
		if (value.detail.pageSize) pageSize = value.detail.pageSize;

		makeData();
	}

	function cleanMenuSelections() {
		MENUS.forEach((menu) => {
			menuSelections[menu.id] = false;
			menu.items.forEach((item) => {
				menuSelections[item.id] = false;
			});
		});
	}

	function createModalMember() {
		modalTypeText = '등록';

		modalMemberRole = 'Employee';
		modalMemberStatus = '1';
		modalMemberUsername = '';
		modalMemberCapsId = '';
		modalMemberPart = '일반가전';
		modalMemberPosition = '팀원';
		modalMemberName = '';
		modalMemberPassword = '';
		modalMemberEmail = '';
		modalMemberCellphone = '';

		cleanMenuSelections();

		modal1.showModal();
	}

	async function editModalMember(id: number) {
		const member: Member | undefined = $memberStore.members?.find((member) => member.id === id);

		if (!member) {
			await executeMessage('직원 정보를 찾을 수 없습니다.');
			return;
		}

		if (member.menu) {
			try {
				const savedMenus = JSON.parse(member.menu);

				Object.keys(savedMenus).forEach((menuId) => {
					menuSelections[menuId] = savedMenus[menuId];
				});
			} catch (e) {
				console.error('메뉴 파싱 오류:', e);
			}
		} else {
			cleanMenuSelections();
		}

		modalTypeText = '수정';

		modalMemberId = member.id;
		modalMemberRole = member.role;
		modalMemberStatus = member.status.toString();
		modalMemberUsername = member.username;
		modalMemberCapsId = member.caps_id;
		modalMemberPart = member.part;
		modalMemberPosition = member.position;
		modalMemberName = member.name;
		modalMemberPassword = '';
		modalMemberEmail = member.email;
		modalMemberCellphone = member.cellphone;

		await tick();

		modal1.showModal();
	}

	// 상위 메뉴 선택시 하위 메뉴 모두 선택/해제
	function toggleSubItems(menuId: string) {
		const menu = MENUS.find((m) => m.id === menuId);

		if (menu) {
			const isSelected = menuSelections[menuId];

			menu.items.forEach((item) => {
				menuSelections[item.id] = isSelected;
			});
		}
	}

	async function handleStore() {
		if (!modalMemberRole) {
			await executeMessage('직원의 등급(권한)을 입력해 주세요.');
			return false;
		}

		if (!modalMemberStatus) {
			await executeMessage('직원의 상태를 입력해 주세요.');
			return false;
		}

		if (!modalMemberUsername) {
			await executeMessage('직원이 로그인할 때 사용할 아이디를 입력해 주세요.');
			return false;
		}

		if (modalTypeText === '등록' && !modalMemberPassword) {
			await executeMessage('직원이 로그인할 때 사용할 비밀번호를 입력해 주세요.');
			return false;
		}

		if (!modalMemberName) {
			await executeMessage('직원의 이름 입력해 주세요.');
			return false;
		}

		if (!modalMemberEmail) {
			await executeMessage('메일주소를 입력해 주세요.');
			return false;
		}

		const payload = {
			id: modalMemberId,
			role: modalMemberRole,
			part: modalMemberPart,
			position: modalMemberPosition,
			status: modalMemberStatus,
			username: modalMemberUsername,
			caps_id: modalMemberCapsId,
			password: modalMemberPassword,
			name: modalMemberName,
			email: modalMemberEmail,
			cellphone: modalMemberCellphone,
			menu: JSON.stringify(menuSelections)
		};

		let response;
		try {
			if (modalTypeText === '수정') {
				response = await authClient.put(`${apiUrl}/${modalMemberId}`, payload);
			} else {
				response = await authClient.post(apiUrl, payload);
			}

			const data = response.data;
			if (response.status === 200 && data.success) {
				toast.success(`정상적으로 ${modalTypeText} 되었습니다.`);
				await makeData();
			} else {
				const message = data.data.message.replace(/\\n/g, '\n');
				await executeMessage(message);
			}
		} catch (error: any) {
			if (error.response) {
				const errorData = error.response;

				let errors;
				const keys = [
					'username',
					'password',
					'name',
					'email',
					'cellphone',
					'role',
					'caps_id',
					'status',
					'position',
					'part',
					'menu'
				];
				switch (errorData.status) {
					case 422:
						errors = errorData.data.errors;
						for (const key of keys) {
							if (errors[key]) {
								await executeMessage(errors[key], 'error');
							}
						}
						break;
					case 500:
						await executeMessage(
							'서버에 접속할 수 없는 상태입니다.\n프로그래머에게 문의해 주세요.'
						);
						break;
					default:
						console.error('Error', error.message);
				}
			}
		}
	}

	const handleDelete = async (e: MouseEvent, id: string) => {
		const ask = await executeAsk('정말 이 직원을 삭제 하시겠습니까?', 'warning');
		if (!ask) {
			return false;
		}

		if (!id) {
			await executeMessage(
				'삭제할 직원의 정보를 찾을 수 없습니다. 프로그래머에게 문의해 주세요.',
				'error'
			);
			return false;
		}

		try {
			const { status, data } = await authClient.delete(`${apiUrl}/${id}`);

			if (status === 200 && data.success) {
				toast.success('직원이 삭제 되었습니다.');
				await makeData();
			} else {
				const message = data.data.message.replace(/\\n/g, '\n');
				await executeMessage(message);
			}
		} catch (error: any) {
			if (error.response) {
				const errorData = error.response;

				let errors;
				const keys = ['username', 'password', 'name', 'email', 'cellphone'];
				switch (errorData.status) {
					case 422:
						errors = errorData.data.errors;
						for (const key of keys) {
							if (errors[key]) {
								await executeMessage(errors[key], 'error');
							}
						}
						break;
					case 500:
						await executeMessage(
							'서버에 접속할 수 없는 상태입니다.\n프로그래머에게 문의해 주세요.'
						);
						break;
					default:
						console.error('Error', error.message);
				}
			}
		}
	};

	const handleRestore = async (e: MouseEvent, id: string) => {
		const ask = await executeAsk('직원의 상태를 삭제에서 활성으로 복구 하시겠습니까?', 'warning');
		if (!ask) {
			return false;
		}

		if (!id) {
			await executeMessage(
				'삭제할 직원의 정보를 찾을 수 없습니다. 프로그래머에게 문의해 주세요.',
				'error'
			);
			return false;
		}

		try {
			const { status, data } = await authClient.put(`${apiUrl}/restore/${id}`);

			if (status === 200 && data.success) {
				toast.success('직원의 상태가 복구 되었습니다.');
				await makeData();
			} else {
				const message = data.data.message.replace(/\\n/g, '\n');
				await executeMessage(message);
			}
		} catch (error: any) {
			if (error.response) {
				const errorData = error.response;

				let errors;
				const keys = ['username', 'password', 'name', 'email', 'cellphone'];
				switch (errorData.status) {
					case 422:
						errors = errorData.data.errors;
						for (const key of keys) {
							if (errors[key]) {
								await executeMessage(errors[key], 'error');
							}
						}
						break;
					case 500:
						await executeMessage(
							'서버에 접속할 수 없는 상태입니다.\n프로그래머에게 문의해 주세요.'
						);
						break;
					default:
						console.error('Error', error.message);
				}
			}
		}
	};

	function uploadExcelFile(file: HTMLInputElement, formData: FormData) {
		console.log(file, file.files, formData);
		if (file.files && file.files.length > 0) {
			formData.append('excel', file.files[0]);
			return true;
		}

		return false;
	}

	async function handleUpload() {
		const file = document.getElementById('dialog_upload') as HTMLInputElement;
		const formData = new FormData();
		const excelUploaded = uploadExcelFile(file, formData);

		if (!excelUploaded) {
			await executeMessage('파일을 업로드 할 수 없습니다.', 'error');
			return false;
		}

		try {
			const { status, data } = await authClient.post(`${apiUrl}/attendances`, formData, {
				headers: {
					'Content-Type': 'multipart/form-data'
				}
			});

			if (status === 200 && data.success) {
				await executeMessage(data.data.message);
				file.value = '';
				modal2.close();
			} else {
				await executeMessage('엑셀 파일 업로드에 실패 했습니다.\n프로그래머에게 문의해 주세요.');
			}
		} catch (e: any) {
			await handleCatch(e);
		}
	}

	onMount(async () => {
		memberStore.set({});

		await makeData();

		modal1 = document.getElementById('my_modal_1') as HTMLDialogElement;
		modal2 = document.getElementById('my_modal_2') as HTMLDialogElement;
	});

	const breadcrumbs: Breadcrumb[] = [
		{ title: '설정', url: '/settings/members' },
		{ title: '직원 정보', url: '/settings/members' }
	];
</script>

<svelte:head>
	<title>설정 > 직원 정보</title>
</svelte:head>

{#if isLoading}
	<Loading size="60" unit="px" />
{/if}

<AppLayout {user}>
	{#snippet main()}
		<div>
			<TitleBar {breadcrumbs} />

			<section class="main-section">
				<SearchUI>
					<SearchField>
						<SearchFieldTitle title="직원 등급" />
						<SearchFieldContent>
							<label class="cursor-pointer pl-2">
								<input
									bind:group={roleGroup}
									class="radio-success radio-sm"
									onclick={() => {
										roleGroup = '';
										makeData();
									}}
									type="radio"
									value=""
								/> 전체
							</label>
							{#each $memberRoles as item}
								<label class="cursor-pointer pl-2">
									<input
										bind:group={roleGroup}
										class="radio-success radio-sm"
										onclick={() => {
											roleGroup = item.value;
											makeData();
										}}
										type="radio"
										value={item.value}
									/>
									{item.text}
								</label>
							{/each}
						</SearchFieldContent>
					</SearchField>

					<SearchField>
						<SearchFieldTitle title="상태" />
						<SearchFieldContent>
							<label class="cursor-pointer pl-2">
								<input
									bind:group={statusGroup}
									class="radio-success radio-sm"
									onclick={() => {
										statusGroup = '';
										makeData();
									}}
									type="radio"
									value=""
								/> 전체
							</label>
							{#each $memberStatuses as item}
								<label class="cursor-pointer pl-2">
									<input
										bind:group={statusGroup}
										class="radio-success radio-sm"
										onclick={() => {
											statusGroup = item.value.toString();
											makeData();
										}}
										type="radio"
										value={item.value.toString()}
									/>
									{item.text}
								</label>
							{/each}
						</SearchFieldContent>
					</SearchField>

					<SearchField>
						<SearchFieldTitle title="검색 키워드" />
						<SearchFieldContent>
							<label class="input input-sm input-bordered flex items-center justify-center gap-2">
								<input
									bind:value={keyword}
									class="grow bg-base-100"
									onkeydown={(e) => {
										if (e.key === 'Enter') {
											makeData();
										}
									}}
									placeholder="이름, 아이디, 이메일"
									type="text"
								/>
								<span
									onclick={() => {
										keyword = '';
									}}
									role="presentation"
								>
									<Icon class="cursor-pointer" data={faXmark} />
								</span>
							</label>
							<SearchButton onclick={makeData} tooltipData="검색" useTooltip={true} />
							<ResetButton {localUrl} useTooltip={true} />
						</SearchFieldContent>
					</SearchField>
				</SearchUI>

				<!-- 리스트 시작 -->
				<div class="overflow-x-auto px-2">
					<TableTop onUpdate={changeSearchParams} {pageSize} total={$memberStore.pageTotal ?? 0}>
						{#snippet right()}
							<div class="pl-3">
								<button class="btn btn-primary btn-sm" onclick={createModalMember} type="button">
									<Icon class="w-4 h-4 mr-2" data={faPlus} />
									직원 등록
								</button>

								<button
									class="btn btn-secondary btn-sm"
									onclick={() => {
										modal2.showModal();
									}}
									type="button"
								>
									<Icon class="w-4 h-4 mr-2" data={faPlus} />
									근태 등록
								</button>
							</div>
						{/snippet}
					</TableTop>

					<table class="table text-xs table-pin-rows">
						<thead class="uppercase">
							<tr class="bg-base-content text-base-300 text-center">
								<th>번호</th>
								<th>등급</th>
								<th>이름</th>
								<th>아이디</th>
								<th>연락처</th>
								<th>등록일</th>
								<th>최근 로그인</th>
								<th>계정 상태</th>
								<th>삭제일</th>
								<th></th>
							</tr>
						</thead>

						<tbody>
							{#if $memberStore.members}
								{#each $memberStore.members as item, index}
									<tr class="text-center hover:bg-base-content/10">
										<td>
											{getNumberFormat(startNo - index)}
										</td>
										<td>
											{getRoleName(item.role)}
										</td>
										<td>
											<span
												role="presentation"
												class="cursor-pointer"
												onclick={() => editModalMember(item.id)}
											>
												{#if item.position !== null}
													({item.position})
												{/if}
												{item.name}
											</span>
										</td>
										<td>
											{item.username}
										</td>
										<td>
											<div class="flex justify-center">
												<div
													class="tooltip tooltip-primary cursor-pointer"
													data-tip={item.cellphone}
													onclick={() => copyToClipboard(item.cellphone)}
													role="presentation"
												>
													<Icon data={faMobileScreenButton} />
												</div>

												<div
													class="tooltip tooltip-primary pl-2 cursor-pointer"
													data-tip={item.email}
													onclick={() => copyToClipboard(item.email)}
													role="presentation"
												>
													<Icon data={faAt} />
												</div>
											</div>
										</td>
										<td>
											{new Date(item.created_at).toLocaleDateString()}
										</td>
										<td>
											{#if item.login_at !== null}
												{new Date(item.login_at).toLocaleDateString()}
											{/if}
										</td>
										<td>
											{getMemberStatusName(item.status)}
										</td>
										<td>
											{#if item.deleted_at !== null}
												{new Date(item.deleted_at).toLocaleDateString()}
											{/if}
										</td>
										<td>
											<div class="w-full flex justify-start">
												<button
													class="btn btn-ghost btn-xs tooltip"
													data-tip="직원 정보 수정"
													onclick={() => editModalMember(item.id)}
												>
													<Icon data={faPenToSquare} class="w-6 h-6" />
												</button>

												{#if user.id !== item.id && item.role !== 'Super-Admin'}
													{#if item.status !== MEMBER_STATUS_DELETED}
														<button
															class="btn btn-ghost btn-xs tooltip"
															data-tip="직원 삭제"
															onclick={async (e) => {
																e.preventDefault();
																await handleDelete(e, item.id.toString());
															}}
															type="button"
														>
															<Icon data={faTrashCan} class="w-6 h-6" />
														</button>
													{/if}

													{#if item.status === MEMBER_STATUS_DELETED}
														<button
															class="btn btn-ghost btn-xs tooltip"
															data-tip="삭제 복구"
															onclick={async (e) => {
																e.preventDefault();
																await handleRestore(e, item.id.toString());
															}}
															type="button"
														>
															<Icon data={faArrowRotateRight} class="w-6 h-6" />
														</button>
													{/if}
												{/if}
											</div>
										</td>
									</tr>
								{/each}
							{/if}
						</tbody>
					</table>

					<!-- Pagination -->
					{#if $memberStore.pageTotal && $memberStore.pageTotal > 0}
						<Paginate
							store={memberStore}
							{localUrl}
							onPageChange={handlePageChange}
							{searchParams}
						/>
					{/if}
				</div>
			</section>

			<dialog class="modal" id="my_modal_1">
				<div class="modal-box w-1/2 max-w-2xl">
					<form method="dialog">
						<button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button>
					</form>

					<h3 class="font-bold text-lg">직원 정보 {modalTypeText}</h3>

					<div class="mt-2 grid grid-cols-2 gap-2">
						<div>
							<label class="label text-sm font-bold" for="member_role">권한</label>
							<select
								bind:value={modalMemberRole}
								class="select select-bordered py-1 px-8 focus:ring-0 focus:outline-none"
								id="member_role"
							>
								<option value="">등급 전체</option>
								{#each $memberRoles as role}
									<option value={role.value}>{role.text}</option>
								{/each}
							</select>
						</div>

						<div>
							<label class="label text-sm font-bold" for="member_status">직원 상태</label>
							<select
								bind:value={modalMemberStatus}
								class="select select-bordered py-1 px-8 focus:ring-0 focus:outline-none"
								id="member_status"
							>
								<option value="">상태 전체</option>
								{#each $memberStatuses as status}
									<option value={status.value.toString()}>{status.text}</option>
								{/each}
							</select>
						</div>
					</div>

					<div class="mt-2 grid grid-cols-2 gap-2">
						<div>
							<label class="label text-sm font-bold" for="member_username">아이디(ID)</label>
							<input
								bind:value={modalMemberUsername}
								class="input input-bordered w-11/12 rounded-lg p-2 leading-4 placeholder-base-content focus:ring-base-300 focus:border-base-300"
								id="member_username"
								required
								type="text"
							/>
						</div>

						<div>
							<label class="label text-sm font-bold justify-start" for="member_caps_id">
								캡스 ID
							</label>
							<input
								bind:value={modalMemberCapsId}
								class="input input-bordered w-11/12 rounded-lg p-2 leading-4 placeholder-base-content focus:ring-base-300 focus:border-base-300"
								id="member_caps_id"
								required
								type="text"
							/>
						</div>
					</div>

					<div class="mt-2 grid grid-cols-2 gap-2">
						<div>
							<label class="label text-sm font-bold" for="member_position">부서</label>
							<select
								bind:value={modalMemberPart}
								class="select select-bordered py-1 px-8 focus:ring-0 focus:outline-none"
								id="member_position"
							>
								<option value="">부서 선택</option>
								<option value="IT">IT</option>
								<option value="경영관리">경영관리</option>
								<option value="물류">물류</option>
								<option value="생산관리">생산관리</option>
								<option value="일반가전">일반가전</option>
							</select>
						</div>

						<div>
							<label class="label text-sm font-bold" for="member_position">직책</label>
							<select
								bind:value={modalMemberPosition}
								class="select select-bordered py-1 px-8 focus:ring-0 focus:outline-none"
								id="member_position"
							>
								<option value="">직책 선택</option>
								<option value="대표">대표</option>
								<option value="이사">이사</option>
								<option value="공장장">공장장</option>
								<option value="과장">과장</option>
								<option value="주임">주임</option>
								<option value="팀장">팀장</option>
								<option value="반장">반장</option>
								<option value="팀원">팀원</option>
							</select>
						</div>
					</div>

					<div class="mt-2 grid grid-cols-2 gap-2">
						<div>
							<label class="label text-sm font-bold" for="member_name">이름</label>
							<input
								bind:value={modalMemberName}
								class="w-11/12 input input-bordered rounded-lg p-2 leading-4 placeholder-base-content focus:ring-base-300 focus:border-base-300"
								id="member_name"
								type="text"
							/>
						</div>

						<div>
							<div>
								<label class="label text-sm font-bold justify-start" for="member_password">
									비밀번호
									<span
										class="tooltip tooltip-info pl-2"
										data-tip="비밀번호는 8자 이상, 숫자 + 영문자(대) + 영문자(소) + 특수문자가 1개 이상 포함 되어야 합니다.(안전한 비밀번호를 이용해 주세요.)"
									>
										<Icon data={faQuestionCircle} />
									</span>

									{#if modalTypeText === '수정'}
										<span class="ml-4 font-bold text-red-700">수정시에만 입력</span>
									{/if}
								</label>
								<div class="relative">
									<input
										bind:value={modalMemberPassword}
										class="input input-bordered w-11/12 rounded-lg p-2 leading-4 placeholder-base-content focus:ring-base-300 focus:border-base-300"
										id="member_password"
										required
										type="{showPassword ? 'text' : 'password'}"
									/>
									
									<button
										class="absolute inset-y-0 right-0 pr-10 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
										onclick={() => showPassword = !showPassword}
										type="button"
									>
										{#if showPassword}
											<Icon data={faEyeSlash} />
										{:else}
											<Icon data={faEye} />
										{/if}
									</button>
								</div>
							</div>
						</div>
					</div>

					<div class="mt-2 grid grid-cols-2 gap-2">
						<div>
							<label class="label text-sm font-bold" for="member_email">이메일</label>
							<input
								bind:value={modalMemberEmail}
								class="w-11/12 input input-bordered rounded-lg p-2 leading-4 placeholder-base-content focus:ring-base-300 focus:border-base-300"
								id="member_email"
								type="email"
							/>
						</div>

						<div>
							<label class="label text-sm font-bold" for="member_cellphone">연락처</label>
							<input
								bind:value={modalMemberCellphone}
								class="w-11/12 input input-bordered rounded-lg p-2 leading-4 placeholder-base-content focus:ring-base-300 focus:border-base-300"
								id="member_cellphone"
							/>
						</div>
					</div>

					<div class="mt-2">
						<span class="text-sm font-bold">접근 메뉴 설정</span>
						<div class="mx-2 border-2 border-base-300 rounded-lg overflow-y-auto h-96">
							{#each MENUS as menu}
								<div class="border-b pb-3 mb-2">
									<div class="flex items-center">
										<input
											type="checkbox"
											id={menu.id}
											class="mr-1"
											bind:checked={menuSelections[menu.id]}
											onchange={() => toggleSubItems(menu.id)}
										/>
										<label for={menu.id} class="font-semibold">{menu.name}</label>
									</div>

									<div class="pl-6 grid grid-cols-3 gap-2">
										{#each menu.items as item}
											<div class="flex items-center">
												<input
													type="checkbox"
													id={item.id}
													class="mr-1"
													bind:checked={menuSelections[item.id]}
												/>
												<label for={item.id}>{item.name}</label>
											</div>
										{/each}
									</div>
								</div>
							{/each}
						</div>
					</div>

					<div class="modal-action flex justify-between">
						<div>
							<button
								class="btn btn-primary"
								onclick={async (e) => {
									e.preventDefault();
									await handleStore();
								}}
								type="submit"
							>
								<Icon class="w-5 h-5" data={faSave} />
								정보 {modalTypeText}
							</button>
						</div>
						<div>
							<form method="dialog">
								<!-- if there is a button in form, it will close the modal -->
								<button
									class="btn btn-warning tooltip tooltip-left"
									data-tip="키보드의 Escape 키를 누르면 닫힙니다."
								>
									<Icon class="w-5 h-5" data={faXmark} />
									닫기
								</button>
							</form>
						</div>
					</div>
				</div>
			</dialog>

			<dialog class="modal" id="my_modal_2">
				<div class="modal-box w-1/2 max-w-2xl">
					<form method="dialog">
						<button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button>
					</form>

					<h3 class="font-bold text-lg">근태 엑셀파일 업로드</h3>

					<div class="mt-2">
						<label class="label text-sm font-bold" for="dialog_upload">파일 업로드</label>
						<input
							accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
							class="file-input file-input-bordered w-full rounded-lg p-2 leading-4 placeholder-base-content focus:ring-base-300 focus:border-base-300"
							id="dialog_upload"
							name="excel"
							type="file"
							onchange={checkFileSize}
						/>
					</div>

					<div class="modal-action flex justify-between">
						<div>
							<button class="btn btn-primary" onclick={() => handleUpload()} type="button">
								<Icon class="w-5 h-5" data={faSave} />
								근태정보 등록
							</button>
						</div>
						<div>
							<form method="dialog">
								<!-- if there is a button in form, it will close the modal -->
								<button
									class="btn btn-warning tooltip tooltip-left"
									data-tip="키보드의 Escape 키를 누르면 닫힙니다."
								>
									<Icon class="w-5 h-5" data={faXmark} />
									닫기
								</button>
							</form>
						</div>
					</div>
				</div>
			</dialog>
		</div>
	{/snippet}
</AppLayout>
