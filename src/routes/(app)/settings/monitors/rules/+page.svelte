<script lang="ts">
	import { getUser } from '$lib/User';

	const user = getUser();

	import type { Breadcrumb } from '$lib/types/types';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import AppLayout from '$components/Layouts/AppLayout.svelte';

	const breadcrumbs: Breadcrumb[] = [
		{ title: '설정', url: '/settings/monitors/rules' },
		{ title: '모니터', url: '/settings/monitors/rules' },
		{ title: '정책 설정', url: '/settings/monitors/rules' },
	];
</script>

<svelte:head>
	<title>설정 > 모니터 > 정책 설정</title>
</svelte:head>

<AppLayout {user}>
	{#snippet main()}
		<div >
			<TitleBar {breadcrumbs} />
			
			<section class="main-section">
				모니터 정책 관리 페이지
			</section>
		</div>
	{/snippet}
</AppLayout>