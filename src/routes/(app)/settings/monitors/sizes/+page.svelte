<script lang="ts">
	import { getUser } from '$lib/User';

	import type { Breadcrumb } from '$lib/types/types';
  import { monitorSizeStore, loadMonitorSizes, saveMonitorSizeItem } from '$lib/stores/monitorSizeStore';
  import { onMount } from 'svelte';
  import { page } from '$app/state';
  import { toast } from 'svoast';

	import TitleBar from '$components/UI/TitleBar.svelte';
	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import MonitorSizeSearchForm from '$lib/components/Monitor/MonitorSizeSearchForm.svelte';
	import TableTop from '$components/UI/TableTop.svelte';
	import MonitorSizeTable from '$lib/components/Monitor/MonitorSizeTable.svelte';
	import Paginate from '$lib/components/UI/Paginate.svelte';

	const user = getUser();

  // 검색/페이지 상태
  const localUrl = page.url.pathname;
  let isLoading = $state(false);
  let p = $state(page.url.searchParams.get('p') || '1');
  let pageSize = $state(page.url.searchParams.get('pageSize') || '16');
  let searchParams = $state('');
  let apiSearchParams = $state('');

  let params = $state({
    name: page.url.searchParams.get('name') ?? '',
    brand: (page.url.searchParams.get('brand') as any) ?? '',
    unit: (page.url.searchParams.get('unit') as any) ?? '',
    min_size: page.url.searchParams.get('min_size') ?? '',
    max_size: page.url.searchParams.get('max_size') ?? ''
  });

  // 정렬 상태
  let sortBy = $state<'name' | 'updated_at'>(
    (page.url.searchParams.get('sortBy') as 'name' | 'updated_at') || 'updated_at'
  );
  let sortDir = $state<'asc' | 'desc'>(
    (page.url.searchParams.get('sortDir') as 'asc' | 'desc') || 'desc'
  );

  function buildAndLoad(search = false) {
    if (search) p = '1';
    const qsLocal = new URLSearchParams({ p, pageSize });
    const apiQS = new URLSearchParams({ page: p, pageSize });

    if (params.name) { qsLocal.set('name', params.name); apiQS.set('name', params.name); }
    if (params.brand) { qsLocal.set('brand', String(params.brand)); apiQS.set('brand', String(params.brand)); }
    if (params.unit) { qsLocal.set('unit', String(params.unit)); apiQS.set('unit', String(params.unit)); }
    if (params.min_size) { qsLocal.set('min_size', String(params.min_size)); apiQS.set('min_size', String(params.min_size)); }
    if (params.max_size) { qsLocal.set('max_size', String(params.max_size)); apiQS.set('max_size', String(params.max_size)); }

    // 정렬 파라미터 추가
    qsLocal.set('sortBy', sortBy);
    qsLocal.set('sortDir', sortDir);
    apiQS.set('sortBy', sortBy);
    apiQS.set('sortDir', sortDir);

    searchParams = qsLocal.toString();
    apiSearchParams = apiQS.toString();

    isLoading = true;
    loadMonitorSizes(Object.fromEntries(apiQS.entries())).finally(() => (isLoading = false));
  }

  function handleSearch(payload: any) {
    params = { ...params, ...payload };
    buildAndLoad(true);
  }

  function handleSort(field: 'name' | 'updated_at' | 'created_at') {
    if (sortBy === field) {
      sortDir = sortDir === 'asc' ? 'desc' : 'asc';
    } else {
      sortBy = field;
      sortDir = 'asc';
    }
    buildAndLoad();
  }

  function handlePageChange(pageNum: number) {
    p = String(pageNum);
    buildAndLoad();
  }

  async function handleSave(id: number, payload: any) {
    try {
      await saveMonitorSizeItem(id, payload);
      toast.success('모니터 사이즈가 수정되었습니다.');
    } catch (e) {
      // handleCatch에서 처리됨
    }
  }

  onMount(() => {
    buildAndLoad();
  });

	const breadcrumbs: Breadcrumb[] = [
		{ title: '설정', url: '/settings/monitors/sizes' },
		{ title: '모니터', url: '/settings/monitors/sizes' },
		{ title: '사이즈 설정', url: '/settings/monitors/sizes' },
	];
</script>

<svelte:head>
	<title>설정 > 모니터 > 사이즈 설정</title>
</svelte:head>

<AppLayout {user}>
	{#snippet main()}
		<div >
			<TitleBar {breadcrumbs} />
			
			<section class="main-section">
        <div class="space-y-3">
          <MonitorSizeSearchForm params={params} onSearch={handleSearch} />

  <TableTop onUpdate={(ev: CustomEvent<{ pageSize: string }>) => { const { pageSize: ps } = ev.detail; pageSize = ps; buildAndLoad(true); }} total={$monitorSizeStore.pageTotal ?? 0} pageSize={pageSize} />

          <MonitorSizeTable 
            items={$monitorSizeStore.items ?? []}
            startNo={$monitorSizeStore.pageStartNo ?? 0}
            onSave={handleSave}
            sortBy={sortBy}
            sortDir={sortDir}
            onSort={handleSort}
            onError={(message: string) => toast.error(message)}
          />

          {#if ($monitorSizeStore.pageTotal ?? 0) > 0}
            <Paginate 
              store={monitorSizeStore}
              {localUrl}
              onPageChange={handlePageChange}
              {searchParams}
            />
          {/if}
        </div>
			</section>
		</div>
	{/snippet}
</AppLayout>