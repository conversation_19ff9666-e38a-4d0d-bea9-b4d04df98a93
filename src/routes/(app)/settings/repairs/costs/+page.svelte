<script lang="ts">
	import { getUser } from '$lib/User';

	const user = getUser();
	
	import type { Breadcrumb } from '$lib/types/types';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import AppLayout from '$components/Layouts/AppLayout.svelte';

	const breadcrumbs: Breadcrumb[] = [
		{ title: '설정', url: '/settings/repairs/costs' },
		{ title: '수리비 설정', url: '/settings/repairs/costs' },
	];
</script>

<svelte:head>
	<title>설정 > 수리비 설정</title>
</svelte:head>

<AppLayout {user}>
	{#snippet main()}
		<div >
			<TitleBar {breadcrumbs} />
			
			<section class="main-section">
				수리비 정책 관리 페이지
			</section>
		</div>
	{/snippet}
</AppLayout>