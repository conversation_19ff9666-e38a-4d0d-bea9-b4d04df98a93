<script lang="ts">
	import { getUser } from '$lib/User';
	import { onMount } from 'svelte';
	import { page } from '$app/state';
	import type { Breadcrumb } from '$lib/types/types';

	import { authClient } from '$lib/services/AxiosBackend';
	import { toast } from 'svoast';

	import { executeAsk, executeMessage, getNumberFormat, handleCatch } from '$lib/Functions';
	import { loadRepairFee, repairFeeStore } from '$stores/repairStore';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import TableTop from '$components/UI/TableTop.svelte';
	import Paginate from '$components/UI/Paginate.svelte';

	import Icon from 'svelte-awesome';
	import { faPlus } from '@fortawesome/free-solid-svg-icons/faPlus';
	import { faPenToSquare } from '@fortawesome/free-regular-svg-icons/faPenToSquare';
	import { faTrashCan } from '@fortawesome/free-regular-svg-icons/faTrashCan';
	import { faSearch } from '@fortawesome/free-solid-svg-icons/faSearch';

	const user = getUser();

	const apiUrl = '/api/wms/settings/repairs/fees';
	const localUrl = page.url.pathname;
	let isLoading = $state(false); // 로딩중 표시 여부

	// 검색관련 시작==========
	let p = $state(page.url.searchParams.get('p') || '1');
	let pageSize = $state(page.url.searchParams.get('pageSize') || '16');
	let searchParams = $state('');
	let apiSearchParams = $state('');

	let searchOptions = $state([]);
	let categories = $state([]); // 전체 카테고리
	let cate4 = $state(''); // 4차 카테고리(선택)
	let cate5 = $state(''); // 5차 카테고리(선택)
	let repairRangeTypes = $state([]); // 수리비 분류(모니터, 일반, 애플)
	let repair_range_type = $state('');
	let repairRangeModels = $state([]); // 수리비 모델(브랜드(삼성, 엘지), 맥북, 아이맥, 기타)
	let repair_range_model = $state('');
	let repairRangeFeeTypes = $state([]); // 가격 기준(크기(size), 판매가(price), 공통(none))
	let repair_range_fee_type = $state('');
	let repairRangeFeeUnits = $state([]); // 수리비 청구 단위(won, cm, inch)
	let repair_range_fee_unit = $state('');
	let repairTypes = $state([]); // 수리 유형(check, repair, os_reinstall)
	let repair_range_min_value = $state(0);
	let repair_range_max_value = $state(0);
	let repair_type = $state('');
	let repair_amount = $state(0);
	let startNo = $state(0);
	// 검색관련 끝==========

	async function makeData(search = false) {
		isLoading = true;

		if (search) {
			p = '1';
		}

		const common_params = {
			cate4: cate4,
			cate5: cate5,
			repair_range_type: repair_range_type,
			repair_range_model: repair_range_model,
			repair_range_fee_type: repair_range_fee_type,
			repair_range_fee_unit: repair_range_fee_unit,
			repair_type: repair_type,
			pageSize: pageSize
		};

		const params = new URLSearchParams({ p: p, ...common_params });
		const api_params = new URLSearchParams({ page: p, ...common_params });

		searchParams = params.toString(); // local
		apiSearchParams = api_params.toString(); // api

		await loadRepairFee(`${apiUrl}?${apiSearchParams}`, user);

		if ($repairFeeStore) {
			startNo = $repairFeeStore.pageStartNo ?? 0;
		}

		isLoading = false;
	}

	/**
	 * 페이지 변경 핸들러 (최적화된 페이지네이션용)
	 */
	function handlePageChange(page: number) {
		p = page.toString();
		makeData();
	}

	function changeSearchParams(value: any) {
		if (value.detail.p) p = value.detail.p;
		if (value.detail.pageSize) pageSize = value.detail.pageSize;

		makeData();
	}

	/**
	 * 검색할 필드를 서버로부터 가져온다.
	 */
	async function getSearchOptions() {
		try {
			const { status, data } = await authClient.get(`${apiUrl}/options`);
			if (status === 200 && data.success) {
				searchOptions = data.data.options;

				categories = searchOptions.categories;
				repairRangeTypes = searchOptions.repair_range_types;
				repairRangeModels = searchOptions.repair_range_models;
				repairRangeFeeTypes = searchOptions.repair_range_fee_types;
				repairRangeFeeUnits = searchOptions.repair_range_fee_units;
				repairTypes = searchOptions.repair_types;
			}
		} catch (e) {
			await handleCatch(e);
		}
	}

	// 수리비 등록
	async function handleStore() {
		if (!cate4) {
			await executeMessage('[4차 카테고리]를 선택해 주세요.');
			return false;
		}

		if (!cate5) {
			await executeMessage('[5차 카테고리]를 선택해 주세요.');
			return false;
		}

		if (!repair_range_type) {
			await executeMessage('[분류]를 선택해 주세요.');
			return false;
		}

		if (!repair_range_model) {
			await executeMessage('[모델]을 선택해 주세요.');
			return false;
		}

		if (!repair_range_fee_type) {
			await executeMessage('[가격 기준]을 선택해 주세요.');
			return false;
		}

		if (!repair_range_fee_unit) {
			await executeMessage('[단위]을 선택해 주세요.');
			return false;
		}

		if (!repair_type) {
			await executeMessage('[청구 유형]을 선택해 주세요.');
			return false;
		}

		if (repair_range_min_value < 0) {
			await executeMessage('시작(이상) 값을 입력해 주세요.');
			return false;
		}

		if (repair_range_max_value < 0 || repair_range_max_value > 1000000000) {
			await executeMessage('종료(미만) 값을 입력해 주세요.');
			return false;
		}

		if (!repair_amount) {
			await executeMessage('수리비를 입력해 주세요.');
			return false;
		}

		try {
			const payload = {
				cate4,
				cate5,
				repair_range_type,
				repair_range_model,
				repair_range_fee_type,
				repair_range_fee_unit,
				repair_range_min_value,
				repair_range_max_value,
				repair_type,
				repair_amount
			};
			const { status, data } = await authClient.post(`${apiUrl}`, payload);

			if (status === 200 && data.success) {
				toast.success('저장 되었습니다.');

				repair_range_min_value = 0;
				repair_range_max_value = 0;
				repair_amount = 0;

				await makeData();
			}
		} catch (e) {
			await handleCatch(e);
		}
	}

	// 작업 상세 분류 수정
	async function handleUpdate(item: any) {
		if (item.min_value < 0) {
			await executeMessage('최소 금액을 0보다 큰 수로 입력해 주세요.');

			return false;
		}

		if (item.max_value < 0) {
			await executeMessage('최대 금액을 0보다 큰 수로 입력해 주세요.');

			return false;
		}

		if (item.max_value < 0) {
			await executeMessage('수리비를 0보다 큰 수로 입력해 주세요.');

			return false;
		}

		try {
			const payload = {
				range_id: item.repair_fee_range_id,
				min_value: item.min_value,
				max_value: item.max_value,
				fee_id: item.id,
				amount: item.amount
			};
			const { status, data } = await authClient.put(`${apiUrl}`, payload);

			if (status === 200 && data.success) {
				toast.success('수정 되었습니다.');
				await makeData();
			} else {
				await executeMessage(data.data.message, 'error');
			}
		} catch (e) {
			await handleCatch(e);
		}
	}

	// 작업 상세 분류 삭제
	async function handleDelete(id: number) {
		const ask = await executeAsk(
			'현재 수리비를 정말 삭제하시겠습니까?\n\n수리비가 삭제되면 기본 수리비가 적용됩니다.'
		);

		if (!ask) {
			return false;
		}

		try {
			const { status, data } = await authClient.delete(`${apiUrl}/${id}`);

			if (status === 200 && data.success) {
				toast.success('삭제 되었습니다.');

				await makeData();
			} else {
				await executeMessage(data.data.message, 'error');
			}
		} catch (e) {
			await handleCatch(e);
		}
	}

	onMount(async () => {
		repairFeeStore.set({});
		await getSearchOptions();
	});

	const breadcrumbs: Breadcrumb[] = [
		{ title: '설정', url: '/settings/repairs/fees' },
		{ title: '수리비 설정', url: '/settings/repairs/fees' }
	];
</script>

<svelte:head>
	<title>설정 > 수리비 설정</title>
</svelte:head>

<AppLayout {user}>
	{#snippet main()}
		<div>
			<TitleBar {breadcrumbs} />

			<section class="main-section">
				<div class="w-full px-3 flex flex-col items-center justify-center xl:justify-between">
					<div class="w-full flex py-1.5 space-y-3 md:space-y-0 md:space-x-3">
						<div class="w-full p-4">
							<div>
								<Icon data={faSearch} class="mb-2" />

								<select
									bind:value={cate4}
									class="select select-bordered select-sm"
									onchange={async () => {
										cate5 = '';
										repair_range_type = '';
										repair_range_model = '';
										repair_range_fee_type = '';
										repair_range_fee_unit = '';
										repair_type = '';
										await makeData(true);
									}}
								>
									<option value="">4차 카테고리</option>
									{#each categories as item (item.id)}
										<option value={String(item.id)}>{item.name}</option>
									{/each}
								</select>

								<select
									bind:value={cate5}
									class="select select-bordered select-sm"
									onchange={async () => {
										repair_range_type = '';
										repair_range_model = '';
										repair_range_fee_type = '';
										repair_range_fee_unit = '';
										repair_type = '';
										await makeData(true);
									}}
								>
									<option value="">5차 카테고리</option>
									{#if cate4}
										{#each categories.find((item) => String(item.id) === cate4).cate5 as item}
											<option value={String(item.id)}>{item.name}</option>
										{/each}
									{/if}
								</select>

								<select
									bind:value={repair_range_type}
									class="select select-bordered select-sm"
									onchange={async () => {
										repair_range_model = '';
										repair_range_fee_type = '';
										repair_range_fee_unit = '';
										repair_type = '';
										await makeData(true);
									}}
								>
									<option value="">분류</option>
									{#each repairRangeTypes as item (item.id)}
										<option value={String(item.id)}>{item.name}</option>
									{/each}
								</select>

								<select
									bind:value={repair_range_model}
									class="select select-bordered select-sm"
									onchange={async () => {
										repair_range_fee_type = '';
										repair_range_fee_unit = '';
										repair_type = '';
										await makeData(true);
									}}
								>
									<option value="">모델</option>
									{#each repairRangeModels as item (item.id)}
										<option value={String(item.id)}>{item.name}</option>
									{/each}
								</select>

								<select
									bind:value={repair_range_fee_type}
									class="select select-bordered select-sm"
									onchange={async () => {
										repair_range_fee_unit = '';
										repair_type = '';
										await makeData(true);
									}}
								>
									<option value="">가격 기준</option>
									{#each repairRangeFeeTypes as item (item.id)}
										<option value={String(item.id)}>{item.name}</option>
									{/each}
								</select>

								<select
									bind:value={repair_range_fee_unit}
									class="select select-bordered select-sm"
									onchange={async () => {
										repair_type = '';
										await makeData(true);
									}}
								>
									<option value="">단위</option>
									{#each repairRangeFeeUnits as item (item.id)}
										<option value={String(item.id)}>{item.name}</option>
									{/each}
								</select>

								<select
									bind:value={repair_type}
									class="select select-bordered select-sm"
									onchange={async () => {
										await makeData(true);
									}}
								>
									<option value="">청구 유형</option>
									{#each repairTypes as item (item.id)}
										<option value={String(item.id)}>{item.name}</option>
									{/each}
								</select>
							</div>

							<div class="flex pt-2">
								<span class="flex">
									시작값(초과):
									<input
										bind:value={repair_range_min_value}
										class="input input-bordered input-sm w-32 max-w-xs"
										type="number"
									/>
								</span>

								<span class="flex pl-1">
									종료값(이하):
									<input
										bind:value={repair_range_max_value}
										class="input input-bordered input-sm w-32 max-w-xs"
										type="number"
									/>
								</span>

								<span class="flex pl-1">
									수리비(원):
									<input
										bind:value={repair_amount}
										class="input input-bordered input-sm w-32 max-w-xs"
										type="number"
									/>
								</span>

								<span class="flex pl-1">
									<button class="btn btn-primary btn-sm" onclick={handleStore}>
										<Icon data={faPlus} />
										추가
									</button>
								</span>
							</div>
						</div>
					</div>
				</div>

				<div class="px-2">
					<TableTop
						onUpdate={changeSearchParams}
						{pageSize}
						total={$repairFeeStore.pageTotal ?? 0}
					/>

					<table class="table text-xs table-pin-rows table-zebra">
						<thead class="uppercase">
							<tr class="bg-base-content text-base-300 text-center">
								<th>번호</th>
								<th>4차</th>
								<th>5차</th>
								<th>분류</th>
								<th>모델</th>
								<th>가격 기준</th>
								<th>단위</th>
								<th>청구 유형</th>
								<th>초과</th>
								<th>이하</th>
								<th>수리비</th>
								<th></th>
							</tr>
						</thead>

						<tbody>
							{#if $repairFeeStore.items}
								{#each $repairFeeStore.items as item, index}
									<tr class="text-center hover:bg-base-content/10">
										<td>
											{getNumberFormat(startNo - index)}
										</td>
										<td>
											{item.cate4_name}
										</td>
										<td>
											{item.cate5_name}
										</td>
										<td>
											{item.range_type}
										</td>
										<td>
											{item.range_model}
										</td>
										<td>
											{item.range_fee_type}
										</td>
										<td>
											{item.range_fee_unit}
										</td>
										<td>
											{item.repair_type}
										</td>
										<td>
											<input
												bind:value={item.min_value}
												class="input input-bordered input-sm w-28 max-w-xs text-right"
												min="0"
												type="number"
											/>{item.range_fee_unit}
										</td>
										<td>
											<input
												bind:value={item.max_value}
												class="input input-bordered input-sm w-28 max-w-xs text-right"
												min="0"
												max="1000000000"
												type="number"
											/>{item.range_fee_unit}
										</td>
										<td>
											<input
												bind:value={item.amount}
												class="input input-bordered input-sm w-28 max-w-xs text-right"
												min="0"
												max="1000000000"
												type="number"
											/>원
										</td>
										<td>
											<button
												class="btn btn-ghost btn-sm px-1 py-0 m-0"
												onclick={() => handleUpdate(item)}
											>
												<Icon data={faPenToSquare} />
											</button>

											<button
												class="btn btn-ghost btn-sm px-1 py-0 m-0"
												onclick={() => handleDelete(String(item.id))}
											>
												<Icon data={faTrashCan} />
											</button>
										</td>
									</tr>
								{/each}
							{/if}
						</tbody>
					</table>

					<!-- Pagination -->
					{#if $repairFeeStore.pageTotal && $repairFeeStore.pageTotal > 0}
						<Paginate
							store={repairFeeStore}
							{localUrl}
							onPageChange={handlePageChange}
							{searchParams}
						/>
					{/if}
				</div>
			</section>
		</div>
	{/snippet}
</AppLayout>
