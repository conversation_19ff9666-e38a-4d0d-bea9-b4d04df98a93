<script lang="ts">
	import { getUser } from '$lib/User';
	import { onMount } from 'svelte';
	import { page } from '$app/state';
	import type { Breadcrumb } from '$lib/types/types';

	import { authClient } from '$lib/services/AxiosBackend';
	import { toast } from 'svoast';

	import { executeAsk, executeMessage, handleCatch } from '$lib/Functions';
	import { loadStatuses, statusStore } from '$stores/statusStore';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';

	import Icon from 'svelte-awesome';
	import { faPlus } from '@fortawesome/free-solid-svg-icons/faPlus';
	import { faPenToSquare } from '@fortawesome/free-regular-svg-icons/faPenToSquare';
	import { faTrashCan } from '@fortawesome/free-regular-svg-icons/faTrashCan';

	const user = getUser();

	const apiUrl = '/api/wms/settings/statuses';
	const localUrl = page.url.pathname;
	let isLoading = $state(false); // 로딩중 표시 여부

	// 검색관련 시작==========
	let statusCode = $state('');
	let statusName = $state('');
	let statusDescription = $state('');

	let p = $state(page.url.searchParams.get('p') || '1');
	let pageSize = $state(page.url.searchParams.get('pageSize') || '20');
	let apiSearchParams = '';

	// 검색관련 끝==========

	async function makeData() {
		isLoading = true;

		const common_params = {
			code: statusDescription,
			name: statusName,
			pageSize: pageSize
		};

		const api_params = new URLSearchParams({ page: p, ...common_params });

		apiSearchParams = api_params.toString(); // api

		await loadStatuses(`/resources/statuses?${apiSearchParams}`, user);

		isLoading = false;
	}

	// 작업 상세 분류 등록
	async function handleStatusCreate() {
		if (!statusCode) {
			await executeMessage('상세 분류 코드를 입력해 주세요.');
			return false;
		}

		if (!statusName) {
			await executeMessage('상세 분류 코드에 대한 이름을 입력해 주세요.');
			return false;
		}

		try {
			const payload = {
				code: statusCode,
				name: statusName,
				description: statusDescription
			};
			const { status, data } = await authClient.post(`${apiUrl}`, payload);

			if (status === 200 && data.success) {
				toast.success('저장 되었습니다.');

				statusCode = '';
				statusName = '';
				statusDescription = '';

				await makeData();
			} else {
				await executeMessage(data.data.message, 'error');
			}
		} catch (e) {
			await handleCatch(e);
		}
	}

	// 작업 상세 분류 수정
	async function handleValueUpdate(item: any) {
		if (!item.code) {
			await executeMessage('상세 분류 코드를 입력해 주세요.');

			return false;
		}

		if (!item.name) {
			await executeMessage('상세 분류 코드에 대한 이름을 입력해 주세요.');

			return false;
		}

		try {
			const payload = {
				code: item.code,
				name: item.name,
				description: item.description
			};
			const { status, data } = await authClient.put(`${apiUrl}/${item.id}`, payload);

			if (status === 200 && data.success) {
				toast.success('수정 되었습니다.');

				await makeData();
			}
		} catch (e) {
			await handleCatch(e);
		}
	}

	// 작업 상세 분류 삭제
	async function handleStatusDelete(id: number) {
		const ask = await executeAsk(
			'작업 상세 분류를 정말 삭제하시겠습니까?\n\n상세 분류를 삭제하면 통계 자료를 뽑을 때 사용하는 데이터가 모두 삭제됩니다.\n\n삭제 전 프로그래머와 상의해 주세요.'
		);

		if (!ask) {
			return false;
		}

		try {
			const { status, data } = await authClient.delete(`${apiUrl}/${id}`);

			if (status === 200 && data.success) {
				toast.success('삭제 되었습니다.');

				await makeData();
			}
		} catch (e) {
			await handleCatch(e);
		}
	}

	onMount(async () => {
		await makeData();
	});

	const breadcrumbs: Breadcrumb[] = [
		{ title: '설정', url: '/settings/statuses' },
		{ title: '작업 세부 설정', url: '/settings/statuses' }
	];
</script>

<svelte:head>
	<title>설정 > 작업 세부 설정</title>
</svelte:head>

<AppLayout {user}>
	{#snippet main()}
		<div>
			<TitleBar {breadcrumbs} />

			<section class="main-section">
				<div
					class="w-full px-3 flex flex-col-reverse lg:flex-row items-center justify-center xl:justify-between"
				>
					<div class="w-full flex py-1.5 space-y-3 md:space-y-0 md:space-x-3">
						<div class="w-full p-4">
							<h2 class="text-xl font-bold">작업 상세 분류</h2>

							<div class="flex flex-col sm:flex-row">
								<div class="sm:ml-2">
									<input
										bind:value={statusCode}
										id="value_code"
										class="input input-bordered input-sm w-16 rounded-lg"
										placeholder="코드"
										type="text"
									/>
								</div>

								<div class="sm:ml-2">
									<input
										bind:value={statusName}
										id="value_name"
										class="input input-bordered input-sm w-44 rounded-lg"
										onkeydown={async (e) => {
											if (e.key === 'Enter') {
												await handleStatusCreate();
											}
										}}
										placeholder="코드명(한글, 영문 등)"
										type="text"
									/>
								</div>

								<div class="sm:ml-2">
									<input
										bind:value={statusDescription}
										id="value_description"
										class="input input-bordered input-sm w-[450px] min-w-96 max-w-[500px] rounded-lg"
										onkeydown={async (e) => {
											if (e.key === 'Enter') {
												await handleStatusCreate();
											}
										}}
										placeholder="코드 설명"
										type="text"
									/>
								</div>

								<div class="sm:ml-2">
									<button
										class="btn btn-primary btn-sm p-1.5 w-16"
										onclick={async () => {
											await handleStatusCreate();
										}}
										type="button"
									>
										<Icon data={faPlus} />
										추가
									</button>
								</div>
							</div>

							<div>코드와 코드명(한글)은 이미 등록되어 있는 내용과 중복되면 안 됩니다.</div>

							<div class="pt-4">
								내역
								{#if $statusStore}
									{#each $statusStore as item, index}
										<div class="flex flex-col sm:flex-row py-1">
											<div class="sm:ml-2">
												<input
													bind:value={item.code}
													id="value_code_{index}"
													class="input input-bordered input-sm w-16 rounded-lg"
													placeholder="코드"
													type="text"
												/>
											</div>

											<div class="sm:ml-2">
												<input
													bind:value={item.name}
													id="value_name_{index}"
													class="input input-bordered input-sm w-44 rounded-lg"
													placeholder="코드명(한글, 영문 등)"
													type="text"
												/>
											</div>

											<div class="sm:ml-2">
												<input
													bind:value={item.description}
													id="value_description_{index}"
													class="input input-bordered input-sm w-[450px] min-w-96 max-w-[500px] rounded-lg"
													placeholder="코드 설명"
													type="text"
												/>
											</div>

											<div class="sm:ml-2 flex">
												<span
													class="btn btn-ghost btn-sm p-1.5 tooltip tooltip-top"
													data-tip="수정"
													role="presentation"
													onclick={async () => {
														await handleValueUpdate(item);
													}}
												>
													<Icon data={faPenToSquare} />
												</span>

												<span
													class="btn btn-ghost btn-sm p-1.5 tooltip tooltip-top"
													data-tip="삭제"
													role="presentation"
													onclick={async () => {
														await handleStatusDelete(item.id);
													}}
												>
													<Icon data={faTrashCan} />
												</span>
											</div>
										</div>
									{/each}
								{/if}
							</div>
						</div>
					</div>
				</div>
			</section>
		</div>
	{/snippet}
</AppLayout>
