<script lang="ts">
	import { getUser, type User } from '$lib/User';
	import type { Breadcrumb } from '$lib/types/types';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';

	let user: User = getUser();

	const breadcrumbs: Breadcrumb[] = [
		{ title: '통계', url: '/dashboard' },
		{ title: '상품별 점검 현황', url: '/statistics/repair-status' },
	];
</script>

<svelte:head>
	<title>통계 > 상품별 점검 현황</title>
</svelte:head>

<AppLayout {user}>
	{#snippet main()}
		<div >
			<TitleBar {breadcrumbs} />
			
			<section class="main-section">
				<div class="w-full px-3 flex flex-col-reverse xl:flex-row items-center justify-center xl:justify-between">
					
					상품별 점검 현황
				
				</div>
			</section>
		</div>
	{/snippet}
</AppLayout>