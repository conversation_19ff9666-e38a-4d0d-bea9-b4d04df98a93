<script lang="ts">
	import { getUser, type User } from '$lib/User';
	import { onMount } from 'svelte';
	import { page } from '$app/state';
	import { goto } from '$app/navigation';
	import type { Breadcrumb } from '$lib/types/types';

	import { openWebviewWindow } from '$lib/services/windowService';
	import { toast } from 'svoast';

	import {
		executeAsk,
		executeMessage,
		formatDateTimeToString,
		formatDateToString,
		getNumberFormat,
		getPayload,
		handleCatch,
		handleDownload
	} from '$lib/Functions';
	import {
		loadWarehousePallets,
		WAREHOUSE_PALLET_STATUS_COMPLETED,
		warehousePalletStore,
		warehousePalletStatuses,
		warehousePalletStatusColor
	} from '$stores/warehousePalletStore';
	import { EXCEL_DOWNLOAD_URL } from '$stores/constant';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import Loading from '$components/Loading/Circle2.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import SearchUI from '$components/UI/SearchUI.svelte';
	import SearchField from '$components/Snippets/SearchField.svelte';
	import SearchFieldTitle from '$components/Snippets/SearchFieldTitle.svelte';
	import SearchFieldContent from '$components/Snippets/SearchFieldContent.svelte';
	import TableTop from '$components/UI/TableTop.svelte';
	import Paginate from '$components/UI/Paginate.svelte';
	import SearchWarehousingDate from '$components/Snippets/SearchWarehousingDate.svelte';
	import ButtonSave from '$components/Button/Save.svelte';
	import ButtonSaveAll from '$components/Button/SaveAll.svelte';
	import SearchButton from '$components/Button/Search.svelte';

	import Icon from 'svelte-awesome';
	import { faPrint } from '@fortawesome/free-solid-svg-icons/faPrint';
	import { faFileExcel } from '@fortawesome/free-regular-svg-icons/faFileExcel';
	import { faBackspace } from '@fortawesome/free-solid-svg-icons/faBackspace';
	import { authClient } from '$lib/services/AxiosBackend';
	import { faPlus } from '@fortawesome/free-solid-svg-icons/faPlus';
	import { faXmark } from '@fortawesome/free-solid-svg-icons/faXmark';

	let user: User = getUser();
	let apiUrl = '/api/wms/warehouse/pallets';
	const localUrl = page.url.pathname;
	let isLoading = $state(false); // 로딩중 표시 여부

	// 검색 query string 시작 ==========
	let beginAt = $state(page.url.searchParams.get('beginAt') ?? '');
	let endAt = $state(page.url.searchParams.get('endAt') ?? '');
	let palletStatus = $state(page.url.searchParams.get('palletStatus') ?? '');
	let keyword = $state(''); // 검색어
	let tmpShippedDate = $state(formatDateToString(new Date()));
	let p = $state(page.url.searchParams.get('p') || '1');
	let pageSize = $state(page.url.searchParams.get('pageSize') || '16');
	let searchParams = $state('');
	let apiSearchParams = $state('');
	let startNo = $state(0);
	// 검색관련 끝==========

	// =============== 페이지 내 필요한 변수들 시작 ===============
	let allChecked = $state(false); // 전체 선택: 체크박스
	let idChecked: [] = $state([]); // 전체 선택: 모든 체크박스의 상태를 참조하는 reactive 변수
	let ids: [] = $state([]); // 전체 선택: 상품의 id를 모으는 변수

	let checkboxes: [] = $state([]); // 선택된 항목 찾기
	let palletIds: number[] = $state([]); // 전체 엑셀 다운로드에 사용될 변수
	let palletStatusGroup: string = $state('');
	// =============== 페이지 내 필요한 변수들 종료 ===============

	/**
	 * API로 부터 데이터를 가져온다.
	 */
	async function makeData() {
		isLoading = true;

		const common_params = {
			exportType: 'pallets',
			beginAt: beginAt,
			endAt: endAt,
			status: palletStatusGroup,
			keyword: keyword,
			pageSize: pageSize
		};

		const params = new URLSearchParams({ p: p, ...common_params });
		const api_params = new URLSearchParams({ page: p, ...common_params });

		searchParams = params.toString(); // local
		apiSearchParams = api_params.toString(); // api

		await loadWarehousePallets(`${apiUrl}?${apiSearchParams}`, user);

		if ($warehousePalletStore) {
			startNo = $warehousePalletStore.pageStartNo ?? 0;
		}

		isLoading = false;
	}

	/**
	 * 자식 컴포넌트에서 변경된 변수의 값을 매칭
	 *
	 * @param value
	 */
	function changeSearchParams(value: any) {
		if (value.detail.p) p = value.detail.p;
		if (value.detail.beginAt) beginAt = value.detail.beginAt;
		if (value.detail.endAt) endAt = value.detail.endAt;
		if (value.detail.pageSize) pageSize = value.detail.pageSize;
		if (value.detail.keyword || value.detail.keyword === '') keyword = value.detail.keyword;

		makeData();
	}

	/**
	 * 페이지 변경 핸들러 (최적화된 페이지네이션용)
	 */
	function handlePageChange(page: number) {
		p = page.toString();
		makeData();
	}

	// 전체 선택
	const toggleAllCheck = (items: any) => {
		allChecked = !allChecked;
		idChecked = items.map(() => allChecked);
		ids = allChecked ? items.map((item: any) => item.id) : [];
	};

	/**
	 * 입고취소::창고에 넣었던 팔레트를 다시 빼는 행위??
	 * @param event
	 */
	async function cancelOut(event: MouseEvent) {
		let palletIds: number[] = [];
		let palletCode;
		let checked_count = 0;

		let id = event.target?.getAttribute('data-id');
		if (id !== null) {
			palletIds.push(id * 1);
			palletCode = event.target?.getAttribute('data-pallet-code');
		} else {
			if (checkboxes.length < 1) {
				alert('선택된 팔레트가 없습니다.');
				return false;
			}

			checkboxes.forEach((checkbox) => {
				if (checkbox && checkbox.checked) {
					if (checked_count === 0) {
						palletCode = checkbox.getAttribute('data-pallet-code');
					}

					id = checkbox.getAttribute('data-pallet-id');
					palletIds.push(id * 1);

					checked_count++;
				}
			});

			if (checked_count > 1) {
				palletCode = palletCode + ' 외 ' + (checked_count - 1) + '개';
			}
		}

		const message = `팔레트[ ${palletCode} ]의 입고를 취소`;
		const ask = await executeAsk(`${message} 하시겠습니까?`);
		if (!ask) {
			return false;
		}

		try {
			const payload = {
				palletIds: palletIds
			};

			const { status, data } = await authClient.put(
				`/api/wms/warehouse/pallets/rollback-shipping`,
				payload
			);
			if (status === 200 && data.success) {
				toast.success(`${message}`);

				await makeData();
			} else {
				const message = data.data.message.replace(/\\n/g, '\n');
				await executeMessage(message);
			}
		} catch (e: any) {
			await handleCatch(e);
		}
	}

	// 기존 함수 - checkoutChecked -> checkOut
	async function checkout(event: MouseEvent) {
		let palletIds: number[] = [];
		let palletCode;
		let checked_count = 0;

		let id = event.target?.getAttribute('data-id');
		if (id !== null) {
			palletIds.push(id * 1);
			palletCode = event.target?.getAttribute('data-pallet-code');
		} else {
			if (checkboxes.length < 1) {
				alert('선택된 팔레트가 없습니다.');
				return false;
			}

			checkboxes.forEach((checkbox) => {
				if (checkbox && checkbox.checked) {
					if (checked_count === 0) {
						palletCode = checkbox.getAttribute('data-pallet-code');
					}

					id = checkbox.getAttribute('data-pallet-id');
					palletIds.push(id * 1);

					checked_count++;
				}
			});

			if (checked_count > 1) {
				palletCode = palletCode + ' 외 ' + (checked_count - 1) + '개';
			}
		}

		const message = `팔레트[ ${palletCode} ]를 입고`;
		const ask = await executeAsk(`${message} 하시겠습니까?`);
		if (!ask) {
			return false;
		}

		try {
			const payload = {
				palletIds: palletIds
			};

			const { status, data } = await authClient.post(`/api/wms/warehouse/pallets/shipped`, payload);
			if (status === 200 && data.success) {
				toast.success(`${message}`);

				await makeData();
			} else {
				const message = data.data.message.replace(/\\n/g, '\n');
				await executeMessage(message);
			}
		} catch (e: any) {
			await handleCatch(e);
		}
	}

	// 선택된 팔레트 번호를 인쇄
	function printCheckedPalletNumber() {
		console.log('checkboxes', checkboxes);
		if (ids.length < 1) {
			alert('선택된 팔레트가 없습니다. 인쇄할 팔레트를 선택해주세요.');
			return false;
		}

		let checked_count = 0;
		let export_date = '';
		let pallet_code_list = '';

		checkboxes.forEach((checkbox) => {
			if (checkbox.checked) {
				if (checked_count === 0) {
					export_date = checkbox.getAttribute('data-export-date');
					pallet_code_list += checkbox.getAttribute('data-pallet-code');
				} else {
					pallet_code_list += '|' + checkbox.getAttribute('data-pallet-code');
				}

				checked_count++;
			}
		});

		if (checked_count > 0) {
			let url = `/print/pallets?import_date=${export_date}&pallet_number_list=${pallet_code_list}`;

			openWebviewWindow(url, 'Print');
		}
	}

	function printPalletNumber(pallet_number: string, imported_date: string) {
		let shipped_date =
			imported_date !== null ? formatDateTimeToString(imported_date) : tmpShippedDate;

		let url = `/print/warehouse?pallet_number_list=${pallet_number}&import_date=${shipped_date}`;
		openWebviewWindow(url, 'Print');
	}

	onMount(async () => {
		warehousePalletStore.set('');

		await makeData();
	});

	const breadcrumbs: Breadcrumb[] = [
		{ title: '입고', url: '/works/inspections/pallets' },
		{ title: '입고 팔레트 목록', url: '/works/inspections/pallets' }
	];
</script>

<svelte:head>
	<title>입고 > 입고 팔레트 목록</title>
</svelte:head>

{#if isLoading}
	<Loading size="60" unit="px" />
{/if}

<AppLayout {user}>
	{#snippet main()}
		<div>
			<TitleBar {breadcrumbs} />

			<section class="main-section">
				<SearchUI>
					{#if palletStatus === WAREHOUSE_PALLET_STATUS_COMPLETED}
						<SearchWarehousingDate
							{beginAt}
							{endAt}
							onUpdate={changeSearchParams}
							title="입고날짜"
						/>
					{/if}
					<SearchField>
						<SearchFieldTitle title="팔레트 상태" />
						<SearchFieldContent>
							<label class="cursor-pointer pl-2">
								<input
									bind:group={palletStatusGroup}
									class="radio-success radio-sm"
									onchange={async () => {
										palletStatus = palletStatusGroup;
										await makeData();
									}}
									type="radio"
									value=""
								/> 전체
							</label>
							{#each $warehousePalletStatuses as status}
								<label class="cursor-pointer pl-2">
									<input
										bind:group={palletStatusGroup}
										class="radio-success radio-sm"
										onchange={async () => {
											palletStatus = palletStatusGroup;
											await makeData();
										}}
										type="radio"
										value={status.value}
									/>
									{status.text}
								</label>
							{/each}
						</SearchFieldContent>
					</SearchField>

					<SearchField>
						<SearchFieldTitle title="팔레트 번호/QAID" />
						<SearchFieldContent>
							<label class="input input-sm input-bordered flex items-center justify-center gap-2">
								<input
									bind:value={keyword}
									class="grow bg-base-100"
									onkeydown={async (e) => {
										if (e.key === 'Enter') {
											await makeData();
										}
									}}
									placeholder="팔레트 번호, QAID 검색"
									type="text"
								/>

								<span
									onclick={async () => {
										keyword = '';
										await makeData();
									}}
									role="presentation"
								>
									<Icon class="cursor-pointer" data={faXmark} />
								</span>
							</label>

							<SearchButton
								onclick={makeData}
								tooltipData="검색"
								tooltipDirection="tooltip-bottom"
								useTooltip={true}
							/>

							<ButtonSaveAll
								onclick={async (e) => {
									e.preventDefault();
									isLoading = true;

									const payload = getPayload(apiSearchParams);

									palletIds = [];
									$warehousePalletStore.items.forEach((item) => {
										palletIds.push(item.id);
									});
									payload.palletIds = palletIds;

									if (beginAt && endAt) {
										payload.beginAt = beginAt;
										payload.endAt = endAt;
									}

									await handleDownload(EXCEL_DOWNLOAD_URL, payload);
									isLoading = false;
								}}
								tooltipData="전체 저장"
								useTooltip={true}
							/>

							<ButtonSave
								onclick={async (e) => {
									e.preventDefault();
									isLoading = true;

									if (ids.length < 1) {
										await executeMessage(
											'선택된 팔레트가 없습니다.\n다운로드할 팔레트를 선택해 주세요.'
										);
										isLoading = false;
										return false;
									}

									const payload = getPayload(apiSearchParams);
									payload.palletIds = ids;

									await handleDownload(EXCEL_DOWNLOAD_URL, payload);
									isLoading = false;
								}}
								tooltipData="선택 저장"
								useTooltip={true}
							/>

							<button
								class="btn btn-info btn-sm ml-4 tooltip tooltip-bottom"
								data-tip="선택된 팔레트의 번호를 출력합니다."
								onclick={printCheckedPalletNumber}
								type="button"
							>
								<Icon data={faPrint} />
								선택 팔레트번호 인쇄
							</button>
						</SearchFieldContent>
					</SearchField>

					<SearchField>
						<SearchFieldTitle title="임시 입고일" />
						<SearchFieldContent>
							<input
								bind:value={tmpShippedDate}
								class="input input-sm input-bordered w-36 focus:ring-0 focus:outline-none"
								id="tmp_export_date"
								name="tmp_export_date"
								type="date"
							/> <span class="text-xs">* 임시 입고일은 <u>출력시</u>에만 사용됩니다.</span>
						</SearchFieldContent>
					</SearchField>
				</SearchUI>

				<!-- 리스트 시작 -->
				<div class="overflow-x-auto px-2">
					<TableTop
						onUpdate={changeSearchParams}
						{pageSize}
						total={$warehousePalletStore.pageTotal ?? 0}
					/>

					<table class="table text-xs table-pin-rows">
						<thead class="uppercase">
							<tr class="bg-base-content text-base-300 text-center">
								<th>
									<input
										checked={allChecked}
										onchange={() => toggleAllCheck($warehousePalletStore.items)}
										type="checkbox"
									/>
								</th>
								<th>번호</th>
								<th>팔레트 번호</th>
								<th>적재수량</th>
								<th>총 금액</th>
								<th>상태</th>
								<th>작업자</th>
								<th>상품 추가</th>
								<th>엑셀저장</th>
								<th>입고일자</th>
								<th>창고 위치</th>
								<th>츨고일자</th>
							</tr>
						</thead>

						<tbody>
							{#if $warehousePalletStore.items}
								{#each $warehousePalletStore.items as item, index}
									<tr class="hover:bg-base-content/10">
										<td class="text-center">
											<input type="hidden" name="pallet_id" value={item.id} />
											<input
												bind:checked={idChecked[index]}
												bind:group={ids}
												bind:this={checkboxes[index]}
												value={item.id}
												data-pallet-id={item.id}
												data-pallet-code={item.pallet_number}
												data-shipped-date=""
												type="checkbox"
											/>
										</td>
										<td class="p-1 text-center">
											{getNumberFormat(startNo - index)}
										</td>
										<td class="p-2 text-center">
											<button
												class="btn btn-ghost btn-xs"
												onclick={() => {
													printPalletNumber(item.pallet_number, item.shipped_at);
												}}
											>
												{item.pallet_number}
												<Icon data={faPrint} />
											</button>
										</td>
										<td class="p-1 text-right">
											<a href="/works/inspections/items?pallet_id={item.id}">
												{getNumberFormat(item.items_count)}
											</a>
										</td>
										<td class="p-1 text-right">
											<a href="/works/inspections/items?pallet_id={item.id}">
												{getNumberFormat(item.amount)}
											</a>
										</td>
										<td class="p-1 text-center">
											{@html warehousePalletStatusColor(item.status)}
										</td>
										<td class="p-1 text-center">
											{item.created_by}
										</td>
										<td class="p-1 text-center">
											{#if item.status === WAREHOUSE_PALLET_STATUS_COMPLETED}
												<button
													class="btn btn-error btn-xs"
													data-pallet-code={item.pallet_number}
													data-id={item.id}
													onclick={cancelOut}
													type="button"
												>
													<Icon data={faBackspace} />
													입고취소
												</button>
											{:else}
												<button
													class="btn btn-info btn-xs"
													data-pallet-code={item.pallet_number}
													onclick={() =>
														goto(`/works/inspections/loading/?pallet_number=${item.pallet_number}`)}
													type="button"
												>
													<Icon data={faPlus} />
													상품추가
												</button>
											{/if}
										</td>
										<td class="p-1 text-center">
											<button
												class="btn btn-success btn-xs"
												data-id={item.id}
												onclick={async (e) => {
													e.preventDefault();
													isLoading = true;

													const payload = getPayload(apiSearchParams);
													payload.palletCode = item.pallet_number;
													payload.palletIds = [item.id];

													await handleDownload(EXCEL_DOWNLOAD_URL, payload);
													isLoading = false;
												}}
												type="button"
											>
												<Icon data={faFileExcel} />
												저장
											</button>
										</td>
										<td class="text-center">
											{#if item.shipped_in_at !== null}
												{item.shipped_in_at}<br />
												{item.shipped_in_by}
											{:else}
												-
											{/if}
										</td>
										<td class="text-center">
											{#if item.location}
												{item.location.name}
											{:else}
												-
											{/if}
										</td>
										<td class="text-center">
											{#if item.shipped_out_at !== null}
												{item.shipped_out_at}<br />
												{item.shipped_out_by}
											{:else}
												-
											{/if}
										</td>
									</tr>
								{/each}
							{/if}
						</tbody>
					</table>
				</div>

				{#if $warehousePalletStore.pageTotal && $warehousePalletStore.pageTotal > 0}
					<!-- Pagination -->
					<Paginate
						store={warehousePalletStore}
						{localUrl}
						onPageChange={handlePageChange}
						{searchParams}
					/>
				{/if}
			</section>
		</div>
	{/snippet}
</AppLayout>
