<script lang="ts">
	import type { Breadcrumb } from '$lib/types/types';

	import { getUser, type User } from '$lib/User';
	import { onMount, tick } from 'svelte';
	import { page } from '$app/state';

	import { authClient } from '$lib/services/AxiosBackend';
	import { toast } from 'svoast';

	import {
		executeAsk,
		executeMessage,
		formatDateTimeToFullString,
		formatDateTimeToString,
		getNumberFormat,
		getPayload,
		handleCatch,
		handleDownload
	} from '$lib/Functions';
	import { getProductCheckedStatusName, loadProduct, productStore } from '$stores/productStore';
	import { getProcessGradeColorButton } from '$stores/processStore';
	import { getPalletNo } from '$stores/palletStore';
	import { EXCEL_DOWNLOAD_URL } from '$stores/constant';

	import AppLayout from '$components/Layouts/AppLayout.svelte';
	import Loading from '$components/Loading/Circle2.svelte';
	import TitleBar from '$components/UI/TitleBar.svelte';
	import SearchUI from '$components/UI/SearchUI.svelte';
	import TableTop from '$components/UI/TableTop.svelte';
	import Paginate from '$components/UI/Paginate.svelte';
	import SearchProductStatus from '$components/Snippets/SearchProductStatus.svelte';
	import SearchProcessGrade from '$components/Snippets/SearchProcessGrade.svelte';
	import SearchCategory from '$components/Snippets/SearchCategory.svelte';
	import SearchQaid from '$components/Snippets/SearchQaid.svelte';
	import ButtonReset from '$components/Button/Reset.svelte';
	import ButtonExcelDownload from '$components/Button/ExcelDownload.svelte';
	import ButtonSelectedDelete from '$components/Button/SelectedDelete.svelte';
	import DisplayKeyword from '$components/Snippets/DisplayKeyword.svelte';

	import Icon from 'svelte-awesome';
	import { faSave } from '@fortawesome/free-regular-svg-icons/faSave';
	import { faXmark } from '@fortawesome/free-solid-svg-icons/faXmark';
	import { faArrowRight } from '@fortawesome/free-solid-svg-icons/faArrowRight';
	import { faPlus } from '@fortawesome/free-solid-svg-icons/faPlus';
	import { faPenToSquare } from '@fortawesome/free-regular-svg-icons/faPenToSquare';

	let user: User = getUser();
	const apiUrl = '/api/wms/products/unlinked';
	const localUrl = page.url.pathname;
	let isLoading = $state(false); // 로딩중 표시 여부

	// 검색 query string 시작 ==========
	let reqIdSelected = page.url.searchParams.get('reqId') ?? ''; // 완료되지 않은 요청서 목록(선택 값)
	let productStatus = $state('');
	let processCd = $state('');
	let cate4 = $state('');
	let cate5 = $state('');
	let searchType = $state('qaid');
	let keyword = $state(''); // 검색어
	let display_keyword = $state('');
	let p = $state('1'); // page 와 중복된다고...
	let pageSize = $state('16');
	let searchParams = $state('');
	let apiSearchParams = $state('');
	// 검색 query string 종료 ==========

	// 페이징 관련 변수 시작 =============
	let startNo = $state(0);
	// 페이징 관련 변수 종료 =============

	// 모달창: 미등록상품 등록/수정 창 시작========
	let modal: HTMLDialogElement;
	let modalTypeText = $state('등록');

	let modalProductId = $state();
	let modalProductQaid = $state('');
	let modalProductName = $state('');
	let modalProductBarcode = $state('');
	let modalProductAmount = $state(0);
	let modalProductCate4 = $state('');
	let modalProductCate5 = $state('');
	let modalProductVendor = $state('');
	let modalProductMemo = $state('');
	let modalProductStatusSelected = $state('10');
	// 모달창: 미등록상품 등록/수정 창 종료========

	// =============== 페이지 내 필요한 변수들 시작 ===============
	let allChecked = $state(false); // 전체 선택: 체크박스
	let idChecked: [] = $state([]); // 전체 선택: 모든 체크박스의 상태를 참조하는 reactive 변수
	let ids: [] = $state([]); // 전체 선택: 상품의 id를 모으는 변수
	// =============== 페이지 내 필요한 변수들 종료 ===============

	/**
	 * API로 부터 데이터를 가져온다.
	 */
	async function makeData() {
		isLoading = true;

		const common_params = {
			exportType: 'products',
			reqId: reqIdSelected,
			productStatus: productStatus,
			processCd: processCd,
			cate4: cate4,
			cate5: cate5,
			searchType: searchType,
			keyword: display_keyword,
			pageSize: pageSize
		};

		const params = new URLSearchParams({ p: p, ...common_params });
		const api_params = new URLSearchParams({ page: p, ...common_params });

		searchParams = params.toString(); // local
		apiSearchParams = api_params.toString(); // api

		await loadProduct(`${apiUrl}?${apiSearchParams}`, user);

		if ($productStore) {
			startNo = $productStore.pageStartNo ?? 0;
		}

		isLoading = false;
	}

	/**
	 * 페이지 변경 핸들러 (최적화된 페이지네이션용)
	 */
	function handlePageChange(page: number) {
		p = page.toString();
		makeData();
	}

	/**
	 * 자식 컴포넌트에서 변경된 변수의 값을 매칭
	 *
	 * @param value
	 */
	function changeSearchParams(value: any) {
		if (value.detail.p) p = value.detail.p;
		if (value.detail.reqId) reqIdSelected = value.detail.reqId;
		if (value.detail.productStatusGroup || value.detail.productStatusGroup === '')
			productStatus = value.detail.productStatusGroup;
		if (value.detail.processGradeGroup || value.detail.processGradeGroup === '')
			processCd = value.detail.processGradeGroup;
		if (value.detail.cate4 || value.detail.cate4 === '') cate4 = value.detail.cate4;
		if (value.detail.cate5 || value.detail.cate5 === '') cate5 = value.detail.cate5;
		if (value.detail.searchType) searchType = value.detail.searchType;
		if (value.detail.keyword || value.detail.keyword === '') display_keyword = value.detail.keyword;
		if (value.detail.pageSize) pageSize = value.detail.pageSize;

		makeData();
	}

	// 전체 선택
	const toggleAllCheck = (items: any) => {
		allChecked = !allChecked;
		idChecked = items.map(() => allChecked);
		ids = allChecked ? items.map((item: any) => item.id) : [];
	};

	/**
	 * 선택 삭제
	 */
	async function handleDeleteProduct(event: Event) {
		event.preventDefault();

		const deleteCount = ids.length;
		if (deleteCount < 1) {
			await executeMessage('삭제할 상품을 선택해 주세요.');
			return false;
		}

		const result = await executeAsk(
			'점검 대상에서 상품을 삭제합니댜.\n\n선택한 상품들의 등록을 취소(삭제) 하시겠습니까?',
			'warning'
		);
		if (!result) {
			return false;
		}

		try {
			const payload = {
				reqId: reqIdSelected,
				ids: ids
			};

			const { status, data } = await authClient.post(`/api/wms/products/destroy`, payload);
			if (status === 200 && data.success) {
				toast.success(`선택된 상품들을 삭제했습니다.`);

				await makeData();
			} else {
				const message = data.data.message.replace(/\\n/g, '\n');
				await executeMessage(message);
			}
		} catch (e) {
			await handleCatch(e);
		}
	}

	/**
	 * QAID 자동 생성(현재 사용 안 함)
	 *
	 * @param event
	 */
	async function createQaid(event: Event) {
		event.preventDefault();

		try {
			const endpoint = `/api/wms/qaid/new`;
			const { status, data } = await authClient.get(endpoint);

			if (status === 200 && data.success) {
				modalProductQaid = data.data.qaid;
			} else {
				await executeMessage('QAID 생성에 실패했습니다.\n프로그래머에게 문의 해 주세요.', 'error');
			}
		} catch (error) {
			await executeMessage(
				'QAID 생성에 문제가 있습니다.\n프로그래머에게 문의 해 주세요: ' + error,
				'error'
			);
		}
	}

	/**
	 * 미등록 상품 등록시 모달창
	 */
	async function createModalUnlinked() {
		modalTypeText = '등록';

		modalProductId = '';
		modalProductQaid = '';
		modalProductName = '';
		modalProductBarcode = '';
		modalProductAmount = 0;
		modalProductCate4 = '';
		modalProductCate5 = '';
		modalProductVendor = '';
		modalProductMemo = '';
		modalProductStatusSelected = '10';

		modal.showModal();
	}

	/**
	 * 미등록 상품 수정시 모달창
	 * @param id
	 */
	async function updateModalUnlinked(id: number) {
		let product = null;
		if ($productStore.products) {
			product = $productStore.products.find((product) => product.id === id);
		}

		modalTypeText = '수정';

		modalProductId = product.id;
		modalProductQaid = product.qaid;
		modalProductName = product.name;
		modalProductBarcode = product.barcode;
		modalProductAmount = product.amount;
		modalProductVendor = product.vendor.name;
		modalProductMemo = product.memo.content;
		modalProductStatusSelected = product.status.toString();

		// 프로그램적으로 select box의 값을 change 한 것과 같은 효과를 준다.
		const cate4El = document.getElementById('product_cate4') as HTMLSelectElement;
		cate4El.value = product.cate4_id;
		cate4El.dispatchEvent(new Event('change'));
		await tick(); // Svelte 가 DOM 업데이트를 완료할 때까지 기다립니다

		const cate5El = document.getElementById('product_cate5') as HTMLSelectElement;
		cate5El.value = product.cate5_id;
		cate5El.dispatchEvent(new Event('change'));
		await tick();

		modal.showModal();
	}

	/**
	 * 미등록 상품 등록
	 */
	async function handleStore() {
		if (!modalProductQaid) {
			await executeMessage('QAID를 입력해 주세요.', 'error');
			return false;
		}

		if (!modalProductName) {
			await executeMessage('상품 이름을 입력해 주세요.', 'error');
			return false;
		}

		if (!modalProductBarcode) {
			await executeMessage('바코드를 입력해 주세요.', 'error');
			return false;
		}

		if (!modalProductAmount) {
			await executeMessage('단가를 입력해 주세요.', 'error');
			return false;
		}

		if (!modalProductCate4) {
			await executeMessage('4차 카테고리를 선택해 주세요.', 'error');
			return false;
		}

		if (!modalProductCate5) {
			await executeMessage('5차 카테고리를 선택해 주세요.', 'error');
			return false;
		}

		if (!modalProductVendor) {
			await executeMessage('공급처를 입력해 주세요.', 'error');
			return false;
		}

		try {
			const payload = {
				productId: modalProductId,
				productQaid: modalProductQaid,
				productName: modalProductName,
				productBarcode: modalProductBarcode,
				productAmount: modalProductAmount,
				productCate4: modalProductCate4,
				productCate5: modalProductCate5,
				productVendor: modalProductVendor,
				productMemo: modalProductMemo,
				productStatus: '10'
			};

			let result;
			if (modalProductId) {
				payload.productStatus = modalProductStatusSelected;

				result = await authClient.put(apiUrl, payload);
			} else {
				result = await authClient.post(apiUrl, payload);
			}

			if (result.status === 200 && result.data.success) {
				await executeMessage(`${modalTypeText} 되었습니다.`);
				await makeData();
			} else {
				await executeMessage(result.data.data.message, 'error');
			}
		} catch (error) {
			await executeMessage(
				'미등록 상품등록에 문제가 있습니다.\n프로그래머에게 문의 해 주세요: ' + error,
				'error'
			);
		}
	}

	onMount(async () => {
		productStore.set({});
		await makeData();

		modal = document.getElementById('my_modal_1') as HTMLDialogElement;
	});

	const breadcrumbs: Breadcrumb[] = [
		{ title: '입고', url: '/works' },
		{ title: '미등록 상품목록', url: '/works/unlinked' }
	];
</script>

<svelte:head>
	<title>입고 > 미등록 상품목록</title>
</svelte:head>

{#if isLoading}
	<Loading size="60" unit="px" />
{/if}

<AppLayout {user}>
	{#snippet main()}
		<div>
			<TitleBar {breadcrumbs} />

			<section class="main-section">
				<SearchUI>
					<SearchProductStatus onUpdate={changeSearchParams} productStatusGroup={productStatus} />
					<SearchProcessGrade onUpdate={changeSearchParams} processGradeGroup={processCd} />

					{#if $productStore.cate4}
						<SearchCategory
							category={$productStore.cate4}
							cate4Selected={cate4}
							cate5Selected={cate5}
							onUpdate={changeSearchParams}
						/>
					{/if}

					<SearchQaid {keyword} onUpdate={changeSearchParams} {searchType}>
						<ButtonReset {localUrl} useTooltip={true} />
						<ButtonExcelDownload
							onclick={async (e) => {
								e.preventDefault();
								isLoading = true;

								const payload = getPayload(apiSearchParams);
								payload.type = 'unlinked';

								await handleDownload(EXCEL_DOWNLOAD_URL, payload);
								isLoading = false;
							}}
							useTooltip={true}
						/>
					</SearchQaid>

					<DisplayKeyword display_keyword1={display_keyword} />
				</SearchUI>

				<!-- 리스트 시작 -->
				<div class="px-2">
					<TableTop onUpdate={changeSearchParams} {pageSize} total={$productStore.pageTotal ?? 0}>
						{#snippet left()}
							<div class="pl-3">
								<ButtonSelectedDelete
									onclick={async (event: Event) => await handleDeleteProduct(event)}
									useTooltip={true}
								/>
							</div>
						{/snippet}

						{#snippet right()}
							<div class="pl-3">
								<button class="btn btn-primary btn-sm" onclick={createModalUnlinked} type="button">
									<Icon class="w-4 h-4 mr-2" data={faPlus} />
									미등록 상품 추가
								</button>
							</div>
						{/snippet}
					</TableTop>

					{#snippet tableHeader()}
						<tr class="bg-base-content text-base-300 text-center">
							<th class="p-0.5">
								<input
									checked={allChecked}
									onchange={() => toggleAllCheck($productStore.products)}
									type="checkbox"
								/>
							</th>
							<th class="p-0.5">번호</th>
							<th class="p-0.5">카테고리</th>
							<th class="p-0.5">QAID</th>
							<th class="p-0.5">
								상품명<br />
								바코드
							</th>
							<th class="p-0.5">중복여부</th>
							<th class="p-0.5">단가</th>
							<th class="p-0.5">검수상태</th>
							<th class="p-0.5">입고검수일자</th>
							<th class="p-0.5">컨디션</th>
							<th class="p-0.5">점검(수리)일자</th>
							<th class="p-0.5">출고일자</th>
						</tr>
					{/snippet}

					<table class="table text-xs table-pin-rows table-zebra">
						<thead class="uppercase">
							{@render tableHeader()}
						</thead>

						<tfoot class="uppercase">
							{@render tableHeader()}
						</tfoot>

						<tbody>
							{#if $productStore.products}
								{#each $productStore.products as item, index}
									<tr class="hover:bg-base-content/10">
										<td class="w-[20px] min-w-[20px] max-w-[20px] p-0.5 text-center">
											<input
												bind:checked={idChecked[index]}
												bind:group={ids}
												value={item.id}
												type="checkbox"
											/>
										</td>
										<td class="w-[50px] min-w-[50px] max-w-[50px] p-0.5 text-center">
											{getNumberFormat(startNo - index)}
										</td>
										<td class="w-[180px] min-w-[120px] max-w-[180px] p-0.5 text-center">
											<div class="flex items-center">
												<div class="w-1/2 p-0">{item.cate4.name}</div>
												{#if item.cate5}
													<div class="w-1/2 p-0">{item.cate5.name}</div>
												{/if}
											</div>
										</td>
										<td class="w-[100px] min-w-[100px] max-w-[120px] p-0.5 text-center">
											{item.qaid}
										</td>
										<td class="p-0.5">
											<p
												class="cursor-pointer"
												onclick={() => updateModalUnlinked(item.id)}
												role="presentation"
											>
												{item.name}
												<Icon data={faPenToSquare} scale={0.7} />
											</p>
											<p class="text-xs text-base-content/50">{item.barcode}</p>
										</td>
										<td class="w-[30px] min-w-[30px] max-w-[30px] p-0.5 text-center">
											{#if item.duplicated === 'N'}
												-
											{:else}
												중복
											{/if}
										</td>
										<td class="w-[60px] min-w-[60px] max-w-[60px] p-0.5 text-right">
											{getNumberFormat(item.amount)}
										</td>
										<td class="w-[76px] min-w-[76px] max-w-[76px] p-0.5 text-center">
											<p>{getProductCheckedStatusName(item.checked_status)}</p>
											{#if item.status === 50}
												<p class="text-xs text-error">반출중</p>
											{/if}
										</td>
										<td class="w-[90px] min-w-[86px] max-w-[90px] p-0.5 text-center">
											{#if item.checked_at}
												{formatDateTimeToFullString(item.checked_at)}
											{:else}
												-
											{/if}
										</td>
										<td class="w-[50px] min-w-[50px] max-w-[50px] p-0.5 text-center">
											{@html getProcessGradeColorButton(item)}
										</td>
										<td class="w-[90px] min-w-[86px] max-w-[90px] p-0.5 text-center">
											{#if item.pallet_products.length > 0}
												{formatDateTimeToFullString(item.pallet_products[0].registered_at)}
											{:else}
												-
											{/if}
										</td>
										<td class="w-[90px] min-w-[86px] max-w-[90px] p-0.5 text-center">
											{#if item.pallet_products.length > 0}
												<p class="badge badge-info badge-sm">
													<a href="/pallets/products?id={item.pallet_products[0].pallet_id}">
														{getPalletNo(item.pallet_products[0].pallet.location)}
													</a>
												</p>
												<p>{formatDateTimeToString(item.pallet_products[0].pallet.exported_at)}</p>
											{:else}
												-
											{/if}
										</td>
									</tr>
								{/each}
							{/if}
						</tbody>
					</table>
				</div>

				<!-- Pagination -->
				{#if $productStore.pageTotal && $productStore.pageTotal > 0}
					<Paginate
						store={productStore}
						{localUrl}
						onPageChange={handlePageChange}
						{searchParams}
					/>
				{/if}
			</section>

			<dialog class="modal" id="my_modal_1">
				<div class="modal-box w-1/2 max-w-2xl">
					<form method="dialog">
						<button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button>
					</form>

					<h3 class="font-bold text-lg">미등록 상품 {modalTypeText}</h3>

					<div class="mt-2">
						<label class="label text-sm font-bold text-red-700" for="product_qaid">QAID</label>
						<input
							bind:value={modalProductQaid}
							class="w-full max-w-48 py-0 border-0 border-b-2 border-b-neutral-300 focus:ring-0 focus:outline-none"
							id="product_qaid"
							maxlength="12"
							minlength="8"
							type="text"
						/>

						<!--						<button class="btn btn-primary btn-sm"-->
						<!--										onclick={createQaid}-->
						<!--										type="button"-->
						<!--						>-->
						<!--							<Icon data={faBolt} />-->
						<!--							QAID 생성-->
						<!--						</button>-->
					</div>

					<div class="mt-2">
						<label class="label text-sm font-bold" for="product_name">제품명</label>
						<input
							bind:value={modalProductName}
							class="w-full py-0 border-0 border-b-2 border-b-neutral-300 focus:ring-0 focus:outline-none"
							type="text"
						/>
					</div>

					<div class="mt-4 grid grid-cols-2 gap-4">
						<div>
							<label class="label text-sm font-bold" for="product_barcode">바코드</label>
							<input
								bind:value={modalProductBarcode}
								class="w-full py-0 border-0 border-b-2 border-b-neutral-300 focus:ring-0 focus:outline-none"
								id="product_barcode"
								type="text"
							/>
						</div>
						<div>
							<label class="label text-sm font-bold" for="product_amount">단가</label>
							<input
								bind:value={modalProductAmount}
								class="w-full py-0 border-0 border-b-2 border-b-neutral-300 focus:ring-0 focus:outline-none"
								id="product_amount"
								type="text"
							/>
						</div>
					</div>

					{#if $productStore.cate4}
						<div class="mt-2">
							<div>
								<label class="label text-sm font-bold" for="product_cate4">카테고리</label>
								<select
									bind:value={modalProductCate4}
									class="select select-bordered py-1 px-8 border-none focus:ring-0 focus:outline-none"
									id="product_cate4"
									onchange={() => (modalProductCate5 = '')}
								>
									<option value="">4차 카테고리</option>
									{#each $productStore.cate4 as item (item.id)}
										<option value={String(item.id)}>{item.name}</option>
									{/each}
								</select>

								<Icon data={faArrowRight} />

								{#if modalProductCate4 && $productStore.cate4.find((item) => String(item.id) === modalProductCate4)}
									<select
										bind:value={modalProductCate5}
										class="select select-bordered py-1 px-8 border-none focus:ring-0 focus:outline-none"
										id="product_cate5"
									>
										<option value="">5차 카테고리</option>
										{#each $productStore.cate4.find((item) => String(item.id) === modalProductCate4).cate5 as item}
											<option value={String(item.id)}>{item.name}</option>
										{/each}
									</select>
								{:else}
									<select
										bind:value={modalProductCate5}
										class="select select-bordered py-1 px-8 border-none focus:ring-0 focus:outline-none"
										id="product_cate5"
									>
										<option value="">5차 카테고리</option>
									</select>
								{/if}
							</div>
						</div>
					{/if}

					<div class="mt-2">
						<label class="label text-sm font-bold" for="product_vendor">공급처</label>
						<input
							bind:value={modalProductVendor}
							class="w-full py-0 border-0 border-b-2 border-b-neutral-300 focus:ring-0 focus:outline-none"
							type="text"
						/>
					</div>

					<div class="mt-2">
						<label class="label text-sm font-bold" for="product_memo">메모</label>
						<textarea
							bind:value={modalProductMemo}
							class="w-full h-24 py-0 border-0 border-b-2 border-b-neutral-300 focus:ring-0 focus:outline-none"
							id="product_memo"
						></textarea>
					</div>

					<div class="mt-2">
						<label class="label text-sm font-bold" for="product_status">상태</label>
						<select
							bind:value={modalProductStatusSelected}
							class="select select-bordered py-1 px-8 border-none focus:ring-0 focus:outline-none"
							disabled={modalTypeText === '등록'}
						>
							<option value="10">수리대기중(창고)</option>
							<option value="50">반출중</option>
							<option value="30">점검완료</option>
							<option value="70">출고완료</option>
							<option value="80">출고보류</option>
							<option value="90">삭제</option>
						</select>
					</div>

					<div class="modal-action flex justify-between">
						<div>
							<button
								class="btn btn-primary"
								onclick={async (e) => {
									e.preventDefault();

									await handleStore();
								}}
								type="submit"
							>
								<Icon class="w-5 h-5" data={faSave} />
								상품{modalTypeText}
							</button>
						</div>
						<div>
							<form method="dialog">
								<!-- if there is a button in form, it will close the modal -->
								<button
									class="btn btn-warning tooltip tooltip-left"
									data-tip="키보드의 Escape 키를 누르면 닫힙니다."
								>
									<Icon class="w-5 h-5" data={faXmark} />
									닫기
								</button>
							</form>
						</div>
					</div>
				</div>
			</dialog>
		</div>
	{/snippet}
</AppLayout>
