<script lang="ts">
	import { onDestroy, onMount } from 'svelte';
	import { browser } from '$app/environment';
	import { page } from '$app/state';

	import { getCurrentWindow } from '@tauri-apps/api/window';
	import { formatDateTimeToString, formatDateToString } from '$lib/Functions.js';

	import Layout from '$components/Layouts/PrintLayout.svelte';

	let mounted = $state(false);
	let isPrinting = $state(false);

	const queryParams = page.url.searchParams;

	let pallet_code_list = queryParams.get('pallet_code_list');
	let export_date = queryParams.get('export_date') ?? null;
	console.log('pallet_code_list', pallet_code_list);
	console.log('export_date', export_date);

	let exportDate = $state('');
	if (export_date === null) {
		exportDate = formatDateToString(new Date());
	} else {
		exportDate = formatDateTimeToString(export_date);
	}
	console.log('exportDate', exportDate);

	let pallet_code_arr: any[] = $state([]);
	let count_pallet_code = $state(0);
	if (pallet_code_list !== null) {
		pallet_code_arr = pallet_code_list.split('|');
		count_pallet_code = pallet_code_arr.length;
	}

	function handleBeforePrint() {
		isPrinting = true;
	}

	function handleAfterPrint() {
		isPrinting = false;
		closeWindow();
	}

	async function closeWindow() {
		console.log('Closing window...', isPrinting);

		if (!isPrinting) {
			setTimeout(async () => {
				try {
					const currentWindow = getCurrentWindow();
					await currentWindow.close();
				} catch (error) {
					console.error('Error closing window (tauri):', error);
					// 웹 환경 폴백: Tauri API 사용 불가 시 브라우저 창 닫기 시도
					try {
						window.close();
					} catch (e) {
						console.error('Error closing window (browser):', e);
					}
				}
			}, 2000); // 2초 후에 창 닫기
		}
	}

	onMount(() => {
		mounted = true;

		if (browser) {
			setTimeout(() => {
				window.print();
			}, 500);
		}

		window.addEventListener('beforeprint', handleBeforePrint);
		window.addEventListener('afterprint', handleAfterPrint);
	});

	onDestroy(() => {
		window.removeEventListener('beforeprint', handleBeforePrint);
		window.removeEventListener('afterprint', handleAfterPrint);
	});
</script>

<svelte:head>
	<title>출고 팔레트 목로</title>
</svelte:head>

{#if mounted}
	<style>
		th {
			padding: 10px;
			background-color: #cccccc;
			border: solid 1px #cccccc;
		}

		td {
			padding: 10px;
			border: solid 1px #cccccc;
		}

		@media print {
			@page {
				size: 210mm 297mm;
				margin: 0;
			}

			body {
				width: 210mm;
				height: 297mm;
			}

			.printable {
				width: 100%;
				height: 100%;
			}

			.printable * {
				visibility: visible;
			}

			body * {
				visibility: hidden;
			}

			.barcode-container {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 100%;
			}
		}
	</style>

	<Layout>
		<div class="printable" style="width: 100%; height: 100%; text-align: center;">
			<table style="width: 600px; margin: 0 auto;">
				<thead>
					<tr>
						<th>출고일</th>
						<th colspan="2">{exportDate}</th>
					</tr>
				</thead>
				<tbody>
					{#each pallet_code_arr as pallet_code, index}
						{#if index === 0}
							<tr>
								<td rowspan={count_pallet_code}>PLT번호</td>
								<td>{index + 1}</td>
								<td style="font-size:20px;">{pallet_code}</td>
							</tr>
						{:else}
							<tr>
								<td>{index + 1}</td>
								<td style="font-size:20px;">{pallet_code}</td>
							</tr>
						{/if}
					{/each}
				</tbody>
			</table>
		</div>
	</Layout>
{/if}
