/**
 * 테스트 환경 설정 파일
 * Vitest와 Svelte 컴포넌트 테스트를 위한 설정
 */

import { vi } from 'vitest';

// DOM 환경 설정
Object.defineProperty(window, 'matchMedia', {
	writable: true,
	value: vi.fn().mockImplementation((query) => ({
		matches: false,
		media: query,
		onchange: null,
		addListener: vi.fn(), // deprecated
		removeListener: vi.fn(), // deprecated
		addEventListener: vi.fn(),
		removeEventListener: vi.fn(),
		dispatchEvent: vi.fn()
	}))
});

// ResizeObserver 모의
global.ResizeObserver = vi.fn().mockImplementation(() => ({
	observe: vi.fn(),
	unobserve: vi.fn(),
	disconnect: vi.fn()
}));

// IntersectionObserver 모의
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
	observe: vi.fn(),
	unobserve: vi.fn(),
	disconnect: vi.fn()
}));

// requestAnimationFrame 모의
global.requestAnimationFrame = vi.fn((cb) => setTimeout(cb, 16));
global.cancelAnimationFrame = vi.fn((id) => clearTimeout(id));

// 브라우저 API 모의
Object.defineProperty(window, 'location', {
	value: {
		href: 'http://localhost:3000',
		origin: 'http://localhost:3000',
		protocol: 'http:',
		host: 'localhost:3000',
		hostname: 'localhost',
		port: '3000',
		pathname: '/',
		search: '',
		hash: ''
	},
	writable: true
});

// 콘솔 경고 억제 (테스트 중 불필요한 경고 메시지 제거)
const originalWarn = console.warn;
console.warn = (...args) => {
	// Svelte 관련 경고는 억제
	if (args[0]?.includes?.('lifecycle_function_unavailable')) {
		return;
	}
	originalWarn(...args);
};
