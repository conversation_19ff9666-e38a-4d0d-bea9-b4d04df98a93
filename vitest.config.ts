import { defineConfig } from 'vitest/config';
import { svelte } from '@sveltejs/vite-plugin-svelte';
import path from 'path';

export default defineConfig({
	plugins: [svelte({ hot: !process.env.VITEST })],
	resolve: {
		alias: {
			$src: path.resolve('./src'),
			$lib: path.resolve('./src/lib'),
			$components: path.resolve('./src/lib/components'),
			$stores: path.resolve('./src/lib/stores'),
			'$app/environment': path.resolve('./src/__mocks__/$app/environment.ts'),
			'$app/navigation': path.resolve('./src/__mocks__/$app/navigation.ts'),
			'$app/stores': path.resolve('./src/__mocks__/$app/stores.ts')
		}
	},
	test: {
		environment: 'jsdom',
		globals: true,
		include: ['src/**/*.{test,spec}.{js,ts,svelte}'],
		setupFiles: ['./src/test-setup.ts']
	}
});
